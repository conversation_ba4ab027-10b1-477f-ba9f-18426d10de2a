{"name": "@types/react", "version": "18.2.14", "description": "TypeScript definitions for React", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "url": "https://asana.com"}, {"name": "AssureSign", "url": "http://www.assuresign.com"}, {"name": "Microsoft", "url": "https://microsoft.com"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/bbenezech", "githubUsername": "bbenezech"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pza<PERSON><PERSON>ky", "githubUsername": "pza<PERSON>linsky"}, {"name": "<PERSON>", "url": "https://github.com/ericanderson", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/theruther4d", "githubUsername": "theruther4d"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/guil<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/ferdaber", "githubUsername": "ferd<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/jrakotoharisoa", "githubUsername": "jrakotoharisoa"}, {"name": "<PERSON>", "url": "https://github.com/pascaloliv", "githubUsername": "pascal<PERSON>v"}, {"name": "<PERSON>", "url": "https://github.com/hotell", "githubUsername": "hotell"}, {"name": "<PERSON>", "url": "https://github.com/franklixuefei", "githubUsername": "frank<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/Jessidhia", "githubUsername": "Jessidhia"}, {"name": "Saransh Kataria", "url": "https://github.com/saranshkataria", "githubUsername": "saranshkataria"}, {"name": "Kanitkorn Sujautra", "url": "https://github.com/lukyth", "githubUsername": "luk<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/eps1lon", "githubUsername": "eps1lon"}, {"name": "<PERSON>", "url": "https://github.com/zieka", "githubUsername": "zieka"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/dancerphil", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/dimitrop<PERSON>los", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/disjukr", "githubUsername": "disju<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/vhfmag", "githubUsername": "vhfmag"}, {"name": "<PERSON>", "url": "https://github.com/hellatan", "githubUsername": "hellatan"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/priyanshurav", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/Semigradsky", "githubUsername": "Semigradsky"}], "main": "", "types": "index.d.ts", "typesVersions": {"<=5.0": {"*": ["ts5.0/*"]}}, "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/react"}, "scripts": {}, "dependencies": {"@types/prop-types": "*", "@types/scheduler": "*", "csstype": "^3.0.2"}, "typesPublisherContentHash": "d8d0f4cd1e77ba9e083f3e9796e4f1dda2e0c60173c7cda3bd921155bbb57f8f", "typeScriptVersion": "4.3", "exports": {".": {"types@<=5.0": {"default": "./ts5.0/index.d.ts"}, "types": {"default": "./index.d.ts"}}, "./canary": {"types@<=5.0": {"default": "./ts5.0/canary.d.ts"}, "types": {"default": "./canary.d.ts"}}, "./experimental": {"types@<=5.0": {"default": "./ts5.0/experimental.d.ts"}, "types": {"default": "./experimental.d.ts"}}, "./jsx-runtime": {"types@<=5.0": {"default": "./ts5.0/jsx-runtime.d.ts"}, "types": {"default": "./jsx-runtime.d.ts"}}, "./jsx-dev-runtime": {"types@<=5.0": {"default": "./ts5.0/jsx-dev-runtime.d.ts"}, "types": {"default": "./jsx-dev-runtime.d.ts"}}, "./package.json": "./package.json"}}