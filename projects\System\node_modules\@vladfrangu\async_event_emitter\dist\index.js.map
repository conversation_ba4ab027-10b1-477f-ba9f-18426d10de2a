{"version": 3, "sources": ["../src/index.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/dot-notation */\nfunction validateListener(input: unknown): asserts input is (...args: unknown[]) => Awaitable<void> {\n\tif (typeof input !== 'function') {\n\t\tthrow new TypeError(`The listener argument must be a function. Received ${typeof input}`);\n\t}\n}\n\nfunction validateAbortSignal(input: unknown): asserts input is AbortSignal | undefined {\n\t// Only validate that the signal is a signal if its defined\n\tif (input && !(input instanceof AbortSignal)) {\n\t\tthrow new TypeError(`The signal option must be an AbortSignal. Received ${input}`);\n\t}\n}\n\n// Inspired from https://github.com/nodejs/node/blob/42ad967d68137df1a80a877e7b5ad56403fc157f/lib/internal/util.js#L397\nfunction spliceOne(list: unknown[], index: number) {\n\tfor (; index + 1 < list.length; index++) {\n\t\tlist[index] = list[index + 1];\n\t}\n\n\tlist.pop();\n}\n\n// Inspired from https://github.com/nodejs/node/blob/42ad967d68137df1a80a877e7b5ad56403fc157f/lib/events.js#L889\nfunction arrayClone<T extends unknown[]>(arr: T): T {\n\t// At least since V8 8.3, this implementation is faster than the previous\n\t// which always used a simple for-loop\n\tswitch (arr.length) {\n\t\tcase 2:\n\t\t\treturn [arr[0], arr[1]] as T;\n\t\tcase 3:\n\t\t\treturn [arr[0], arr[1], arr[2]] as T;\n\t\tcase 4:\n\t\t\treturn [arr[0], arr[1], arr[2], arr[3]] as T;\n\t\tcase 5:\n\t\t\treturn [arr[0], arr[1], arr[2], arr[3], arr[4]] as T;\n\t\tcase 6:\n\t\t\treturn [arr[0], arr[1], arr[2], arr[3], arr[4], arr[5]] as T;\n\t}\n\n\treturn arr.slice() as T;\n}\n\n// Inspired from https://github.com/nodejs/node/blob/42ad967d68137df1a80a877e7b5ad56403fc157f/lib/events.js#L427-L475\nfunction identicalSequenceRange(a: unknown[], b: unknown[]): [number, number] {\n\tfor (let i = 0; i < a.length - 3; i++) {\n\t\t// Find the first entry of b that matches the current entry of a.\n\t\tconst pos = b.indexOf(a[i]);\n\t\tif (pos !== -1) {\n\t\t\tconst rest = b.length - pos;\n\t\t\tif (rest > 3) {\n\t\t\t\tlet len = 1;\n\t\t\t\tconst maxLen = Math.min(a.length - i, rest);\n\t\t\t\t// Count the number of consecutive entries.\n\t\t\t\twhile (maxLen > len && a[i + len] === b[pos + len]) {\n\t\t\t\t\tlen++;\n\t\t\t\t}\n\t\t\t\tif (len > 3) {\n\t\t\t\t\treturn [len, i];\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\treturn [0, 0];\n}\n\nfunction enhanceStackTrace(this: AsyncEventEmitter<any>, err: Error, own: Error) {\n\tlet ctorInfo = '';\n\ttry {\n\t\tconst { name } = this.constructor;\n\t\tif (name !== 'AsyncEventEmitter') ctorInfo = ` on ${name} instance`;\n\t} catch {\n\t\t// Continue regardless of error.\n\t}\n\tconst sep = `\\nEmitted 'error' event${ctorInfo} at:\\n`;\n\n\tconst errStack = err.stack!.split('\\n').slice(1);\n\tconst ownStack = own.stack!.split('\\n').slice(1);\n\n\tconst { 0: len, 1: off } = identicalSequenceRange(ownStack, errStack);\n\tif (len > 0) {\n\t\townStack.splice(off + 1, len - 2, '    [... lines matching original stack trace ...]');\n\t}\n\n\treturn err.stack + sep + ownStack.join('\\n');\n}\n\ninterface InternalEventMap extends Array<Listener | WrappedOnce> {\n\t_hasWarnedAboutMaxListeners?: boolean;\n}\n\nexport class AsyncEventEmitter<Events extends Record<PropertyKey, unknown[]> = Record<PropertyKey, unknown[]> & AsyncEventEmitterPredefinedEvents> {\n\tprivate _events: Record<keyof Events | keyof AsyncEventEmitterPredefinedEvents, Listener | WrappedOnce | InternalEventMap> = {\n\t\t__proto__: null\n\t} as Record<keyof Events | keyof AsyncEventEmitterPredefinedEvents, Listener | WrappedOnce | InternalEventMap>;\n\n\tprivate _eventCount = 0;\n\tprivate _maxListeners = 10;\n\tprivate _internalPromiseMap: Map<string, Promise<void>> = new Map();\n\tprivate _wrapperId = 0n;\n\n\tpublic addListener<K extends keyof Events | keyof AsyncEventEmitterPredefinedEvents>(\n\t\teventName: K,\n\t\tlistener: (...args: K extends keyof AsyncEventEmitterPredefinedEvents ? AsyncEventEmitterPredefinedEvents[K] : Events[K]) => void\n\t): this {\n\t\tvalidateListener(listener);\n\n\t\tconst wrapped = this._wrapListener(eventName, listener, false);\n\n\t\tthis._addListener(eventName, wrapped, false);\n\n\t\treturn this;\n\t}\n\n\tpublic on<K extends keyof Events | keyof AsyncEventEmitterPredefinedEvents>(\n\t\teventName: K,\n\t\tlistener: (...args: K extends keyof AsyncEventEmitterPredefinedEvents ? AsyncEventEmitterPredefinedEvents[K] : Events[K]) => void\n\t): this {\n\t\treturn this.addListener(eventName, listener);\n\t}\n\n\tpublic once<K extends keyof Events | keyof AsyncEventEmitterPredefinedEvents>(\n\t\teventName: K,\n\t\tlistener: (...args: K extends keyof AsyncEventEmitterPredefinedEvents ? AsyncEventEmitterPredefinedEvents[K] : Events[K]) => void\n\t): this {\n\t\tvalidateListener(listener);\n\n\t\tconst wrapped = this._wrapListener(eventName, listener, true);\n\n\t\tthis._addListener(eventName, wrapped, false);\n\n\t\treturn this;\n\t}\n\n\tpublic removeListener<K extends keyof Events | keyof AsyncEventEmitterPredefinedEvents>(\n\t\teventName: K,\n\t\tlistener: (...args: K extends keyof AsyncEventEmitterPredefinedEvents ? AsyncEventEmitterPredefinedEvents[K] : Events[K]) => void\n\t): this {\n\t\tvalidateListener(listener);\n\n\t\tconst events = this._events;\n\t\tconst eventList = events[eventName];\n\n\t\tif (eventList === undefined) {\n\t\t\treturn this;\n\t\t}\n\n\t\tif (eventList === listener || (eventList as WrappedOnce).listener === listener) {\n\t\t\tif (--this._eventCount === 0) {\n\t\t\t\tthis._events = { __proto__: null } as Record<\n\t\t\t\t\tkeyof Events | keyof AsyncEventEmitterPredefinedEvents,\n\t\t\t\t\tListener | WrappedOnce | InternalEventMap\n\t\t\t\t>;\n\t\t\t} else {\n\t\t\t\tdelete events[eventName];\n\t\t\t\tif (events.removeListener) {\n\t\t\t\t\tthis.emit('removeListener', eventName as string, (eventList as WrappedOnce).listener ?? eventList);\n\t\t\t\t}\n\t\t\t}\n\t\t} else if (typeof eventList !== 'function') {\n\t\t\tlet position = -1;\n\n\t\t\tfor (let i = eventList.length - 1; i >= 0; i--) {\n\t\t\t\tif (eventList[i] === listener || (eventList[i] as WrappedOnce).listener === listener) {\n\t\t\t\t\tposition = i;\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (position < 0) {\n\t\t\t\treturn this;\n\t\t\t}\n\n\t\t\tif (position === 0) {\n\t\t\t\teventList.shift();\n\t\t\t} else {\n\t\t\t\tspliceOne(eventList, position);\n\t\t\t}\n\n\t\t\tif (eventList.length === 0) {\n\t\t\t\tdelete events[eventName];\n\t\t\t\t--this._eventCount;\n\t\t\t}\n\n\t\t\tif (events.removeListener !== undefined) {\n\t\t\t\t// Thanks TypeScript for the cast...\n\t\t\t\tthis.emit('removeListener', eventName as string | symbol, listener);\n\t\t\t}\n\t\t}\n\n\t\treturn this;\n\t}\n\n\tpublic off<K extends keyof Events | keyof AsyncEventEmitterPredefinedEvents>(\n\t\teventName: K,\n\t\tlistener: (...args: K extends keyof AsyncEventEmitterPredefinedEvents ? AsyncEventEmitterPredefinedEvents[K] : Events[K]) => void\n\t): this {\n\t\treturn this.removeListener(eventName, listener);\n\t}\n\n\tpublic removeAllListeners<K extends keyof Events | keyof AsyncEventEmitterPredefinedEvents>(event?: K | undefined): this {\n\t\tconst events = this._events;\n\n\t\t// Not listening for removeListener, no need to emit\n\t\tif (events.removeListener === undefined) {\n\t\t\tif (!event) {\n\t\t\t\tthis._events = { __proto__: null } as Record<keyof Events | keyof AsyncEventEmitterPredefinedEvents, InternalEventMap>;\n\t\t\t\tthis._eventCount = 0;\n\t\t\t} else if (events[event] !== undefined) {\n\t\t\t\tif (--this._eventCount === 0) {\n\t\t\t\t\tthis._events = { __proto__: null } as Record<keyof Events | keyof AsyncEventEmitterPredefinedEvents, InternalEventMap>;\n\t\t\t\t} else {\n\t\t\t\t\tdelete events[event];\n\t\t\t\t}\n\t\t\t}\n\n\t\t\treturn this;\n\t\t}\n\n\t\t// Emit removeListener for all listeners on all events\n\t\tif (!event) {\n\t\t\tfor (const key of Reflect.ownKeys(events)) {\n\t\t\t\tif (key === 'removeListener') {\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\tthis.removeAllListeners(key);\n\t\t\t}\n\n\t\t\tthis.removeAllListeners('removeListener');\n\t\t\tthis._events = { __proto__: null } as Record<keyof Events | keyof AsyncEventEmitterPredefinedEvents, InternalEventMap>;\n\t\t\tthis._eventCount = 0;\n\n\t\t\treturn this;\n\t\t}\n\n\t\tconst listeners = events[event];\n\n\t\tif (typeof listeners === 'function') {\n\t\t\tthis.removeListener(event, listeners);\n\t\t} else if (listeners !== undefined) {\n\t\t\t// LIFO order\n\t\t\tfor (let i = listeners.length - 1; i >= 0; i--) {\n\t\t\t\tthis.removeListener(event, listeners[i]);\n\t\t\t}\n\t\t}\n\n\t\treturn this;\n\t}\n\n\tpublic setMaxListeners(n: number): this {\n\t\tif (typeof n !== 'number' || n < 0 || Number.isNaN(n)) {\n\t\t\tthrow new RangeError(`Expected to get a non-negative number for \"setMaxListeners\", got ${n} instead`);\n\t\t}\n\n\t\tthis._maxListeners = n;\n\n\t\treturn this;\n\t}\n\n\tpublic getMaxListeners(): number {\n\t\treturn this._maxListeners;\n\t}\n\n\tpublic listeners<K extends keyof Events | keyof AsyncEventEmitterPredefinedEvents>(eventName: K): Listener<Events[keyof Events]>['listener'][] {\n\t\tconst eventList = this._events[eventName];\n\n\t\tif (eventList === undefined) {\n\t\t\treturn [];\n\t\t}\n\n\t\tif (typeof eventList === 'function') {\n\t\t\treturn [eventList.listener ?? eventList];\n\t\t}\n\n\t\tconst ret = arrayClone(eventList) as Listener<Events[keyof Events]>['listener'][];\n\n\t\tfor (let i = 0; i < ret.length; ++i) {\n\t\t\tconst orig = (ret[i] as WrappedOnce).listener;\n\t\t\tif (typeof orig === 'function') {\n\t\t\t\tret[i] = orig;\n\t\t\t}\n\t\t}\n\n\t\treturn ret;\n\t}\n\n\tpublic rawListeners<K extends keyof Events | keyof AsyncEventEmitterPredefinedEvents>(eventName: K): Listener<Events[keyof Events]>[] {\n\t\tconst eventList = this._events[eventName];\n\n\t\tif (eventList === undefined) {\n\t\t\treturn [];\n\t\t}\n\n\t\tif (typeof eventList === 'function') {\n\t\t\treturn [eventList];\n\t\t}\n\n\t\treturn arrayClone(eventList) as Listener<Events[keyof Events]>[];\n\t}\n\n\tpublic emit<K extends keyof Events | keyof AsyncEventEmitterPredefinedEvents>(\n\t\teventName: K,\n\t\t...args: K extends keyof AsyncEventEmitterPredefinedEvents ? AsyncEventEmitterPredefinedEvents[K] : Events[K]\n\t): boolean {\n\t\tlet doError = eventName === 'error';\n\n\t\tconst events = this._events;\n\t\tif (events !== undefined) {\n\t\t\tdoError = doError && events.error === undefined;\n\t\t} else if (!doError) {\n\t\t\treturn false;\n\t\t}\n\n\t\tif (doError) {\n\t\t\tlet er: unknown;\n\n\t\t\tif (args.length > 0) {\n\t\t\t\t// eslint-disable-next-line prefer-destructuring\n\t\t\t\ter = args[0];\n\t\t\t}\n\n\t\t\tif (er instanceof Error) {\n\t\t\t\ttry {\n\t\t\t\t\tconst capture = {};\n\t\t\t\t\t// eslint-disable-next-line @typescript-eslint/unbound-method\n\t\t\t\t\tError.captureStackTrace(capture, AsyncEventEmitter.prototype.emit);\n\t\t\t\t\tObject.defineProperty(er, 'stack', {\n\t\t\t\t\t\tvalue: enhanceStackTrace.call(this, er, capture as Error),\n\t\t\t\t\t\tconfigurable: true\n\t\t\t\t\t});\n\t\t\t\t} catch {\n\t\t\t\t\t// Continue regardless of error\n\t\t\t\t}\n\n\t\t\t\tthrow er; // Unhandled 'error' event\n\t\t\t}\n\n\t\t\tconst stringifiedError = String(er);\n\n\t\t\t// Give some error to user\n\t\t\tconst err = new Error(`Unhandled 'error' event emitted, received ${stringifiedError}`);\n\t\t\t// @ts-expect-error Add context to error too\n\t\t\terr.context = er;\n\n\t\t\tthrow err; // Unhandled 'error' event\n\t\t}\n\n\t\tconst handlers = events[eventName];\n\n\t\tif (handlers === undefined) {\n\t\t\treturn false;\n\t\t}\n\n\t\tif (typeof handlers === 'function') {\n\t\t\tconst result = handlers.apply(this, args);\n\n\t\t\tif (result !== undefined && result !== null) {\n\t\t\t\thandleMaybeAsync(this, result);\n\t\t\t}\n\t\t} else {\n\t\t\tconst len = handlers.length;\n\t\t\tconst listeners = arrayClone(handlers as InternalEventMap);\n\n\t\t\tfor (let i = 0; i < len; ++i) {\n\t\t\t\t// We call all listeners regardless of the result, as we already handle possible error emits in the wrapped func\n\t\t\t\tconst result = listeners[i].apply(this, args);\n\n\t\t\t\tif (result !== undefined && result !== null) {\n\t\t\t\t\thandleMaybeAsync(this, result);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\treturn true;\n\t}\n\n\tpublic listenerCount<K extends keyof Events | keyof AsyncEventEmitterPredefinedEvents>(eventName: K): number {\n\t\tconst events = this._events;\n\n\t\tif (events === undefined) {\n\t\t\treturn 0;\n\t\t}\n\n\t\tconst eventListeners = events[eventName];\n\n\t\tif (typeof eventListeners === 'function') {\n\t\t\treturn 1;\n\t\t}\n\n\t\treturn eventListeners?.length ?? 0;\n\t}\n\n\tpublic prependListener<K extends keyof Events | keyof AsyncEventEmitterPredefinedEvents>(\n\t\teventName: K,\n\t\tlistener: (...args: K extends keyof AsyncEventEmitterPredefinedEvents ? AsyncEventEmitterPredefinedEvents[K] : Events[K]) => void\n\t): this {\n\t\tvalidateListener(listener);\n\n\t\tconst wrapped = this._wrapListener(eventName, listener, false);\n\n\t\tthis._addListener(eventName, wrapped, true);\n\n\t\treturn this;\n\t}\n\n\tpublic prependOnceListener<K extends keyof Events | keyof AsyncEventEmitterPredefinedEvents>(\n\t\teventName: K,\n\t\tlistener: (...args: K extends keyof AsyncEventEmitterPredefinedEvents ? AsyncEventEmitterPredefinedEvents[K] : Events[K]) => void\n\t): this {\n\t\tvalidateListener(listener);\n\n\t\tconst wrapped = this._wrapListener(eventName, listener, true);\n\n\t\tthis._addListener(eventName, wrapped, true);\n\n\t\treturn this;\n\t}\n\n\tpublic eventNames(): (keyof Events | keyof AsyncEventEmitterPredefinedEvents)[] {\n\t\treturn this._eventCount > 0 ? Reflect.ownKeys(this._events) : [];\n\t}\n\n\tpublic async waitForAllListenersToComplete() {\n\t\tconst promises = [...this._internalPromiseMap.values()];\n\n\t\tif (promises.length === 0) {\n\t\t\treturn false;\n\t\t}\n\n\t\tawait Promise.all(promises);\n\n\t\treturn true;\n\t}\n\n\tprivate _addListener<K extends keyof Events | keyof AsyncEventEmitterPredefinedEvents>(\n\t\teventName: K,\n\t\twrappedListener: Listener | WrappedOnce,\n\t\tprepend: boolean\n\t) {\n\t\t// Emit newListener first in the event someone is listening for it\n\t\tif (this._events.newListener !== undefined) {\n\t\t\t// Thanks TypeScript for the cast...\n\t\t\tthis.emit('newListener', eventName as string | symbol, (wrappedListener as WrappedOnce).listener ?? wrappedListener);\n\t\t}\n\n\t\tlet existing = this._events[eventName];\n\n\t\tif (existing === undefined) {\n\t\t\t// eslint-disable-next-line no-multi-assign\n\t\t\texisting = this._events[eventName] = wrappedListener;\n\t\t\t++this._eventCount;\n\t\t} else if (typeof existing === 'function') {\n\t\t\t// Adding the second element, need to change to array.\n\t\t\t// eslint-disable-next-line no-multi-assign\n\t\t\texisting = this._events[eventName] = prepend ? [wrappedListener, existing] : [existing, wrappedListener];\n\t\t\t// If we've already got an array, just append.\n\t\t} else if (prepend) {\n\t\t\texisting.unshift(wrappedListener);\n\t\t} else {\n\t\t\texisting.push(wrappedListener);\n\t\t}\n\n\t\tif (this._maxListeners > 0 && existing.length > this._maxListeners && !existing._hasWarnedAboutMaxListeners) {\n\t\t\texisting._hasWarnedAboutMaxListeners = true;\n\t\t\tconst warningMessage = [\n\t\t\t\t`Possible AsyncEventEmitter memory leak detected. ${existing.length} ${String(eventName)} listeners added to ${\n\t\t\t\t\tthis.constructor.name\n\t\t\t\t}.`,\n\t\t\t\t`Use emitter.setMaxListeners() to increase the limit.`\n\t\t\t].join(' ');\n\t\t\tconsole.warn(warningMessage);\n\t\t}\n\t}\n\n\tprivate _wrapListener<K extends keyof Events | keyof AsyncEventEmitterPredefinedEvents>(\n\t\teventName: K,\n\t\tlistener: (...args: K extends keyof AsyncEventEmitterPredefinedEvents ? AsyncEventEmitterPredefinedEvents[K] : Events[K]) => Awaitable<void>,\n\t\tonce: boolean\n\t): Listener | WrappedOnce {\n\t\tif (!once) {\n\t\t\treturn listener as Listener;\n\t\t}\n\n\t\tconst state = {\n\t\t\tfired: false,\n\t\t\twrapFn: undefined!,\n\t\t\teventEmitter: this,\n\t\t\teventName,\n\t\t\tlistener\n\t\t} as WrappedOnceState<K extends keyof AsyncEventEmitterPredefinedEvents ? AsyncEventEmitterPredefinedEvents[K] : Events[K]>;\n\n\t\tconst aliased = onceWrapper<K extends keyof AsyncEventEmitterPredefinedEvents ? AsyncEventEmitterPredefinedEvents[K] : Events[K]>;\n\n\t\tconst wrapped = aliased.bind(state) as WrappedOnce<\n\t\t\tK extends keyof AsyncEventEmitterPredefinedEvents ? AsyncEventEmitterPredefinedEvents[K] : Events[K]\n\t\t>;\n\t\twrapped.listener = listener;\n\t\tstate.wrapFn = wrapped;\n\n\t\treturn wrapped as WrappedOnce;\n\t}\n\n\tpublic static listenerCount<\n\t\tEmitter extends AsyncEventEmitter<any>,\n\t\tEventNames = Emitter extends AsyncEventEmitter<infer Events> ? Events : never,\n\t\tEventName extends PropertyKey = EventNames extends never ? string | symbol : keyof EventNames\n\t>(emitter: Emitter, eventName: EventName | keyof AsyncEventEmitterPredefinedEvents) {\n\t\treturn emitter.listenerCount(eventName);\n\t}\n\n\tpublic static async once<\n\t\tEmitter extends AsyncEventEmitter<any>,\n\t\tEventNames extends Record<PropertyKey, unknown[]> = Emitter extends AsyncEventEmitter<infer Events> ? Events : Record<PropertyKey, unknown[]>,\n\t\tEventName extends PropertyKey = keyof EventNames | keyof AsyncEventEmitterPredefinedEvents,\n\t\tEventResult extends unknown[] = EventName extends keyof AsyncEventEmitterPredefinedEvents\n\t\t\t? AsyncEventEmitterPredefinedEvents[EventName]\n\t\t\t: EventNames[EventName]\n\t>(emitter: Emitter, eventName: EventName, options: AbortableMethods = {}) {\n\t\tconst signal = options?.signal;\n\t\tvalidateAbortSignal(signal);\n\n\t\tif (signal?.aborted) {\n\t\t\tthrow new AbortError(undefined, { cause: getReason(signal) });\n\t\t}\n\n\t\treturn new Promise<EventResult>((resolve, reject) => {\n\t\t\tconst errorListener = (err: unknown) => {\n\t\t\t\temitter.removeListener(eventName, resolver);\n\n\t\t\t\tif (signal) {\n\t\t\t\t\teventTargetAgnosticRemoveListener(emitter, eventName, abortListener);\n\t\t\t\t}\n\n\t\t\t\treject(err);\n\t\t\t};\n\n\t\t\tconst resolver = (...args: unknown[]) => {\n\t\t\t\temitter.removeListener('error', errorListener);\n\n\t\t\t\tif (signal) {\n\t\t\t\t\teventTargetAgnosticRemoveListener(signal, 'abort', abortListener);\n\t\t\t\t}\n\n\t\t\t\tresolve(args as EventResult);\n\t\t\t};\n\n\t\t\temitter.once(eventName, resolver);\n\t\t\tif (eventName !== 'error') {\n\t\t\t\temitter.once('error', errorListener);\n\t\t\t}\n\n\t\t\tconst abortListener = () => {\n\t\t\t\teventTargetAgnosticRemoveListener(emitter, eventName, resolver);\n\t\t\t\teventTargetAgnosticRemoveListener(emitter, 'error', errorListener);\n\t\t\t\treject(new AbortError(undefined, { cause: getReason(signal) }));\n\t\t\t};\n\n\t\t\tif (signal) {\n\t\t\t\teventTargetAgnosticAddListener(signal, 'abort', abortListener, { once: true });\n\t\t\t}\n\t\t});\n\t}\n\n\tpublic static on<\n\t\tEmitter extends AsyncEventEmitter<any>,\n\t\tEventNames extends Record<PropertyKey, unknown[]> = Emitter extends AsyncEventEmitter<infer Events> ? Events : Record<PropertyKey, unknown[]>,\n\t\tEventName extends PropertyKey = keyof EventNames | keyof AsyncEventEmitterPredefinedEvents,\n\t\tEventResult extends unknown[] = EventName extends keyof AsyncEventEmitterPredefinedEvents\n\t\t\t? AsyncEventEmitterPredefinedEvents[EventName]\n\t\t\t: EventNames[EventName]\n\t>(emitter: Emitter, eventName: EventName, options: AbortableMethods = {}): AsyncGenerator<EventResult, void> {\n\t\tconst signal = options?.signal;\n\t\tvalidateAbortSignal(signal);\n\n\t\tif (signal?.aborted) {\n\t\t\tthrow new AbortError(undefined, { cause: getReason(signal) });\n\t\t}\n\n\t\tconst unconsumedEvents: unknown[][] = [];\n\t\tconst unconsumedPromises: { resolve: (value?: unknown) => void; reject: (reason?: unknown) => void }[] = [];\n\t\tlet error: unknown = null;\n\t\tlet finished = false;\n\n\t\tconst abortListener = () => {\n\t\t\terrorHandler(new AbortError(undefined, { cause: getReason(signal) }));\n\t\t};\n\n\t\tconst eventHandler = (...args: unknown[]) => {\n\t\t\tconst promise = unconsumedPromises.shift();\n\t\t\tif (promise) {\n\t\t\t\tpromise.resolve(createIterResult(args, false));\n\t\t\t} else {\n\t\t\t\tunconsumedEvents.push(args);\n\t\t\t}\n\t\t};\n\n\t\tconst errorHandler = (err: unknown) => {\n\t\t\tfinished = true;\n\n\t\t\tconst toError = unconsumedPromises.shift();\n\n\t\t\tif (toError) {\n\t\t\t\ttoError.reject(err);\n\t\t\t} else {\n\t\t\t\terror = err;\n\t\t\t}\n\n\t\t\tvoid iterator.return();\n\t\t};\n\n\t\tconst iterator: AsyncGenerator<EventResult, void> = Object.setPrototypeOf(\n\t\t\t{\n\t\t\t\tnext() {\n\t\t\t\t\t// First, we consume all unread events\n\t\t\t\t\tconst value = unconsumedEvents.shift();\n\t\t\t\t\tif (value) {\n\t\t\t\t\t\treturn Promise.resolve(createIterResult(value, false));\n\t\t\t\t\t}\n\n\t\t\t\t\t// Then we error, if an error happened\n\t\t\t\t\t// This happens one time if at all, because after 'error'\n\t\t\t\t\t// we stop listening\n\t\t\t\t\tif (error) {\n\t\t\t\t\t\tconst p = Promise.reject(error);\n\t\t\t\t\t\t// Only the first element errors\n\t\t\t\t\t\terror = null;\n\t\t\t\t\t\treturn p;\n\t\t\t\t\t}\n\n\t\t\t\t\t// If the iterator is finished, resolve to done\n\t\t\t\t\tif (finished) {\n\t\t\t\t\t\treturn Promise.resolve(createIterResult(undefined, true));\n\t\t\t\t\t}\n\n\t\t\t\t\t// Wait until an event happens\n\t\t\t\t\treturn new Promise((resolve, reject) => {\n\t\t\t\t\t\tunconsumedPromises.push({ resolve, reject });\n\t\t\t\t\t});\n\t\t\t\t},\n\n\t\t\t\treturn() {\n\t\t\t\t\temitter.off(eventName, eventHandler);\n\t\t\t\t\temitter.off('error', errorHandler);\n\n\t\t\t\t\tif (signal) {\n\t\t\t\t\t\teventTargetAgnosticRemoveListener(signal, 'abort', abortListener);\n\t\t\t\t\t}\n\n\t\t\t\t\tfinished = true;\n\n\t\t\t\t\tconst doneResult = createIterResult(undefined, true);\n\t\t\t\t\tfor (const promise of unconsumedPromises) {\n\t\t\t\t\t\tpromise.resolve(doneResult);\n\t\t\t\t\t}\n\n\t\t\t\t\treturn Promise.resolve(doneResult);\n\t\t\t\t},\n\n\t\t\t\tthrow(err: unknown) {\n\t\t\t\t\tif (!err || !(err instanceof Error)) {\n\t\t\t\t\t\tthrow new TypeError(`Expected Error instance to be thrown in AsyncEventEmitter.AsyncIterator. Got ${err}`);\n\t\t\t\t\t}\n\n\t\t\t\t\terror = err;\n\t\t\t\t\temitter.off(eventName, eventHandler);\n\t\t\t\t\temitter.off('error', errorHandler);\n\t\t\t\t},\n\n\t\t\t\t[Symbol.asyncIterator]() {\n\t\t\t\t\treturn this;\n\t\t\t\t}\n\t\t\t},\n\t\t\tAsyncIteratorPrototype\n\t\t);\n\n\t\temitter.on(eventName, eventHandler);\n\t\tif (eventName !== 'error') {\n\t\t\temitter.on('error', errorHandler);\n\t\t}\n\n\t\tif (signal) {\n\t\t\teventTargetAgnosticAddListener(signal, 'abort', abortListener);\n\t\t}\n\n\t\treturn iterator;\n\t}\n}\n\nexport interface AsyncEventEmitterPredefinedEvents {\n\tnewListener: [eventName: string | symbol, listener: (...args: any[]) => Awaitable<void>];\n\tremoveListener: [eventName: string | symbol, listener: (...args: any[]) => Awaitable<void>];\n}\n\ninterface WrappedOnceState<Args extends any[] = any[]> {\n\tlistener: (...args: Args) => Awaitable<void>;\n\tfired: boolean;\n\teventName: string | symbol;\n\teventEmitter: AsyncEventEmitter<any>;\n\twrapFn: (...args: Args) => Awaitable<void>;\n}\n\nexport interface WrappedOnce<Args extends any[] = any[]> {\n\t(...args: Args): Awaitable<void>;\n\tlistener: (...args: Args) => Awaitable<void>;\n\t_hasWarnedAboutMaxListeners?: boolean;\n}\n\nexport interface Listener<Args extends any[] = any[]> {\n\t(...args: Args): Awaitable<void>;\n\tlistener: (...args: Args) => Awaitable<void>;\n\t_hasWarnedAboutMaxListeners?: boolean;\n}\n\nexport type Awaitable<T> = T | Promise<T>;\n\nexport interface AbortableMethods {\n\tsignal?: AbortSignal;\n}\n\n// @ts-ignore Not all paths returning is fine just fine:tm:\nfunction onceWrapper<Args extends any[] = any[]>(this: WrappedOnceState<Args>) {\n\tif (!this.fired) {\n\t\tthis.eventEmitter.removeListener(this.eventName, this.wrapFn);\n\t\tthis.fired = true;\n\t\t// eslint-disable-next-line @typescript-eslint/dot-notation\n\t\tif (arguments.length === 0) {\n\t\t\t// @ts-expect-error Types can be hell\n\t\t\treturn this.listener.call(this.eventEmitter);\n\t\t}\n\n\t\t// eslint-disable-next-line prefer-rest-params\n\t\treturn this.listener.apply(this.eventEmitter, arguments as unknown as Args);\n\t}\n}\n\n/**\n * A TypeScript not-compliant way of accessing AbortSignal#reason\n * Because DOM types have it, NodeJS types don't. -w-\n */\nfunction getReason(signal: any) {\n\treturn signal?.reason;\n}\n\nfunction eventTargetAgnosticRemoveListener(emitter: any, name: PropertyKey, listener: (...args: unknown[]) => any, flags?: InternalAgnosticFlags) {\n\tif (typeof emitter.off === 'function') {\n\t\temitter.off(name, listener);\n\t} else if (typeof emitter.removeEventListener === 'function') {\n\t\temitter.removeEventListener(name, listener, flags);\n\t}\n}\n\nfunction eventTargetAgnosticAddListener(emitter: any, name: string | symbol, listener: (...args: unknown[]) => any, flags?: InternalAgnosticFlags) {\n\tif (typeof emitter.on === 'function') {\n\t\tif (flags?.once) {\n\t\t\temitter.once!(name, listener);\n\t\t} else {\n\t\t\temitter.on(name, listener);\n\t\t}\n\t} else if (typeof emitter.addEventListener === 'function') {\n\t\temitter.addEventListener(name, listener, flags);\n\t}\n}\n\ninterface InternalAgnosticFlags {\n\tonce?: boolean;\n}\n\n// eslint-disable-next-line func-names, @typescript-eslint/no-empty-function\nconst AsyncIteratorPrototype = Object.getPrototypeOf(Object.getPrototypeOf(async function* () {}).prototype);\n\nfunction createIterResult(value: unknown, done: boolean) {\n\treturn { value, done };\n}\n\nexport interface AbortErrorOptions {\n\tcause?: unknown;\n}\n\nexport class AbortError extends Error {\n\tpublic readonly code = 'ABORT_ERR';\n\tpublic override readonly name = 'AbortError';\n\n\tpublic constructor(message = 'The operation was aborted', options: AbortErrorOptions | undefined = undefined) {\n\t\tif (options !== undefined && typeof options !== 'object') {\n\t\t\tthrow new TypeError(`Failed to create AbortError: options is not an object or undefined`);\n\t\t}\n\n\t\tsuper(message, options);\n\t}\n}\n\nfunction handleMaybeAsync(emitter: AsyncEventEmitter<any>, result: any) {\n\ttry {\n\t\tconst fin = result.finally;\n\n\t\tif (typeof fin === 'function') {\n\t\t\tconst promiseId = String(++emitter['_wrapperId']);\n\t\t\temitter['_internalPromiseMap'].set(promiseId, result);\n\t\t\tfin.call(result, function final() {\n\t\t\t\temitter['_internalPromiseMap'].delete(promiseId);\n\t\t\t});\n\t\t}\n\t} catch (err) {\n\t\temitter.emit('error', err);\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,SAAS,iBAAiB,OAA0E;AACnG,MAAI,OAAO,UAAU,YAAY;AAChC,UAAM,IAAI,UAAU,sDAAsD,OAAO,OAAO;AAAA,EACzF;AACD;AAJS;AAMT,SAAS,oBAAoB,OAA0D;AAEtF,MAAI,SAAS,EAAE,iBAAiB,cAAc;AAC7C,UAAM,IAAI,UAAU,sDAAsD,OAAO;AAAA,EAClF;AACD;AALS;AAQT,SAAS,UAAU,MAAiB,OAAe;AAClD,SAAO,QAAQ,IAAI,KAAK,QAAQ,SAAS;AACxC,SAAK,KAAK,IAAI,KAAK,QAAQ,CAAC;AAAA,EAC7B;AAEA,OAAK,IAAI;AACV;AANS;AAST,SAAS,WAAgC,KAAW;AAGnD,UAAQ,IAAI,QAAQ;AAAA,IACnB,KAAK;AACJ,aAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;AAAA,IACvB,KAAK;AACJ,aAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;AAAA,IAC/B,KAAK;AACJ,aAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;AAAA,IACvC,KAAK;AACJ,aAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;AAAA,IAC/C,KAAK;AACJ,aAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;AAAA,EACxD;AAEA,SAAO,IAAI,MAAM;AAClB;AAjBS;AAoBT,SAAS,uBAAuB,GAAc,GAAgC;AAC7E,WAAS,IAAI,GAAG,IAAI,EAAE,SAAS,GAAG,KAAK;AAEtC,UAAM,MAAM,EAAE,QAAQ,EAAE,CAAC,CAAC;AAC1B,QAAI,QAAQ,IAAI;AACf,YAAM,OAAO,EAAE,SAAS;AACxB,UAAI,OAAO,GAAG;AACb,YAAI,MAAM;AACV,cAAM,SAAS,KAAK,IAAI,EAAE,SAAS,GAAG,IAAI;AAE1C,eAAO,SAAS,OAAO,EAAE,IAAI,GAAG,MAAM,EAAE,MAAM,GAAG,GAAG;AACnD;AAAA,QACD;AACA,YAAI,MAAM,GAAG;AACZ,iBAAO,CAAC,KAAK,CAAC;AAAA,QACf;AAAA,MACD;AAAA,IACD;AAAA,EACD;AAEA,SAAO,CAAC,GAAG,CAAC;AACb;AArBS;AAuBT,SAAS,kBAAgD,KAAY,KAAY;AAChF,MAAI,WAAW;AACf,MAAI;AACH,UAAM,EAAE,KAAK,IAAI,KAAK;AACtB,QAAI,SAAS;AAAqB,iBAAW,OAAO;AAAA,EACrD,QAAE;AAAA,EAEF;AACA,QAAM,MAAM;AAAA,uBAA0B;AAAA;AAEtC,QAAM,WAAW,IAAI,MAAO,MAAM,IAAI,EAAE,MAAM,CAAC;AAC/C,QAAM,WAAW,IAAI,MAAO,MAAM,IAAI,EAAE,MAAM,CAAC;AAE/C,QAAM,EAAE,GAAG,KAAK,GAAG,IAAI,IAAI,uBAAuB,UAAU,QAAQ;AACpE,MAAI,MAAM,GAAG;AACZ,aAAS,OAAO,MAAM,GAAG,MAAM,GAAG,mDAAmD;AAAA,EACtF;AAEA,SAAO,IAAI,QAAQ,MAAM,SAAS,KAAK,IAAI;AAC5C;AAnBS;AAyBF,IAAM,oBAAN,MAA4I;AAAA,EAA5I;AACN,SAAQ,UAAqH;AAAA,MAC5H,WAAW;AAAA,IACZ;AAEA,SAAQ,cAAc;AACtB,SAAQ,gBAAgB;AACxB,SAAQ,sBAAkD,oBAAI,IAAI;AAClE,SAAQ,aAAa;AAAA;AAAA,EAEd,YACN,WACA,UACO;AACP,qBAAiB,QAAQ;AAEzB,UAAM,UAAU,KAAK,cAAc,WAAW,UAAU,KAAK;AAE7D,SAAK,aAAa,WAAW,SAAS,KAAK;AAE3C,WAAO;AAAA,EACR;AAAA,EAEO,GACN,WACA,UACO;AACP,WAAO,KAAK,YAAY,WAAW,QAAQ;AAAA,EAC5C;AAAA,EAEO,KACN,WACA,UACO;AACP,qBAAiB,QAAQ;AAEzB,UAAM,UAAU,KAAK,cAAc,WAAW,UAAU,IAAI;AAE5D,SAAK,aAAa,WAAW,SAAS,KAAK;AAE3C,WAAO;AAAA,EACR;AAAA,EAEO,eACN,WACA,UACO;AACP,qBAAiB,QAAQ;AAEzB,UAAM,SAAS,KAAK;AACpB,UAAM,YAAY,OAAO,SAAS;AAElC,QAAI,cAAc,QAAW;AAC5B,aAAO;AAAA,IACR;AAEA,QAAI,cAAc,YAAa,UAA0B,aAAa,UAAU;AAC/E,UAAI,EAAE,KAAK,gBAAgB,GAAG;AAC7B,aAAK,UAAU,EAAE,WAAW,KAAK;AAAA,MAIlC,OAAO;AACN,eAAO,OAAO,SAAS;AACvB,YAAI,OAAO,gBAAgB;AAC1B,eAAK,KAAK,kBAAkB,WAAsB,UAA0B,YAAY,SAAS;AAAA,QAClG;AAAA,MACD;AAAA,IACD,WAAW,OAAO,cAAc,YAAY;AAC3C,UAAI,WAAW;AAEf,eAAS,IAAI,UAAU,SAAS,GAAG,KAAK,GAAG,KAAK;AAC/C,YAAI,UAAU,CAAC,MAAM,YAAa,UAAU,CAAC,EAAkB,aAAa,UAAU;AACrF,qBAAW;AACX;AAAA,QACD;AAAA,MACD;AAEA,UAAI,WAAW,GAAG;AACjB,eAAO;AAAA,MACR;AAEA,UAAI,aAAa,GAAG;AACnB,kBAAU,MAAM;AAAA,MACjB,OAAO;AACN,kBAAU,WAAW,QAAQ;AAAA,MAC9B;AAEA,UAAI,UAAU,WAAW,GAAG;AAC3B,eAAO,OAAO,SAAS;AACvB,UAAE,KAAK;AAAA,MACR;AAEA,UAAI,OAAO,mBAAmB,QAAW;AAExC,aAAK,KAAK,kBAAkB,WAA8B,QAAQ;AAAA,MACnE;AAAA,IACD;AAEA,WAAO;AAAA,EACR;AAAA,EAEO,IACN,WACA,UACO;AACP,WAAO,KAAK,eAAe,WAAW,QAAQ;AAAA,EAC/C;AAAA,EAEO,mBAAqF,OAA6B;AACxH,UAAM,SAAS,KAAK;AAGpB,QAAI,OAAO,mBAAmB,QAAW;AACxC,UAAI,CAAC,OAAO;AACX,aAAK,UAAU,EAAE,WAAW,KAAK;AACjC,aAAK,cAAc;AAAA,MACpB,WAAW,OAAO,KAAK,MAAM,QAAW;AACvC,YAAI,EAAE,KAAK,gBAAgB,GAAG;AAC7B,eAAK,UAAU,EAAE,WAAW,KAAK;AAAA,QAClC,OAAO;AACN,iBAAO,OAAO,KAAK;AAAA,QACpB;AAAA,MACD;AAEA,aAAO;AAAA,IACR;AAGA,QAAI,CAAC,OAAO;AACX,iBAAW,OAAO,QAAQ,QAAQ,MAAM,GAAG;AAC1C,YAAI,QAAQ,kBAAkB;AAC7B;AAAA,QACD;AACA,aAAK,mBAAmB,GAAG;AAAA,MAC5B;AAEA,WAAK,mBAAmB,gBAAgB;AACxC,WAAK,UAAU,EAAE,WAAW,KAAK;AACjC,WAAK,cAAc;AAEnB,aAAO;AAAA,IACR;AAEA,UAAM,YAAY,OAAO,KAAK;AAE9B,QAAI,OAAO,cAAc,YAAY;AACpC,WAAK,eAAe,OAAO,SAAS;AAAA,IACrC,WAAW,cAAc,QAAW;AAEnC,eAAS,IAAI,UAAU,SAAS,GAAG,KAAK,GAAG,KAAK;AAC/C,aAAK,eAAe,OAAO,UAAU,CAAC,CAAC;AAAA,MACxC;AAAA,IACD;AAEA,WAAO;AAAA,EACR;AAAA,EAEO,gBAAgB,GAAiB;AACvC,QAAI,OAAO,MAAM,YAAY,IAAI,KAAK,OAAO,MAAM,CAAC,GAAG;AACtD,YAAM,IAAI,WAAW,oEAAoE,WAAW;AAAA,IACrG;AAEA,SAAK,gBAAgB;AAErB,WAAO;AAAA,EACR;AAAA,EAEO,kBAA0B;AAChC,WAAO,KAAK;AAAA,EACb;AAAA,EAEO,UAA4E,WAA4D;AAC9I,UAAM,YAAY,KAAK,QAAQ,SAAS;AAExC,QAAI,cAAc,QAAW;AAC5B,aAAO,CAAC;AAAA,IACT;AAEA,QAAI,OAAO,cAAc,YAAY;AACpC,aAAO,CAAC,UAAU,YAAY,SAAS;AAAA,IACxC;AAEA,UAAM,MAAM,WAAW,SAAS;AAEhC,aAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,EAAE,GAAG;AACpC,YAAM,OAAQ,IAAI,CAAC,EAAkB;AACrC,UAAI,OAAO,SAAS,YAAY;AAC/B,YAAI,CAAC,IAAI;AAAA,MACV;AAAA,IACD;AAEA,WAAO;AAAA,EACR;AAAA,EAEO,aAA+E,WAAgD;AACrI,UAAM,YAAY,KAAK,QAAQ,SAAS;AAExC,QAAI,cAAc,QAAW;AAC5B,aAAO,CAAC;AAAA,IACT;AAEA,QAAI,OAAO,cAAc,YAAY;AACpC,aAAO,CAAC,SAAS;AAAA,IAClB;AAEA,WAAO,WAAW,SAAS;AAAA,EAC5B;AAAA,EAEO,KACN,cACG,MACO;AACV,QAAI,UAAU,cAAc;AAE5B,UAAM,SAAS,KAAK;AACpB,QAAI,WAAW,QAAW;AACzB,gBAAU,WAAW,OAAO,UAAU;AAAA,IACvC,WAAW,CAAC,SAAS;AACpB,aAAO;AAAA,IACR;AAEA,QAAI,SAAS;AACZ,UAAI;AAEJ,UAAI,KAAK,SAAS,GAAG;AAEpB,aAAK,KAAK,CAAC;AAAA,MACZ;AAEA,UAAI,cAAc,OAAO;AACxB,YAAI;AACH,gBAAM,UAAU,CAAC;AAEjB,gBAAM,kBAAkB,SAAS,kBAAkB,UAAU,IAAI;AACjE,iBAAO,eAAe,IAAI,SAAS;AAAA,YAClC,OAAO,kBAAkB,KAAK,MAAM,IAAI,OAAgB;AAAA,YACxD,cAAc;AAAA,UACf,CAAC;AAAA,QACF,QAAE;AAAA,QAEF;AAEA,cAAM;AAAA,MACP;AAEA,YAAM,mBAAmB,OAAO,EAAE;AAGlC,YAAM,MAAM,IAAI,MAAM,6CAA6C,kBAAkB;AAErF,UAAI,UAAU;AAEd,YAAM;AAAA,IACP;AAEA,UAAM,WAAW,OAAO,SAAS;AAEjC,QAAI,aAAa,QAAW;AAC3B,aAAO;AAAA,IACR;AAEA,QAAI,OAAO,aAAa,YAAY;AACnC,YAAM,SAAS,SAAS,MAAM,MAAM,IAAI;AAExC,UAAI,WAAW,UAAa,WAAW,MAAM;AAC5C,yBAAiB,MAAM,MAAM;AAAA,MAC9B;AAAA,IACD,OAAO;AACN,YAAM,MAAM,SAAS;AACrB,YAAM,YAAY,WAAW,QAA4B;AAEzD,eAAS,IAAI,GAAG,IAAI,KAAK,EAAE,GAAG;AAE7B,cAAM,SAAS,UAAU,CAAC,EAAE,MAAM,MAAM,IAAI;AAE5C,YAAI,WAAW,UAAa,WAAW,MAAM;AAC5C,2BAAiB,MAAM,MAAM;AAAA,QAC9B;AAAA,MACD;AAAA,IACD;AAEA,WAAO;AAAA,EACR;AAAA,EAEO,cAAgF,WAAsB;AAC5G,UAAM,SAAS,KAAK;AAEpB,QAAI,WAAW,QAAW;AACzB,aAAO;AAAA,IACR;AAEA,UAAM,iBAAiB,OAAO,SAAS;AAEvC,QAAI,OAAO,mBAAmB,YAAY;AACzC,aAAO;AAAA,IACR;AAEA,WAAO,gBAAgB,UAAU;AAAA,EAClC;AAAA,EAEO,gBACN,WACA,UACO;AACP,qBAAiB,QAAQ;AAEzB,UAAM,UAAU,KAAK,cAAc,WAAW,UAAU,KAAK;AAE7D,SAAK,aAAa,WAAW,SAAS,IAAI;AAE1C,WAAO;AAAA,EACR;AAAA,EAEO,oBACN,WACA,UACO;AACP,qBAAiB,QAAQ;AAEzB,UAAM,UAAU,KAAK,cAAc,WAAW,UAAU,IAAI;AAE5D,SAAK,aAAa,WAAW,SAAS,IAAI;AAE1C,WAAO;AAAA,EACR;AAAA,EAEO,aAAyE;AAC/E,WAAO,KAAK,cAAc,IAAI,QAAQ,QAAQ,KAAK,OAAO,IAAI,CAAC;AAAA,EAChE;AAAA,EAEA,MAAa,gCAAgC;AAC5C,UAAM,WAAW,CAAC,GAAG,KAAK,oBAAoB,OAAO,CAAC;AAEtD,QAAI,SAAS,WAAW,GAAG;AAC1B,aAAO;AAAA,IACR;AAEA,UAAM,QAAQ,IAAI,QAAQ;AAE1B,WAAO;AAAA,EACR;AAAA,EAEQ,aACP,WACA,iBACA,SACC;AAED,QAAI,KAAK,QAAQ,gBAAgB,QAAW;AAE3C,WAAK,KAAK,eAAe,WAA+B,gBAAgC,YAAY,eAAe;AAAA,IACpH;AAEA,QAAI,WAAW,KAAK,QAAQ,SAAS;AAErC,QAAI,aAAa,QAAW;AAE3B,iBAAW,KAAK,QAAQ,SAAS,IAAI;AACrC,QAAE,KAAK;AAAA,IACR,WAAW,OAAO,aAAa,YAAY;AAG1C,iBAAW,KAAK,QAAQ,SAAS,IAAI,UAAU,CAAC,iBAAiB,QAAQ,IAAI,CAAC,UAAU,eAAe;AAAA,IAExG,WAAW,SAAS;AACnB,eAAS,QAAQ,eAAe;AAAA,IACjC,OAAO;AACN,eAAS,KAAK,eAAe;AAAA,IAC9B;AAEA,QAAI,KAAK,gBAAgB,KAAK,SAAS,SAAS,KAAK,iBAAiB,CAAC,SAAS,6BAA6B;AAC5G,eAAS,8BAA8B;AACvC,YAAM,iBAAiB;AAAA,QACtB,oDAAoD,SAAS,UAAU,OAAO,SAAS,wBACtF,KAAK,YAAY;AAAA,QAElB;AAAA,MACD,EAAE,KAAK,GAAG;AACV,cAAQ,KAAK,cAAc;AAAA,IAC5B;AAAA,EACD;AAAA,EAEQ,cACP,WACA,UACA,MACyB;AACzB,QAAI,CAAC,MAAM;AACV,aAAO;AAAA,IACR;AAEA,UAAM,QAAQ;AAAA,MACb,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,cAAc;AAAA,MACd;AAAA,MACA;AAAA,IACD;AAEA,UAAM,UAAU;AAEhB,UAAM,UAAU,QAAQ,KAAK,KAAK;AAGlC,YAAQ,WAAW;AACnB,UAAM,SAAS;AAEf,WAAO;AAAA,EACR;AAAA,EAEA,OAAc,cAIZ,SAAkB,WAAgE;AACnF,WAAO,QAAQ,cAAc,SAAS;AAAA,EACvC;AAAA,EAEA,aAAoB,KAOlB,SAAkB,WAAsB,UAA4B,CAAC,GAAG;AACzE,UAAM,SAAS,SAAS;AACxB,wBAAoB,MAAM;AAE1B,QAAI,QAAQ,SAAS;AACpB,YAAM,IAAI,WAAW,QAAW,EAAE,OAAO,UAAU,MAAM,EAAE,CAAC;AAAA,IAC7D;AAEA,WAAO,IAAI,QAAqB,CAAC,SAAS,WAAW;AACpD,YAAM,gBAAgB,wBAAC,QAAiB;AACvC,gBAAQ,eAAe,WAAW,QAAQ;AAE1C,YAAI,QAAQ;AACX,4CAAkC,SAAS,WAAW,aAAa;AAAA,QACpE;AAEA,eAAO,GAAG;AAAA,MACX,GARsB;AAUtB,YAAM,WAAW,2BAAI,SAAoB;AACxC,gBAAQ,eAAe,SAAS,aAAa;AAE7C,YAAI,QAAQ;AACX,4CAAkC,QAAQ,SAAS,aAAa;AAAA,QACjE;AAEA,gBAAQ,IAAmB;AAAA,MAC5B,GARiB;AAUjB,cAAQ,KAAK,WAAW,QAAQ;AAChC,UAAI,cAAc,SAAS;AAC1B,gBAAQ,KAAK,SAAS,aAAa;AAAA,MACpC;AAEA,YAAM,gBAAgB,6BAAM;AAC3B,0CAAkC,SAAS,WAAW,QAAQ;AAC9D,0CAAkC,SAAS,SAAS,aAAa;AACjE,eAAO,IAAI,WAAW,QAAW,EAAE,OAAO,UAAU,MAAM,EAAE,CAAC,CAAC;AAAA,MAC/D,GAJsB;AAMtB,UAAI,QAAQ;AACX,uCAA+B,QAAQ,SAAS,eAAe,EAAE,MAAM,KAAK,CAAC;AAAA,MAC9E;AAAA,IACD,CAAC;AAAA,EACF;AAAA,EAEA,OAAc,GAOZ,SAAkB,WAAsB,UAA4B,CAAC,GAAsC;AAC5G,UAAM,SAAS,SAAS;AACxB,wBAAoB,MAAM;AAE1B,QAAI,QAAQ,SAAS;AACpB,YAAM,IAAI,WAAW,QAAW,EAAE,OAAO,UAAU,MAAM,EAAE,CAAC;AAAA,IAC7D;AAEA,UAAM,mBAAgC,CAAC;AACvC,UAAM,qBAAmG,CAAC;AAC1G,QAAI,QAAiB;AACrB,QAAI,WAAW;AAEf,UAAM,gBAAgB,6BAAM;AAC3B,mBAAa,IAAI,WAAW,QAAW,EAAE,OAAO,UAAU,MAAM,EAAE,CAAC,CAAC;AAAA,IACrE,GAFsB;AAItB,UAAM,eAAe,2BAAI,SAAoB;AAC5C,YAAM,UAAU,mBAAmB,MAAM;AACzC,UAAI,SAAS;AACZ,gBAAQ,QAAQ,iBAAiB,MAAM,KAAK,CAAC;AAAA,MAC9C,OAAO;AACN,yBAAiB,KAAK,IAAI;AAAA,MAC3B;AAAA,IACD,GAPqB;AASrB,UAAM,eAAe,wBAAC,QAAiB;AACtC,iBAAW;AAEX,YAAM,UAAU,mBAAmB,MAAM;AAEzC,UAAI,SAAS;AACZ,gBAAQ,OAAO,GAAG;AAAA,MACnB,OAAO;AACN,gBAAQ;AAAA,MACT;AAEA,WAAK,SAAS,OAAO;AAAA,IACtB,GAZqB;AAcrB,UAAM,WAA8C,OAAO;AAAA,MAC1D;AAAA,QACC,OAAO;AAEN,gBAAM,QAAQ,iBAAiB,MAAM;AACrC,cAAI,OAAO;AACV,mBAAO,QAAQ,QAAQ,iBAAiB,OAAO,KAAK,CAAC;AAAA,UACtD;AAKA,cAAI,OAAO;AACV,kBAAM,IAAI,QAAQ,OAAO,KAAK;AAE9B,oBAAQ;AACR,mBAAO;AAAA,UACR;AAGA,cAAI,UAAU;AACb,mBAAO,QAAQ,QAAQ,iBAAiB,QAAW,IAAI,CAAC;AAAA,UACzD;AAGA,iBAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACvC,+BAAmB,KAAK,EAAE,SAAS,OAAO,CAAC;AAAA,UAC5C,CAAC;AAAA,QACF;AAAA,QAEA,SAAS;AACR,kBAAQ,IAAI,WAAW,YAAY;AACnC,kBAAQ,IAAI,SAAS,YAAY;AAEjC,cAAI,QAAQ;AACX,8CAAkC,QAAQ,SAAS,aAAa;AAAA,UACjE;AAEA,qBAAW;AAEX,gBAAM,aAAa,iBAAiB,QAAW,IAAI;AACnD,qBAAW,WAAW,oBAAoB;AACzC,oBAAQ,QAAQ,UAAU;AAAA,UAC3B;AAEA,iBAAO,QAAQ,QAAQ,UAAU;AAAA,QAClC;AAAA,QAEA,MAAM,KAAc;AACnB,cAAI,CAAC,OAAO,EAAE,eAAe,QAAQ;AACpC,kBAAM,IAAI,UAAU,gFAAgF,KAAK;AAAA,UAC1G;AAEA,kBAAQ;AACR,kBAAQ,IAAI,WAAW,YAAY;AACnC,kBAAQ,IAAI,SAAS,YAAY;AAAA,QAClC;AAAA,QAEA,CAAC,OAAO,aAAa,IAAI;AACxB,iBAAO;AAAA,QACR;AAAA,MACD;AAAA,MACA;AAAA,IACD;AAEA,YAAQ,GAAG,WAAW,YAAY;AAClC,QAAI,cAAc,SAAS;AAC1B,cAAQ,GAAG,SAAS,YAAY;AAAA,IACjC;AAEA,QAAI,QAAQ;AACX,qCAA+B,QAAQ,SAAS,aAAa;AAAA,IAC9D;AAEA,WAAO;AAAA,EACR;AACD;AAnlBa;AAqnBb,SAAS,cAAsE;AAC9E,MAAI,CAAC,KAAK,OAAO;AAChB,SAAK,aAAa,eAAe,KAAK,WAAW,KAAK,MAAM;AAC5D,SAAK,QAAQ;AAEb,QAAI,UAAU,WAAW,GAAG;AAE3B,aAAO,KAAK,SAAS,KAAK,KAAK,YAAY;AAAA,IAC5C;AAGA,WAAO,KAAK,SAAS,MAAM,KAAK,cAAc,SAA4B;AAAA,EAC3E;AACD;AAbS;AAmBT,SAAS,UAAU,QAAa;AAC/B,SAAO,QAAQ;AAChB;AAFS;AAIT,SAAS,kCAAkC,SAAc,MAAmB,UAAuC,OAA+B;AACjJ,MAAI,OAAO,QAAQ,QAAQ,YAAY;AACtC,YAAQ,IAAI,MAAM,QAAQ;AAAA,EAC3B,WAAW,OAAO,QAAQ,wBAAwB,YAAY;AAC7D,YAAQ,oBAAoB,MAAM,UAAU,KAAK;AAAA,EAClD;AACD;AANS;AAQT,SAAS,+BAA+B,SAAc,MAAuB,UAAuC,OAA+B;AAClJ,MAAI,OAAO,QAAQ,OAAO,YAAY;AACrC,QAAI,OAAO,MAAM;AAChB,cAAQ,KAAM,MAAM,QAAQ;AAAA,IAC7B,OAAO;AACN,cAAQ,GAAG,MAAM,QAAQ;AAAA,IAC1B;AAAA,EACD,WAAW,OAAO,QAAQ,qBAAqB,YAAY;AAC1D,YAAQ,iBAAiB,MAAM,UAAU,KAAK;AAAA,EAC/C;AACD;AAVS;AAiBT,IAAM,yBAAyB,OAAO,eAAe,OAAO,eAAe,mBAAmB;AAAC,CAAC,EAAE,SAAS;AAE3G,SAAS,iBAAiB,OAAgB,MAAe;AACxD,SAAO,EAAE,OAAO,KAAK;AACtB;AAFS;AAQF,IAAM,aAAN,cAAyB,MAAM;AAAA,EAI9B,YAAY,UAAU,6BAA6B,UAAyC,QAAW;AAC7G,QAAI,YAAY,UAAa,OAAO,YAAY,UAAU;AACzD,YAAM,IAAI,UAAU,oEAAoE;AAAA,IACzF;AAEA,UAAM,SAAS,OAAO;AARvB,SAAgB,OAAO;AACvB,SAAyB,OAAO;AAAA,EAQhC;AACD;AAXa;AAab,SAAS,iBAAiB,SAAiC,QAAa;AACvE,MAAI;AACH,UAAM,MAAM,OAAO;AAEnB,QAAI,OAAO,QAAQ,YAAY;AAC9B,YAAM,YAAY,OAAO,EAAE,QAAQ,YAAY,CAAC;AAChD,cAAQ,qBAAqB,EAAE,IAAI,WAAW,MAAM;AACpD,UAAI,KAAK,QAAQ,gCAAS,QAAQ;AACjC,gBAAQ,qBAAqB,EAAE,OAAO,SAAS;AAAA,MAChD,GAFiB,QAEhB;AAAA,IACF;AAAA,EACD,SAAS,KAAP;AACD,YAAQ,KAAK,SAAS,GAAG;AAAA,EAC1B;AACD;AAdS;", "names": []}