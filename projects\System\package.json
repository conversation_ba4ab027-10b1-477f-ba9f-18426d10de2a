{"scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "nodemon index.js"}, "dependencies": {"@napi-rs/canvas": "^0.1.40", "axios": "^1.4.0", "better-sqlite3": "^8.4.0", "canvas": "^2.11.0", "canvas-constructor": "^7.0.0", "chalk": "^4.1.2", "discord-banners": "^1.0.1", "discord-html-transcripts": "^3.1.4", "discord-image-generation": "^1.4.25", "discord-invite": "^3.0.0", "discord-inviter": "^0.9.3", "discord-perms-array": "^1.0.4", "discord.js": "^14.11.0", "dotenv": "^16.0.3", "fs": "^0.0.1-security", "moment": "^2.29.4", "mongoose": "^7.0.3", "ms": "^2.1.3", "node-recursive-directory": "^1.2.0", "pm2": "^5.3.0", "pro.db-fork": "^4.2.1", "quick.db": "^9.1.6", "randomstring": "^1.2.3", "st.db": "^6.0.1"}, "devDependencies": {"nodemon": "^2.0.22"}}