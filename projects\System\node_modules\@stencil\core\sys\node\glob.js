!function(t,e){for(var r in e)t[r]=e[r]}(exports,function(t){var e={};function r(n){if(e[n])return e[n].exports;var i=e[n]={i:n,l:!1,exports:{}};return t[n].call(i.exports,i,i.exports,r),i.l=!0,i.exports}return r.m=t,r.c=e,r.d=function(t,e,n){r.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:n})},r.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},r.t=function(t,e){if(1&e&&(t=r(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var i in t)r.d(n,i,function(e){return t[e]}.bind(null,i));return n},r.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return r.d(e,"a",e),e},r.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},r.p="",r(r.s=10)}([function(t,e){t.exports=require("path")},function(t,e){t.exports=require("fs")},function(t,e,r){const n=t.exports=(t,e,r={})=>(m(e),!(!r.nocomment&&"#"===e.charAt(0))&&new v(e,r).match(t));t.exports=n;const i=r(12);n.sep=i.sep;const s=Symbol("globstar **");n.GLOBSTAR=s;const o=r(13),a={"!":{open:"(?:(?!(?:",close:"))[^/]*?)"},"?":{open:"(?:",close:")?"},"+":{open:"(?:",close:")+"},"*":{open:"(?:",close:")*"},"@":{open:"(?:",close:")"}},c="[^/]",h=c+"*?",l=t=>t.split("").reduce(((t,e)=>(t[e]=!0,t)),{}),u=l("().*{}+?[]^$\\!"),f=l("[.("),p=/\/+/;n.filter=(t,e={})=>(r,i,s)=>n(r,t,e);const d=(t,e={})=>{const r={};return Object.keys(t).forEach((e=>r[e]=t[e])),Object.keys(e).forEach((t=>r[t]=e[t])),r};n.defaults=t=>{if(!t||"object"!=typeof t||!Object.keys(t).length)return n;const e=n,r=(r,n,i)=>e(r,n,d(t,i));return(r.Minimatch=class extends e.Minimatch{constructor(e,r){super(e,d(t,r))}}).defaults=r=>e.defaults(d(t,r)).Minimatch,r.filter=(r,n)=>e.filter(r,d(t,n)),r.defaults=r=>e.defaults(d(t,r)),r.makeRe=(r,n)=>e.makeRe(r,d(t,n)),r.braceExpand=(r,n)=>e.braceExpand(r,d(t,n)),r.match=(r,n,i)=>e.match(r,n,d(t,i)),r},n.braceExpand=(t,e)=>g(t,e);const g=(t,e={})=>(m(t),e.nobrace||!/\{(?:(?!\{).)*\}/.test(t)?[t]:o(t)),m=t=>{if("string"!=typeof t)throw new TypeError("invalid pattern");if(t.length>65536)throw new TypeError("pattern is too long")},y=Symbol("subparse");n.makeRe=(t,e)=>new v(t,e||{}).makeRe(),n.match=(t,e,r={})=>{const n=new v(e,r);return t=t.filter((t=>n.match(t))),n.options.nonull&&!t.length&&t.push(e),t};const b=t=>t.replace(/[[\]\\]/g,"\\$&");class v{constructor(t,e){m(t),e||(e={}),this.options=e,this.set=[],this.pattern=t,this.windowsPathsNoEscape=!!e.windowsPathsNoEscape||!1===e.allowWindowsEscape,this.windowsPathsNoEscape&&(this.pattern=this.pattern.replace(/\\/g,"/")),this.regexp=null,this.negate=!1,this.comment=!1,this.empty=!1,this.partial=!!e.partial,this.make()}debug(){}make(){const t=this.pattern,e=this.options;if(!e.nocomment&&"#"===t.charAt(0))return void(this.comment=!0);if(!t)return void(this.empty=!0);this.parseNegate();let r=this.globSet=this.braceExpand();e.debug&&(this.debug=(...t)=>console.error(...t)),this.debug(this.pattern,r),r=this.globParts=r.map((t=>t.split(p))),this.debug(this.pattern,r),r=r.map(((t,e,r)=>t.map(this.parse,this))),this.debug(this.pattern,r),r=r.filter((t=>-1===t.indexOf(!1))),this.debug(this.pattern,r),this.set=r}parseNegate(){if(this.options.nonegate)return;const t=this.pattern;let e=!1,r=0;for(let n=0;n<t.length&&"!"===t.charAt(n);n++)e=!e,r++;r&&(this.pattern=t.slice(r)),this.negate=e}matchOne(t,e,r){var n=this.options;this.debug("matchOne",{this:this,file:t,pattern:e}),this.debug("matchOne",t.length,e.length);for(var i=0,o=0,a=t.length,c=e.length;i<a&&o<c;i++,o++){this.debug("matchOne loop");var h,l=e[o],u=t[i];if(this.debug(e,l,u),!1===l)return!1;if(l===s){this.debug("GLOBSTAR",[e,l,u]);var f=i,p=o+1;if(p===c){for(this.debug("** at the end");i<a;i++)if("."===t[i]||".."===t[i]||!n.dot&&"."===t[i].charAt(0))return!1;return!0}for(;f<a;){var d=t[f];if(this.debug("\nglobstar while",t,f,e,p,d),this.matchOne(t.slice(f),e.slice(p),r))return this.debug("globstar found match!",f,a,d),!0;if("."===d||".."===d||!n.dot&&"."===d.charAt(0)){this.debug("dot detected!",t,f,e,p);break}this.debug("globstar swallow a segment, and continue"),f++}return!(!r||(this.debug("\n>>> no match, partial?",t,f,e,p),f!==a))}if("string"==typeof l?(h=u===l,this.debug("string match",l,u,h)):(h=u.match(l),this.debug("pattern match",l,u,h)),!h)return!1}if(i===a&&o===c)return!0;if(i===a)return r;if(o===c)return i===a-1&&""===t[i];throw new Error("wtf?")}braceExpand(){return g(this.pattern,this.options)}parse(t,e){m(t);const r=this.options;if("**"===t){if(!r.noglobstar)return s;t="*"}if(""===t)return"";let n="",i=!!r.nocase,o=!1;const l=[],p=[];let d,g,v,_,w=!1,E=-1,O=-1;const k="."===t.charAt(0)?"":r.dot?"(?!(?:^|\\/)\\.{1,2}(?:$|\\/))":"(?!\\.)",A=()=>{if(d){switch(d){case"*":n+=h,i=!0;break;case"?":n+=c,i=!0;break;default:n+="\\"+d}this.debug("clearStateChar %j %j",d,n),d=!1}};for(let e,s=0;s<t.length&&(e=t.charAt(s));s++)if(this.debug("%s\t%s %s %j",t,s,n,e),o){if("/"===e)return!1;u[e]&&(n+="\\"),n+=e,o=!1}else switch(e){case"/":return!1;case"\\":if(w&&"-"===t.charAt(s+1)){n+=e;continue}A(),o=!0;continue;case"?":case"*":case"+":case"@":case"!":if(this.debug("%s\t%s %s %j <-- stateChar",t,s,n,e),w){this.debug("  in class"),"!"===e&&s===O+1&&(e="^"),n+=e;continue}this.debug("call clearStateChar %j",d),A(),d=e,r.noext&&A();continue;case"(":if(w){n+="(";continue}if(!d){n+="\\(";continue}l.push({type:d,start:s-1,reStart:n.length,open:a[d].open,close:a[d].close}),n+="!"===d?"(?:(?!(?:":"(?:",this.debug("plType %j %j",d,n),d=!1;continue;case")":if(w||!l.length){n+="\\)";continue}A(),i=!0,v=l.pop(),n+=v.close,"!"===v.type&&p.push(v),v.reEnd=n.length;continue;case"|":if(w||!l.length){n+="\\|";continue}A(),n+="|";continue;case"[":if(A(),w){n+="\\"+e;continue}w=!0,O=s,E=n.length,n+=e;continue;case"]":if(s===O+1||!w){n+="\\"+e;continue}g=t.substring(O+1,s);try{RegExp("["+b(g.replace(/\\([^-\]])/g,"$1"))+"]"),n+=e}catch(t){n=n.substring(0,E)+"(?:$.)"}i=!0,w=!1;continue;default:A(),!u[e]||"^"===e&&w||(n+="\\"),n+=e}for(w&&(g=t.slice(O+1),_=this.parse(g,y),n=n.substring(0,E)+"\\["+_[0],i=i||_[1]),v=l.pop();v;v=l.pop()){let t;t=n.slice(v.reStart+v.open.length),this.debug("setting tail",n,v),t=t.replace(/((?:\\{2}){0,64})(\\?)\|/g,((t,e,r)=>(r||(r="\\"),e+e+r+"|"))),this.debug("tail=%j\n   %s",t,t,v,n);const e="*"===v.type?h:"?"===v.type?c:"\\"+v.type;i=!0,n=n.slice(0,v.reStart)+e+"\\("+t}A(),o&&(n+="\\\\");const S=f[n.charAt(0)];for(let t=p.length-1;t>-1;t--){const r=p[t],i=n.slice(0,r.reStart),s=n.slice(r.reStart,r.reEnd-8);let o=n.slice(r.reEnd);const a=n.slice(r.reEnd-8,r.reEnd)+o,c=i.split("(").length-1;let h=o;for(let t=0;t<c;t++)h=h.replace(/\)[+*?]?/,"");o=h;n=i+s+o+(""===o&&e!==y?"$":"")+a}if(""!==n&&i&&(n="(?=.)"+n),S&&(n=k+n),e===y)return[n,i];if(!i)return(t=>t.replace(/\\(.)/g,"$1"))(t);const j=r.nocase?"i":"";try{return Object.assign(new RegExp("^"+n+"$",j),{_glob:t,_src:n})}catch(t){return new RegExp("$.")}}makeRe(){if(this.regexp||!1===this.regexp)return this.regexp;const t=this.set;if(!t.length)return this.regexp=!1,this.regexp;const e=this.options,r=e.noglobstar?h:e.dot?"(?:(?!(?:\\/|^)(?:\\.{1,2})($|\\/)).)*?":"(?:(?!(?:\\/|^)\\.).)*?",n=e.nocase?"i":"";let i=t.map((t=>(t=t.map((t=>"string"==typeof t?t.replace(/[-[\]{}()*+?.,\\^$|#\s]/g,"\\$&"):t===s?s:t._src)).reduce(((t,e)=>(t[t.length-1]===s&&e===s||t.push(e),t)),[]),t.forEach(((e,n)=>{e===s&&t[n-1]!==s&&(0===n?t.length>1?t[n+1]="(?:\\/|"+r+"\\/)?"+t[n+1]:t[n]=r:n===t.length-1?t[n-1]+="(?:\\/|"+r+")?":(t[n-1]+="(?:\\/|\\/"+r+"\\/)"+t[n+1],t[n+1]=s))})),t.filter((t=>t!==s)).join("/")))).join("|");i="^(?:"+i+")$",this.negate&&(i="^(?!"+i+").*$");try{this.regexp=new RegExp(i,n)}catch(t){this.regexp=!1}return this.regexp}match(t,e=this.partial){if(this.debug("match",t,this.pattern),this.comment)return!1;if(this.empty)return""===t;if("/"===t&&e)return!0;const r=this.options;"/"!==i.sep&&(t=t.split(i.sep).join("/")),t=t.split(p),this.debug(this.pattern,"split",t);const n=this.set;let s;this.debug(this.pattern,"set",n);for(let e=t.length-1;e>=0&&(s=t[e],!s);e--);for(let i=0;i<n.length;i++){const o=n[i];let a=t;r.matchBase&&1===o.length&&(a=[s]);if(this.matchOne(a,o,e))return!!r.flipNegate||!this.negate}return!r.flipNegate&&this.negate}static defaults(t){return n.defaults(t).Minimatch}}n.Minimatch=v},function(t,e){t.exports=require("util")},function(t,e,r){t.exports=b;var n=r(5),i=r(2),s=(i.Minimatch,r(15)),o=r(17).EventEmitter,a=r(0),c=r(6),h=r(0).isAbsolute,l=r(18),u=r(7),f=u.setopts,p=u.ownProp,d=r(19),g=(r(3),u.childrenIgnored),m=u.isIgnored,y=r(9);function b(t,e,r){if("function"==typeof e&&(r=e,e={}),e||(e={}),e.sync){if(r)throw new TypeError("callback provided to sync glob");return l(t,e)}return new _(t,e,r)}b.sync=l;var v=b.GlobSync=l.GlobSync;function _(t,e,r){if("function"==typeof e&&(r=e,e=null),e&&e.sync){if(r)throw new TypeError("callback provided to sync glob");return new v(t,e)}if(!(this instanceof _))return new _(t,e,r);f(this,t,e),this._didRealPath=!1;var n=this.minimatch.set.length;this.matches=new Array(n),"function"==typeof r&&(r=y(r),this.on("error",r),this.on("end",(function(t){r(null,t)})));var i=this;if(this._processing=0,this._emitQueue=[],this._processQueue=[],this.paused=!1,this.noprocess)return this;if(0===n)return o();for(var s=0;s<n;s++)this._process(this.minimatch.set[s],s,!1,o);function o(){--i._processing,i._processing<=0&&i._finish()}}b.glob=b,b.hasMagic=function(t,e){var r=function(t,e){if(null===e||"object"!=typeof e)return t;for(var r=Object.keys(e),n=r.length;n--;)t[r[n]]=e[r[n]];return t}({},e);r.noprocess=!0;var n=new _(t,r).minimatch.set;if(!t)return!1;if(n.length>1)return!0;for(var i=0;i<n[0].length;i++)if("string"!=typeof n[0][i])return!0;return!1},b.Glob=_,s(_,o),_.prototype._finish=function(){if(c(this instanceof _),!this.aborted){if(this.realpath&&!this._didRealpath)return this._realpath();u.finish(this),this.emit("end",this.found)}},_.prototype._realpath=function(){if(!this._didRealpath){this._didRealpath=!0;var t=this.matches.length;if(0===t)return this._finish();for(var e=this,r=0;r<this.matches.length;r++)this._realpathSet(r,n)}function n(){0==--t&&e._finish()}},_.prototype._realpathSet=function(t,e){var r=this.matches[t];if(!r)return e();var i=Object.keys(r),s=this,o=i.length;if(0===o)return e();var a=this.matches[t]=Object.create(null);i.forEach((function(r,i){r=s._makeAbs(r),n.realpath(r,s.realpathCache,(function(n,i){n?"stat"===n.syscall?a[r]=!0:s.emit("error",n):a[i]=!0,0==--o&&(s.matches[t]=a,e())}))}))},_.prototype._mark=function(t){return u.mark(this,t)},_.prototype._makeAbs=function(t){return u.makeAbs(this,t)},_.prototype.abort=function(){this.aborted=!0,this.emit("abort")},_.prototype.pause=function(){this.paused||(this.paused=!0,this.emit("pause"))},_.prototype.resume=function(){if(this.paused){if(this.emit("resume"),this.paused=!1,this._emitQueue.length){var t=this._emitQueue.slice(0);this._emitQueue.length=0;for(var e=0;e<t.length;e++){var r=t[e];this._emitMatch(r[0],r[1])}}if(this._processQueue.length){var n=this._processQueue.slice(0);this._processQueue.length=0;for(e=0;e<n.length;e++){var i=n[e];this._processing--,this._process(i[0],i[1],i[2],i[3])}}}},_.prototype._process=function(t,e,r,n){if(c(this instanceof _),c("function"==typeof n),!this.aborted)if(this._processing++,this.paused)this._processQueue.push([t,e,r,n]);else{for(var s,o=0;"string"==typeof t[o];)o++;switch(o){case t.length:return void this._processSimple(t.join("/"),e,n);case 0:s=null;break;default:s=t.slice(0,o).join("/")}var a,l=t.slice(o);null===s?a=".":h(s)||h(t.map((function(t){return"string"==typeof t?t:"[*]"})).join("/"))?(s&&h(s)||(s="/"+s),a=s):a=s;var u=this._makeAbs(a);if(g(this,a))return n();l[0]===i.GLOBSTAR?this._processGlobStar(s,a,u,l,e,r,n):this._processReaddir(s,a,u,l,e,r,n)}},_.prototype._processReaddir=function(t,e,r,n,i,s,o){var a=this;this._readdir(r,s,(function(c,h){return a._processReaddir2(t,e,r,n,i,s,h,o)}))},_.prototype._processReaddir2=function(t,e,r,n,i,s,o,c){if(!o)return c();for(var h=n[0],l=!!this.minimatch.negate,u=h._glob,f=this.dot||"."===u.charAt(0),p=[],d=0;d<o.length;d++){if("."!==(m=o[d]).charAt(0)||f)(l&&!t?!m.match(h):m.match(h))&&p.push(m)}var g=p.length;if(0===g)return c();if(1===n.length&&!this.mark&&!this.stat){this.matches[i]||(this.matches[i]=Object.create(null));for(d=0;d<g;d++){var m=p[d];t&&(m="/"!==t?t+"/"+m:t+m),"/"!==m.charAt(0)||this.nomount||(m=a.join(this.root,m)),this._emitMatch(i,m)}return c()}n.shift();for(d=0;d<g;d++){m=p[d];t&&(m="/"!==t?t+"/"+m:t+m),this._process([m].concat(n),i,s,c)}c()},_.prototype._emitMatch=function(t,e){if(!this.aborted&&!m(this,e))if(this.paused)this._emitQueue.push([t,e]);else{var r=h(e)?e:this._makeAbs(e);if(this.mark&&(e=this._mark(e)),this.absolute&&(e=r),!this.matches[t][e]){if(this.nodir){var n=this.cache[r];if("DIR"===n||Array.isArray(n))return}this.matches[t][e]=!0;var i=this.statCache[r];i&&this.emit("stat",e,i),this.emit("match",e)}}},_.prototype._readdirInGlobStar=function(t,e){if(!this.aborted){if(this.follow)return this._readdir(t,!1,e);var r=this,n=d("lstat\0"+t,(function(n,i){if(n&&"ENOENT"===n.code)return e();var s=i&&i.isSymbolicLink();r.symlinks[t]=s,s||!i||i.isDirectory()?r._readdir(t,!1,e):(r.cache[t]="FILE",e())}));n&&r.fs.lstat(t,n)}},_.prototype._readdir=function(t,e,r){if(!this.aborted&&(r=d("readdir\0"+t+"\0"+e,r))){if(e&&!p(this.symlinks,t))return this._readdirInGlobStar(t,r);if(p(this.cache,t)){var n=this.cache[t];if(!n||"FILE"===n)return r();if(Array.isArray(n))return r(null,n)}this.fs.readdir(t,function(t,e,r){return function(n,i){n?t._readdirError(e,n,r):t._readdirEntries(e,i,r)}}(this,t,r))}},_.prototype._readdirEntries=function(t,e,r){if(!this.aborted){if(!this.mark&&!this.stat)for(var n=0;n<e.length;n++){var i=e[n];i="/"===t?t+i:t+"/"+i,this.cache[i]=!0}return this.cache[t]=e,r(null,e)}},_.prototype._readdirError=function(t,e,r){if(!this.aborted){switch(e.code){case"ENOTSUP":case"ENOTDIR":var n=this._makeAbs(t);if(this.cache[n]="FILE",n===this.cwdAbs){var i=new Error(e.code+" invalid cwd "+this.cwd);i.path=this.cwd,i.code=e.code,this.emit("error",i),this.abort()}break;case"ENOENT":case"ELOOP":case"ENAMETOOLONG":case"UNKNOWN":this.cache[this._makeAbs(t)]=!1;break;default:this.cache[this._makeAbs(t)]=!1,this.strict&&(this.emit("error",e),this.abort()),this.silent||console.error("glob error",e)}return r()}},_.prototype._processGlobStar=function(t,e,r,n,i,s,o){var a=this;this._readdir(r,s,(function(c,h){a._processGlobStar2(t,e,r,n,i,s,h,o)}))},_.prototype._processGlobStar2=function(t,e,r,n,i,s,o,a){if(!o)return a();var c=n.slice(1),h=t?[t]:[],l=h.concat(c);this._process(l,i,!1,a);var u=this.symlinks[r],f=o.length;if(u&&s)return a();for(var p=0;p<f;p++){if("."!==o[p].charAt(0)||this.dot){var d=h.concat(o[p],c);this._process(d,i,!0,a);var g=h.concat(o[p],n);this._process(g,i,!0,a)}}a()},_.prototype._processSimple=function(t,e,r){var n=this;this._stat(t,(function(i,s){n._processSimple2(t,e,i,s,r)}))},_.prototype._processSimple2=function(t,e,r,n,i){if(this.matches[e]||(this.matches[e]=Object.create(null)),!n)return i();if(t&&h(t)&&!this.nomount){var s=/[\/\\]$/.test(t);"/"===t.charAt(0)?t=a.join(this.root,t):(t=a.resolve(this.root,t),s&&(t+="/"))}"win32"===process.platform&&(t=t.replace(/\\/g,"/")),this._emitMatch(e,t),i()},_.prototype._stat=function(t,e){var r=this._makeAbs(t),n="/"===t.slice(-1);if(t.length>this.maxLength)return e();if(!this.stat&&p(this.cache,r)){var i=this.cache[r];if(Array.isArray(i)&&(i="DIR"),!n||"DIR"===i)return e(null,i);if(n&&"FILE"===i)return e()}var s=this.statCache[r];if(void 0!==s){if(!1===s)return e(null,s);var o=s.isDirectory()?"DIR":"FILE";return n&&"FILE"===o?e():e(null,o,s)}var a=this,c=d("stat\0"+r,(function(n,i){if(i&&i.isSymbolicLink())return a.fs.stat(r,(function(n,s){n?a._stat2(t,r,null,i,e):a._stat2(t,r,n,s,e)}));a._stat2(t,r,n,i,e)}));c&&a.fs.lstat(r,c)},_.prototype._stat2=function(t,e,r,n,i){if(r&&("ENOENT"===r.code||"ENOTDIR"===r.code))return this.statCache[e]=!1,i();var s="/"===t.slice(-1);if(this.statCache[e]=n,"/"===e.slice(-1)&&n&&!n.isDirectory())return i(null,!1,n);var o=!0;return n&&(o=n.isDirectory()?"DIR":"FILE"),this.cache[e]=this.cache[e]||o,s&&"FILE"===o?i():i(null,o,n)}},function(t,e,r){t.exports=l,l.realpath=l,l.sync=u,l.realpathSync=u,l.monkeypatch=function(){n.realpath=l,n.realpathSync=u},l.unmonkeypatch=function(){n.realpath=i,n.realpathSync=s};var n=r(1),i=n.realpath,s=n.realpathSync,o=process.version,a=/^v[0-5]\./.test(o),c=r(11);function h(t){return t&&"realpath"===t.syscall&&("ELOOP"===t.code||"ENOMEM"===t.code||"ENAMETOOLONG"===t.code)}function l(t,e,r){if(a)return i(t,e,r);"function"==typeof e&&(r=e,e=null),i(t,e,(function(n,i){h(n)?c.realpath(t,e,r):r(n,i)}))}function u(t,e){if(a)return s(t,e);try{return s(t,e)}catch(r){if(h(r))return c.realpathSync(t,e);throw r}}},function(t,e){t.exports=require("assert")},function(t,e,r){function n(t,e){return Object.prototype.hasOwnProperty.call(t,e)}e.setopts=function(t,e,r){r||(r={});if(r.matchBase&&-1===e.indexOf("/")){if(r.noglobstar)throw new Error("base matching requires globstar");e="**/"+e}t.windowsPathsNoEscape=!!r.windowsPathsNoEscape||!1===r.allowWindowsEscape,t.windowsPathsNoEscape&&(e=e.replace(/\\/g,"/"));t.silent=!!r.silent,t.pattern=e,t.strict=!1!==r.strict,t.realpath=!!r.realpath,t.realpathCache=r.realpathCache||Object.create(null),t.follow=!!r.follow,t.dot=!!r.dot,t.mark=!!r.mark,t.nodir=!!r.nodir,t.nodir&&(t.mark=!0);t.sync=!!r.sync,t.nounique=!!r.nounique,t.nonull=!!r.nonull,t.nosort=!!r.nosort,t.nocase=!!r.nocase,t.stat=!!r.stat,t.noprocess=!!r.noprocess,t.absolute=!!r.absolute,t.fs=r.fs||i,t.maxLength=r.maxLength||1/0,t.cache=r.cache||Object.create(null),t.statCache=r.statCache||Object.create(null),t.symlinks=r.symlinks||Object.create(null),function(t,e){t.ignore=e.ignore||[],Array.isArray(t.ignore)||(t.ignore=[t.ignore]);t.ignore.length&&(t.ignore=t.ignore.map(l))}(t,r),t.changedCwd=!1;var o=process.cwd();n(r,"cwd")?(t.cwd=s.resolve(r.cwd),t.changedCwd=t.cwd!==o):t.cwd=s.resolve(o);t.root=r.root||s.resolve(t.cwd,"/"),t.root=s.resolve(t.root),t.cwdAbs=a(t.cwd)?t.cwd:u(t,t.cwd),t.nomount=!!r.nomount,"win32"===process.platform&&(t.root=t.root.replace(/\\/g,"/"),t.cwd=t.cwd.replace(/\\/g,"/"),t.cwdAbs=t.cwdAbs.replace(/\\/g,"/"));r.nonegate=!0,r.nocomment=!0,t.minimatch=new c(e,r),t.options=t.minimatch.options},e.ownProp=n,e.makeAbs=u,e.finish=function(t){for(var e=t.nounique,r=e?[]:Object.create(null),n=0,i=t.matches.length;n<i;n++){var s=t.matches[n];if(s&&0!==Object.keys(s).length){var o=Object.keys(s);e?r.push.apply(r,o):o.forEach((function(t){r[t]=!0}))}else if(t.nonull){var a=t.minimatch.globSet[n];e?r.push(a):r[a]=!0}}e||(r=Object.keys(r));t.nosort||(r=r.sort(h));if(t.mark){for(n=0;n<r.length;n++)r[n]=t._mark(r[n]);t.nodir&&(r=r.filter((function(e){var r=!/\/$/.test(e),n=t.cache[e]||t.cache[u(t,e)];return r&&n&&(r="DIR"!==n&&!Array.isArray(n)),r})))}t.ignore.length&&(r=r.filter((function(e){return!f(t,e)})));t.found=r},e.mark=function(t,e){var r=u(t,e),n=t.cache[r],i=e;if(n){var s="DIR"===n||Array.isArray(n),o="/"===e.slice(-1);if(s&&!o?i+="/":!s&&o&&(i=i.slice(0,-1)),i!==e){var a=u(t,i);t.statCache[a]=t.statCache[r],t.cache[a]=t.cache[r]}}return i},e.isIgnored=f,e.childrenIgnored=function(t,e){return!!t.ignore.length&&t.ignore.some((function(t){return!(!t.gmatcher||!t.gmatcher.match(e))}))};var i=r(1),s=r(0),o=r(2),a=r(0).isAbsolute,c=o.Minimatch;function h(t,e){return t.localeCompare(e,"en")}function l(t){var e=null;if("/**"===t.slice(-3)){var r=t.replace(/(\/\*\*)+$/,"");e=new c(r,{dot:!0})}return{matcher:new c(t,{dot:!0}),gmatcher:e}}function u(t,e){var r=e;return r="/"===e.charAt(0)?s.join(t.root,e):a(e)||""===e?e:t.changedCwd?s.resolve(t.cwd,e):s.resolve(e),"win32"===process.platform&&(r=r.replace(/\\/g,"/")),r}function f(t,e){return!!t.ignore.length&&t.ignore.some((function(t){return t.matcher.match(e)||!(!t.gmatcher||!t.gmatcher.match(e))}))}},function(t,e){t.exports=function t(e,r){if(e&&r)return t(e)(r);if("function"!=typeof e)throw new TypeError("need wrapper function");return Object.keys(e).forEach((function(t){n[t]=e[t]})),n;function n(){for(var t=new Array(arguments.length),r=0;r<t.length;r++)t[r]=arguments[r];var n=e.apply(this,t),i=t[t.length-1];return"function"==typeof n&&n!==i&&Object.keys(i).forEach((function(t){n[t]=i[t]})),n}}},function(t,e,r){var n=r(8);function i(t){var e=function(){return e.called?e.value:(e.called=!0,e.value=t.apply(this,arguments))};return e.called=!1,e}function s(t){var e=function(){if(e.called)throw new Error(e.onceError);return e.called=!0,e.value=t.apply(this,arguments)},r=t.name||"Function wrapped with `once`";return e.onceError=r+" shouldn't be called more than once",e.called=!1,e}t.exports=n(i),t.exports.strict=n(s),i.proto=i((function(){Object.defineProperty(Function.prototype,"once",{value:function(){return i(this)},configurable:!0}),Object.defineProperty(Function.prototype,"onceStrict",{value:function(){return s(this)},configurable:!0})}))},function(t,e,r){t.exports=r(4)},function(t,e,r){var n=r(0),i="win32"===process.platform,s=r(1),o=process.env.NODE_DEBUG&&/fs/.test(process.env.NODE_DEBUG);function a(t){return"function"==typeof t?t:function(){var t;if(o){var e=new Error;t=function(t){t&&(e.message=t.message,r(t=e))}}else t=r;return t;function r(t){if(t){if(process.throwDeprecation)throw t;if(!process.noDeprecation){var e="fs: missing callback "+(t.stack||t.message);process.traceDeprecation?console.trace(e):console.error(e)}}}}()}n.normalize;if(i)var c=/(.*?)(?:[\/\\]+|$)/g;else c=/(.*?)(?:[\/]+|$)/g;if(i)var h=/^(?:[a-zA-Z]:|[\\\/]{2}[^\\\/]+[\\\/][^\\\/]+)?[\\\/]*/;else h=/^[\/]*/;e.realpathSync=function(t,e){if(t=n.resolve(t),e&&Object.prototype.hasOwnProperty.call(e,t))return e[t];var r,o,a,l,u=t,f={},p={};function d(){var e=h.exec(t);r=e[0].length,o=e[0],a=e[0],l="",i&&!p[a]&&(s.lstatSync(a),p[a]=!0)}for(d();r<t.length;){c.lastIndex=r;var g=c.exec(t);if(l=o,o+=g[0],a=l+g[1],r=c.lastIndex,!(p[a]||e&&e[a]===a)){var m;if(e&&Object.prototype.hasOwnProperty.call(e,a))m=e[a];else{var y=s.lstatSync(a);if(!y.isSymbolicLink()){p[a]=!0,e&&(e[a]=a);continue}var b=null;if(!i){var v=y.dev.toString(32)+":"+y.ino.toString(32);f.hasOwnProperty(v)&&(b=f[v])}null===b&&(s.statSync(a),b=s.readlinkSync(a)),m=n.resolve(l,b),e&&(e[a]=m),i||(f[v]=b)}t=n.resolve(m,t.slice(r)),d()}}return e&&(e[u]=t),t},e.realpath=function(t,e,r){if("function"!=typeof r&&(r=a(e),e=null),t=n.resolve(t),e&&Object.prototype.hasOwnProperty.call(e,t))return process.nextTick(r.bind(null,null,e[t]));var o,l,u,f,p=t,d={},g={};function m(){var e=h.exec(t);o=e[0].length,l=e[0],u=e[0],f="",i&&!g[u]?s.lstat(u,(function(t){if(t)return r(t);g[u]=!0,y()})):process.nextTick(y)}function y(){if(o>=t.length)return e&&(e[p]=t),r(null,t);c.lastIndex=o;var n=c.exec(t);return f=l,l+=n[0],u=f+n[1],o=c.lastIndex,g[u]||e&&e[u]===u?process.nextTick(y):e&&Object.prototype.hasOwnProperty.call(e,u)?_(e[u]):s.lstat(u,b)}function b(t,n){if(t)return r(t);if(!n.isSymbolicLink())return g[u]=!0,e&&(e[u]=u),process.nextTick(y);if(!i){var o=n.dev.toString(32)+":"+n.ino.toString(32);if(d.hasOwnProperty(o))return v(null,d[o],u)}s.stat(u,(function(t){if(t)return r(t);s.readlink(u,(function(t,e){i||(d[o]=e),v(t,e)}))}))}function v(t,i,s){if(t)return r(t);var o=n.resolve(f,i);e&&(e[s]=o),_(o)}function _(e){t=n.resolve(e,t.slice(o)),m()}m()}},function(t,e){const r="object"==typeof process&&process&&"win32"===process.platform;t.exports=r?{sep:"\\"}:{sep:"/"}},function(t,e,r){var n=r(14);t.exports=function(t){if(!t)return[];"{}"===t.substr(0,2)&&(t="\\{\\}"+t.substr(2));return m(function(t){return t.split("\\\\").join(i).split("\\{").join(s).split("\\}").join(o).split("\\,").join(a).split("\\.").join(c)}(t),!0).map(l)};var i="\0SLASH"+Math.random()+"\0",s="\0OPEN"+Math.random()+"\0",o="\0CLOSE"+Math.random()+"\0",a="\0COMMA"+Math.random()+"\0",c="\0PERIOD"+Math.random()+"\0";function h(t){return parseInt(t,10)==t?parseInt(t,10):t.charCodeAt(0)}function l(t){return t.split(i).join("\\").split(s).join("{").split(o).join("}").split(a).join(",").split(c).join(".")}function u(t){if(!t)return[""];var e=[],r=n("{","}",t);if(!r)return t.split(",");var i=r.pre,s=r.body,o=r.post,a=i.split(",");a[a.length-1]+="{"+s+"}";var c=u(o);return o.length&&(a[a.length-1]+=c.shift(),a.push.apply(a,c)),e.push.apply(e,a),e}function f(t){return"{"+t+"}"}function p(t){return/^-?0\d/.test(t)}function d(t,e){return t<=e}function g(t,e){return t>=e}function m(t,e){var r=[],i=n("{","}",t);if(!i)return[t];var s=i.pre,a=i.post.length?m(i.post,!1):[""];if(/\$$/.test(i.pre))for(var c=0;c<a.length;c++){var l=s+"{"+i.body+"}"+a[c];r.push(l)}else{var y,b,v=/^-?\d+\.\.-?\d+(?:\.\.-?\d+)?$/.test(i.body),_=/^[a-zA-Z]\.\.[a-zA-Z](?:\.\.-?\d+)?$/.test(i.body),w=v||_,E=i.body.indexOf(",")>=0;if(!w&&!E)return i.post.match(/,.*\}/)?m(t=i.pre+"{"+i.body+o+i.post):[t];if(w)y=i.body.split(/\.\./);else if(1===(y=u(i.body)).length&&1===(y=m(y[0],!1).map(f)).length)return a.map((function(t){return i.pre+y[0]+t}));if(w){var O=h(y[0]),k=h(y[1]),A=Math.max(y[0].length,y[1].length),S=3==y.length?Math.abs(h(y[2])):1,j=d;k<O&&(S*=-1,j=g);var x=y.some(p);b=[];for(var I=O;j(I,k);I+=S){var N;if(_)"\\"===(N=String.fromCharCode(I))&&(N="");else if(N=String(I),x){var R=A-N.length;if(R>0){var L=new Array(R+1).join("0");N=I<0?"-"+L+N.slice(1):L+N}}b.push(N)}}else{b=[];for(var M=0;M<y.length;M++)b.push.apply(b,m(y[M],!1))}for(M=0;M<b.length;M++)for(c=0;c<a.length;c++){l=s+b[M]+a[c];(!e||w||l)&&r.push(l)}}return r}},function(t,e,r){"use strict";function n(t,e,r){t instanceof RegExp&&(t=i(t,r)),e instanceof RegExp&&(e=i(e,r));var n=s(t,e,r);return n&&{start:n[0],end:n[1],pre:r.slice(0,n[0]),body:r.slice(n[0]+t.length,n[1]),post:r.slice(n[1]+e.length)}}function i(t,e){var r=e.match(t);return r?r[0]:null}function s(t,e,r){var n,i,s,o,a,c=r.indexOf(t),h=r.indexOf(e,c+1),l=c;if(c>=0&&h>0){for(n=[],s=r.length;l>=0&&!a;)l==c?(n.push(l),c=r.indexOf(t,l+1)):1==n.length?a=[n.pop(),h]:((i=n.pop())<s&&(s=i,o=h),h=r.indexOf(e,l+1)),l=c<h&&c>=0?c:h;n.length&&(a=[s,o])}return a}t.exports=n,n.range=s},function(t,e,r){try{var n=r(3);if("function"!=typeof n.inherits)throw"";t.exports=n.inherits}catch(e){t.exports=r(16)}},function(t,e){"function"==typeof Object.create?t.exports=function(t,e){e&&(t.super_=e,t.prototype=Object.create(e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}))}:t.exports=function(t,e){if(e){t.super_=e;var r=function(){};r.prototype=e.prototype,t.prototype=new r,t.prototype.constructor=t}}},function(t,e){t.exports=require("events")},function(t,e,r){t.exports=p,p.GlobSync=d;var n=r(5),i=r(2),s=(i.Minimatch,r(4).Glob,r(3),r(0)),o=r(6),a=r(0).isAbsolute,c=r(7),h=c.setopts,l=c.ownProp,u=c.childrenIgnored,f=c.isIgnored;function p(t,e){if("function"==typeof e||3===arguments.length)throw new TypeError("callback provided to sync glob\nSee: https://github.com/isaacs/node-glob/issues/167");return new d(t,e).found}function d(t,e){if(!t)throw new Error("must provide pattern");if("function"==typeof e||3===arguments.length)throw new TypeError("callback provided to sync glob\nSee: https://github.com/isaacs/node-glob/issues/167");if(!(this instanceof d))return new d(t,e);if(h(this,t,e),this.noprocess)return this;var r=this.minimatch.set.length;this.matches=new Array(r);for(var n=0;n<r;n++)this._process(this.minimatch.set[n],n,!1);this._finish()}d.prototype._finish=function(){if(o.ok(this instanceof d),this.realpath){var t=this;this.matches.forEach((function(e,r){var i=t.matches[r]=Object.create(null);for(var s in e)try{s=t._makeAbs(s),i[n.realpathSync(s,t.realpathCache)]=!0}catch(e){if("stat"!==e.syscall)throw e;i[t._makeAbs(s)]=!0}}))}c.finish(this)},d.prototype._process=function(t,e,r){o.ok(this instanceof d);for(var n,s=0;"string"==typeof t[s];)s++;switch(s){case t.length:return void this._processSimple(t.join("/"),e);case 0:n=null;break;default:n=t.slice(0,s).join("/")}var c,h=t.slice(s);null===n?c=".":a(n)||a(t.map((function(t){return"string"==typeof t?t:"[*]"})).join("/"))?(n&&a(n)||(n="/"+n),c=n):c=n;var l=this._makeAbs(c);u(this,c)||(h[0]===i.GLOBSTAR?this._processGlobStar(n,c,l,h,e,r):this._processReaddir(n,c,l,h,e,r))},d.prototype._processReaddir=function(t,e,r,n,i,o){var a=this._readdir(r,o);if(a){for(var c=n[0],h=!!this.minimatch.negate,l=c._glob,u=this.dot||"."===l.charAt(0),f=[],p=0;p<a.length;p++){if("."!==(m=a[p]).charAt(0)||u)(h&&!t?!m.match(c):m.match(c))&&f.push(m)}var d=f.length;if(0!==d)if(1!==n.length||this.mark||this.stat){n.shift();for(p=0;p<d;p++){var g;m=f[p];g=t?[t,m]:[m],this._process(g.concat(n),i,o)}}else{this.matches[i]||(this.matches[i]=Object.create(null));for(var p=0;p<d;p++){var m=f[p];t&&(m="/"!==t.slice(-1)?t+"/"+m:t+m),"/"!==m.charAt(0)||this.nomount||(m=s.join(this.root,m)),this._emitMatch(i,m)}}}},d.prototype._emitMatch=function(t,e){if(!f(this,e)){var r=this._makeAbs(e);if(this.mark&&(e=this._mark(e)),this.absolute&&(e=r),!this.matches[t][e]){if(this.nodir){var n=this.cache[r];if("DIR"===n||Array.isArray(n))return}this.matches[t][e]=!0,this.stat&&this._stat(e)}}},d.prototype._readdirInGlobStar=function(t){if(this.follow)return this._readdir(t,!1);var e,r;try{r=this.fs.lstatSync(t)}catch(t){if("ENOENT"===t.code)return null}var n=r&&r.isSymbolicLink();return this.symlinks[t]=n,n||!r||r.isDirectory()?e=this._readdir(t,!1):this.cache[t]="FILE",e},d.prototype._readdir=function(t,e){if(e&&!l(this.symlinks,t))return this._readdirInGlobStar(t);if(l(this.cache,t)){var r=this.cache[t];if(!r||"FILE"===r)return null;if(Array.isArray(r))return r}try{return this._readdirEntries(t,this.fs.readdirSync(t))}catch(e){return this._readdirError(t,e),null}},d.prototype._readdirEntries=function(t,e){if(!this.mark&&!this.stat)for(var r=0;r<e.length;r++){var n=e[r];n="/"===t?t+n:t+"/"+n,this.cache[n]=!0}return this.cache[t]=e,e},d.prototype._readdirError=function(t,e){switch(e.code){case"ENOTSUP":case"ENOTDIR":var r=this._makeAbs(t);if(this.cache[r]="FILE",r===this.cwdAbs){var n=new Error(e.code+" invalid cwd "+this.cwd);throw n.path=this.cwd,n.code=e.code,n}break;case"ENOENT":case"ELOOP":case"ENAMETOOLONG":case"UNKNOWN":this.cache[this._makeAbs(t)]=!1;break;default:if(this.cache[this._makeAbs(t)]=!1,this.strict)throw e;this.silent||console.error("glob error",e)}},d.prototype._processGlobStar=function(t,e,r,n,i,s){var o=this._readdir(r,s);if(o){var a=n.slice(1),c=t?[t]:[],h=c.concat(a);this._process(h,i,!1);var l=o.length;if(!this.symlinks[r]||!s)for(var u=0;u<l;u++){if("."!==o[u].charAt(0)||this.dot){var f=c.concat(o[u],a);this._process(f,i,!0);var p=c.concat(o[u],n);this._process(p,i,!0)}}}},d.prototype._processSimple=function(t,e){var r=this._stat(t);if(this.matches[e]||(this.matches[e]=Object.create(null)),r){if(t&&a(t)&&!this.nomount){var n=/[\/\\]$/.test(t);"/"===t.charAt(0)?t=s.join(this.root,t):(t=s.resolve(this.root,t),n&&(t+="/"))}"win32"===process.platform&&(t=t.replace(/\\/g,"/")),this._emitMatch(e,t)}},d.prototype._stat=function(t){var e=this._makeAbs(t),r="/"===t.slice(-1);if(t.length>this.maxLength)return!1;if(!this.stat&&l(this.cache,e)){var n=this.cache[e];if(Array.isArray(n)&&(n="DIR"),!r||"DIR"===n)return n;if(r&&"FILE"===n)return!1}var i=this.statCache[e];if(!i){var s;try{s=this.fs.lstatSync(e)}catch(t){if(t&&("ENOENT"===t.code||"ENOTDIR"===t.code))return this.statCache[e]=!1,!1}if(s&&s.isSymbolicLink())try{i=this.fs.statSync(e)}catch(t){i=s}else i=s}this.statCache[e]=i;n=!0;return i&&(n=i.isDirectory()?"DIR":"FILE"),this.cache[e]=this.cache[e]||n,(!r||"FILE"!==n)&&n},d.prototype._mark=function(t){return c.mark(this,t)},d.prototype._makeAbs=function(t){return c.makeAbs(this,t)}},function(t,e,r){var n=r(8),i=Object.create(null),s=r(9);function o(t){for(var e=t.length,r=[],n=0;n<e;n++)r[n]=t[n];return r}t.exports=n((function(t,e){return i[t]?(i[t].push(e),null):(i[t]=[e],function(t){return s((function e(){var r=i[t],n=r.length,s=o(arguments);try{for(var a=0;a<n;a++)r[a].apply(null,s)}finally{r.length>n?(r.splice(0,n),process.nextTick((function(){e.apply(null,s)}))):delete i[t]}}))}(t))}))}]));