{"name": "@types/ws", "version": "8.5.5", "description": "TypeScript definitions for ws", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/ws", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/loyd", "githubUsername": "loyd"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mlamp", "githubUsername": "mlamp"}, {"name": "<PERSON>", "url": "https://github.com/TitaneBoy", "githubUsername": "TitaneBoy"}, {"name": "reduckted", "url": "https://github.com/reduckted", "githubUsername": "reduckted"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/teidesu", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/wojtkowiak", "githubUsername": "w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/k-yle", "githubUsername": "k-yle"}, {"name": "<PERSON>", "url": "https://github.com/cwadrupldijjit", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/ws"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "6eea0ac9982d9cb29e842a5be3f7d3e0103c444a7b96a324c0ce4a86980da93f", "typeScriptVersion": "4.3", "exports": {".": {"types": {"import": "./index.d.mts", "default": "./index.d.ts"}}, "./package.json": "./package.json"}}