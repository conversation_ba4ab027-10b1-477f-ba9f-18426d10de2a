{"name": "@vladfrangu/async_event_emitter", "version": "2.2.2", "description": "An event emitter implementation with async support in mind", "main": "./dist/index.js", "module": "./dist/index.mjs", "browser": "./dist/index.global.js", "unpkg": "./dist/index.global.js", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js", "types": "./dist/index.d.ts"}}, "sideEffects": false, "author": "<PERSON> <<EMAIL>>", "license": "MIT", "scripts": {"lint": "eslint src tests --ext ts --fix", "format": "prettier --write \"{src,tests}/**/*.ts\"", "docs": "typedoc", "test": "vitest run", "test:watch": "vitest", "update": "yarn upgrade-interactive", "build": "tsup && tsc -b src", "clean": "node scripts/clean.mjs", "typecheck": "tsc -p tsconfig.typecheck.json", "bump": "cliff-jumper", "check-update": "cliff-jumper --dry-run", "_postinstall": "husky install .github/husky", "prepack": "rollup-type-bundler && pinst --disable", "postpack": "pinst --enable"}, "devDependencies": {"@commitlint/cli": "^17.4.4", "@commitlint/config-conventional": "^17.4.4", "@favware/cliff-jumper": "^1.10.0", "@favware/npm-deprecate": "^1.0.7", "@favware/rollup-type-bundler": "^2.0.0", "@sapphire/eslint-config": "^4.3.8", "@sapphire/prettier-config": "^1.4.5", "@sapphire/ts-config": "^3.3.4", "@typescript-eslint/eslint-plugin": "^5.52.0", "@typescript-eslint/parser": "^5.52.0", "@vitest/coverage-c8": "^0.28.5", "cz-conventional-changelog": "^3.3.0", "eslint": "^8.34.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-prettier": "^4.2.1", "husky": "^8.0.3", "lint-staged": "^13.1.2", "pinst": "^3.0.0", "prettier": "^2.8.4", "pretty-quick": "^3.1.3", "tsup": "^6.6.3", "typedoc": "^0.23.25", "typedoc-plugin-mdn-links": "^2.0.2", "typescript": "^4.9.5", "vitest": "^0.28.5"}, "repository": {"type": "git", "url": "git+https://github.com/vladfrangu/async_event_emitter.git"}, "files": ["dist", "!dist/*.tsbuildinfo", "THIRD_PARTY_LICENSE.md"], "engines": {"node": ">=v14.0.0", "npm": ">=7.0.0"}, "keywords": ["event emitter", "async", "event", "emitter"], "bugs": {"url": "https://github.com/vladfrangu/async_event_emitter/issues"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "lint-staged": {"*.{mjs,js,ts}": "eslint --fix --ext mjs,js,ts"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "publishConfig": {"access": "public"}, "prettier": "@sapphire/prettier-config", "resolutions": {"ansi-regex": "^5.0.1", "minimist": "^1.2.6"}, "packageManager": "yarn@3.2.1"}