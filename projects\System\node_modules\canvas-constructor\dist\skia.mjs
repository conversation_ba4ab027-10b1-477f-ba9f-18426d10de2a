import{deprecate as t}from"node:util";import{loadImage as e,Canvas as r,FontLibrary as n}from"skia-canvas";export{FontLibrary,Image,Path2D,loadImage}from"skia-canvas";class i{constructor(t,e){"number"==typeof t?(this.canvas=new r(t,e),this.context=this.canvas.getContext("2d")):(this.canvas=t,this.context=null!=e?e:this.canvas.getContext("2d"))}addPage(t,e){return new i(this.canvas,this.canvas.newPage(t,e))}get width(){return this.canvas.width}set width(t){this.canvas.width=t}get height(){return this.canvas.height}set height(t){this.canvas.height=t}get direction(){return this.context.direction}get font(){return this.context.font}get globalAlpha(){return this.context.globalAlpha}get imageSmoothingEnabled(){return this.context.imageSmoothingEnabled}get imageSmoothingQuality(){return this.context.imageSmoothingQuality}get pages(){return this.canvas.pages}get fontVariant(){return this.context.fontVariant}get textTracking(){return this.context.textTracking}get textWrap(){return this.context.textWrap}get transform(){return this.context.getTransform()}get textFontHeight(){return y(this.context.font)}get lineDash(){return this.context.getLineDash()}changeCanvasSize(t,e){return this.changeCanvasWidth(t).changeCanvasHeight(e)}changeCanvasWidth(t){return this.width=t,this}changeCanvasHeight(t){return this.height=t,this}save(){return this.context.save(),this}restore(){return this.context.restore(),this}rotate(t){return this.context.rotate(t),this}scale(t,e){return this.context.scale(t,e),this}translate(t,e){return this.context.translate(t,e),this}clip(...t){return this.context.clip(...t),this}setTransform(...t){return this.context.setTransform(...t),this}resetTransform(){return this.context.resetTransform(),this}resetFilters(){return this.setFilter("none")}getImageData(t,e,r,n){return this.context.getImageData(null!=t?t:0,null!=e?e:0,null!=r?r:this.width,null!=n?n:this.height)}putImageData(...t){return this.context.putImageData(...t),this}fill(...t){return this.context.fill(...t),this}printText(t,e,r,...n){return this.context.fillText(t,e,r,...n),this}printResponsiveText(t,e,r,n){const[i,s,a]=this.parseFont(this.context.font);if("number"!=typeof s)return this.printText(t,e,r);const{width:o}=this.measureText(t);if(o<=n)return this.printText(t,e,r);const h=n/o*s;return this.save().setTextFont(`${i}${h}${a}`).printText(t,e,r).restore()}printMultilineText(t,e,r){const n=t.split(/\r?\n/);if(n.length<=1)return this.printText(t,e,r);const i=this.textFontHeight;let s=r;for(const t of n)this.printText(t,e,Math.floor(s)),s+=i;return this}printWrappedText(t,e,r,n){const i=$(this,t,n);return this.printMultilineText(i,e,r)}stroke(){return this.context.stroke(),this}printStrokeRectangle(t,e,r,n){return this.context.strokeRect(t,e,r,n),this}printStrokeText(t,e,r,n){return this.context.strokeText(t,e,r,n),this}measureText(t){return this.context.measureText(t)}setTextSize(t){const e=this.parseFont(this.context.font);return 1===e.length?this:this.setTextFont(`${e[0]}${t}${e[2]}`)}setStroke(t){return this.context.strokeStyle=t,this}setLineWidth(t){return this.context.lineWidth=t,this}setStrokeWidth(t){return this.setLineWidth(t)}setLineDashOffset(t){return this.context.lineDashOffset=t,this}setLineJoin(t){return this.context.lineJoin=t,this}setLineCap(t){return this.context.lineCap=t,this}setLineDash(t){return this.context.setLineDash(t),this}printImage(t,...e){var r;return this.context.drawImage((r=t)instanceof i?r.canvas:r,...e),this}printCircularImage(t,e,r,n,{fit:i="fill"}={}){const{positionX:s,positionY:a,sizeX:o,sizeY:h}=this.resolveCircularCoordinates(t,e,r,n,i);return this.save().createCircularClip(e,r,n,0,2*Math.PI,!1).printImage(t,s,a,o,h).restore()}printRoundedImage(t,e,r,n,i,s){return this.save().createRoundedClip(e,r,n,i,s).printImage(t,e,r,n,i).restore()}printCircle(t,e,r){return this.save().createCircularPath(t,e,r).fill().restore()}printRectangle(t,e,r,n){return this.context.fillRect(t,e,r,n),this}printRoundedRectangle(t,e,r,n,i){return this.save().createRoundedPath(t,e,r,n,i).fill().restore()}createCircularPath(t,e,r,n=0,i=2*Math.PI,s=!1){return this.context.beginPath(),this.context.arc(t,e,r,n,i,s),this}createCircularClip(t,e,r,n,i,s){return this.createCircularPath(t,e,r,n,i,s).clip()}createRectanglePath(t,e,r,n){return this.context.rect(t,e,r,n),this}createRectangleClip(t,e,r,n){return this.createRectanglePath(t,e,r,n).clip()}createRoundedPath(t,e,r,n,i){if(r>0&&n>0){let s;"number"==typeof i?s={tl:i=Math.min(i,r/2,n/2),tr:i,br:i,bl:i}:(s=i,i=Math.min(5,r/2,n/2));const{tl:a=i,tr:o=i,br:h=i,bl:c=i}=s;this.context.beginPath(),this.context.moveTo(t+a,e),this.context.lineTo(t+r-o,e),this.context.quadraticCurveTo(t+r,e,t+r,e+o),this.context.lineTo(t+r,e+n-h),this.context.quadraticCurveTo(t+r,e+n,t+r-h,e+n),this.context.lineTo(t+c,e+n),this.context.quadraticCurveTo(t,e+n,t,e+n-c),this.context.lineTo(t,e+a),this.context.quadraticCurveTo(t,e,t+a,e),this.context.closePath()}return this}createRoundedClip(t,e,r,n,i){return this.createRoundedPath(t,e,r,n,i).clip()}setColor(t){return this.context.fillStyle=t,this}setTextFont(t){return this.context.font=t,this}setTextAlign(t){return this.context.textAlign=t,this}setTextBaseline(t){return this.context.textBaseline=t,this}setFilter(t){return this.context.filter=t,this}beginPath(){return this.context.beginPath(),this}closePath(){return this.context.closePath(),this}createPattern(t,e){return this.context.createPattern((r=t)instanceof i?r.canvas:r,e);var r}printPattern(t,e){return this.setColor(this.createPattern(t,e))}createLinearGradient(t,e,r,n,i=[]){const s=this.context.createLinearGradient(t,e,r,n);for(const t of i)s.addColorStop(t.position,t.color);return s}printLinearColorGradient(t,e,r,n,i){const s=this.createLinearGradient(t,e,r,n,i);return this.setColor(s)}printLinearStrokeGradient(t,e,r,n,i){const s=this.createLinearGradient(t,e,r,n,i);return this.setStroke(s)}createRadialGradient(t,e,r,n,i,s,a=[]){const o=this.context.createRadialGradient(t,e,r,n,i,s);for(const t of a)o.addColorStop(t.position,t.color);return o}printRadialColorGradient(t,e,r,n,i,s,a){const o=this.createRadialGradient(t,e,r,n,i,s,a);return this.setColor(o)}printRadialStrokeGradient(t,e,r,n,i,s,a){const o=this.createRadialGradient(t,e,r,n,i,s,a);return this.setStroke(o)}createConicGradient(t,e,r,n=[]){const i=this.context.createConicGradient(t,e,r);for(const t of n)i.addColorStop(t.position,t.color);return i}printConicColorGradient(t,e,r,n){const i=this.createConicGradient(t,e,r,n);return this.setColor(i)}printConicStrokeGradient(t,e,r,n){const i=this.createConicGradient(t,e,r,n);return this.setStroke(i)}createEllipsePath(t,e,r,n,i,s,a,o){return this.context.ellipse(t,e,r,n,i,s,a,o),this}createEllipseClip(t,e,r,n,i,s,a,o){return this.createEllipsePath(t,e,r,n,i,s,a,o).clip()}arc(t,e,r,n,i,s){return this.context.arc(t,e,r,n,i,s),this}arcTo(t,e,r,n,i){return this.context.arcTo(t,e,r,n,i),this}quadraticCurveTo(t,e,r,n){return this.context.quadraticCurveTo(t,e,r,n),this}bezierCurveTo(t,e,r,n,i,s){return this.context.bezierCurveTo(t,e,r,n,i,s),this}lineTo(t,e){return this.context.lineTo(t,e),this}moveTo(t,e){return this.context.moveTo(t,e),this}setShadowBlur(t){return this.context.shadowBlur=t,this}setShadowColor(t){return this.context.shadowColor=t,this}setShadowOffsetX(t){return this.context.shadowOffsetX=t,this}setShadowOffsetY(t){return this.context.shadowOffsetY=t,this}setMiterLimit(t){return this.context.miterLimit=t,this}setGlobalCompositeOperation(t){return this.context.globalCompositeOperation=t,this}setGlobalAlpha(t){return this.context.globalAlpha=t,this}setImageSmoothingEnabled(t){return this.context.imageSmoothingEnabled=t,this}setImageSmoothingQuality(t){return this.context.imageSmoothingQuality=t,this}setFontVariant(t){return this.context.fontVariant=t,this}setTextTracking(t){return this.context.textTracking=t,this}setTextWrap(t){return this.context.textWrap=t,this}resetShadows(){return this.setShadowBlur(0).setShadowOffsetX(0).setShadowOffsetY(0).setShadowColor("transparent")}clearCircle(t,e,r,n=0,i=2*Math.PI,s=!1){return this.createCircularClip(t,e,r,n,i,s).clearRectangle(t-r,e-r,2*r,2*r)}clearRectangle(t=0,e=0,r=this.width,n=this.height){return this.context.clearRect(t,e,r,n),this}isPointInPath(...t){return this.context.isPointInPath(...t)}isPointInStroke(...t){return this.context.isPointInStroke(...t)}process(t,...e){return t.call(this,this,...e),this}wrapText(t,e){return $(this,t,e)}createImageData(...t){return this.context.createImageData(...t)}jpeg(t){return this.canvas.toBufferSync("jpeg",t)}jpegAsync(t){return this.canvas.toBuffer("jpeg",t)}png(t){return this.canvas.toBufferSync("png",t)}pngAsync(t){return this.canvas.toBuffer("png",t)}pdf(t){return this.canvas.toBufferSync("pdf",t)}pdfAsync(t){return this.canvas.toBuffer("pdf",t)}svg(t){return this.canvas.toBufferSync("svg",t)}svgAsync(t){return this.canvas.toBuffer("svg",t)}toBuffer(...t){return this.canvas.toBufferSync(...t)}toBufferAsync(...t){return this.canvas.toBuffer(...t)}toDataURL(...t){return this.canvas.toDataURL(...t)}saveAs(...t){return this.canvas.saveAsSync(...t),this}async saveAsAsync(...t){return await this.canvas.saveAs(...t),this}parseFont(t){const e=R.exec(t);return null===e?[t]:[t.slice(0,e.index),Number(e[1]),t.slice(e.index+e[1].length)]}resolveCircularCoordinates(t,e,r,n,i){const{width:s,height:a}=t;if("none"===i)return{positionX:e-s/2,positionY:r-a/2,sizeX:s,sizeY:a};const o=s/a,h=2*n;if("fill"===i||1===o)return{positionX:e-n,positionY:r-n,sizeX:h,sizeY:h};if("contain"===i)return o>1?{positionX:e-n,positionY:r-n/o,sizeX:h,sizeY:h/o}:{positionX:e-n*o,positionY:r-n,sizeX:h*o,sizeY:h};if(o>1){const t=h*o;return{positionX:e-t/2,positionY:r-h/2,sizeX:t,sizeY:h}}const c=h/o;return{positionX:e-h/2,positionY:r-c/2,sizeX:h,sizeY:c}}}const s=t(e,"resolveImage() is deprecated. Use loadImage() instead.");function a(...t){return n.use(...t)}const o=t(a,"registerFont() is deprecated. Use loadFont() instead.");function h(...t){return t.join(" ")}const c=t=>t.save().setGlobalCompositeOperation("difference").setColor("white").printRectangle(0,0,t.width,t.height).restore(),u=t=>{const e=t.getImageData(),{data:r}=e;for(let t=0;t<r.length;t+=4){const e=.2126*r[t]+.7152*r[t+1]+.0722*r[t+2];r[t]=e,r[t+1]=e,r[t+2]=e}return t.putImageData(e,0,0)},l=u,g=t=>{const e=t.getImageData(),{data:r}=e;for(let t=0;t<r.length;t+=4){const e=255-(.2126*r[t]+.7152*r[t+1]+.0722*r[t+2]);r[t]=e,r[t+1]=e,r[t+2]=e}return t.putImageData(e,0,0)},p=g,x=t=>{const e=t.getImageData(),{data:r}=e;for(let t=0;t<r.length;t+=4){const e=r[t],n=r[t+1],i=r[t+2];r[t]=.393*e+.769*n+.189*i,r[t+1]=.349*e+.686*n+.168*i,r[t+2]=.272*e+.534*n+.131*i}return t.putImageData(e,0,0)},d=t=>{const e=t.getImageData(),{data:r}=e;for(let t=0;t<r.length;t+=4)r[t]=0,r[t+1]=0,r[t+2]=0;return t.putImageData(e,0,0)},f=(t,e)=>{const r=t.getImageData(),{data:n}=r;for(let t=0;t<n.length;t+=4){const r=.2126*n[t]+.7152*n[t+1]+.0722*n[t+2]>=e?255:0;n[t]=r,n[t+1]=r,n[t+2]=r}return t.putImageData(r,0,0)},m=(t,e)=>{const r=t.getImageData(),{data:n}=r;for(let t=0;t<n.length;t+=4){const r=.2126*n[t]+.7152*n[t+1]+.0722*n[t+2]>=e?0:255;n[t]=r,n[t+1]=r,n[t+2]=r}return t.putImageData(r,0,0)},v=(t,e)=>{const r=t.getImageData(),{data:n}=r;for(let t=0;t<n.length;t+=4)n[t]+=e,n[t+1]+=e,n[t+2]+=e;return t.putImageData(r,0,0)},C=(t,e)=>{const r=t.getImageData(),{data:n}=r;for(let t=0;t<n.length;t+=4)n[t]-=e,n[t+1]-=e,n[t+2]-=e;return t.putImageData(r,0,0)},T=C,I=(t,e,r=!0)=>{const n=Math.round(Math.sqrt(e.length)),i=Math.floor(n/2),s=t.getImageData(),a=s.data,o=s.width,h=s.height,c=o,u=h,l=t.getImageData(),g=l.data,p=r?1:0;for(let t=0;t<u;t++)for(let r=0;r<c;r++){const s=t,u=r,l=4*(t*c+r);let x=0,d=0,f=0,m=0;for(let t=0;t<n;t++)for(let r=0;r<n;r++){const c=s+t-i,l=u+r-i;if(c>=0&&c<h&&l>=0&&l<o){const i=4*(c*o+l),s=e[t*n+r];x+=a[i]*s,d+=a[i+1]*s,f+=a[i+2]*s,m+=a[i+3]*s}}g[l]=x,g[l+1]=d,g[l+2]=f,g[l+3]=m+p*(255-m)}return t.putImageData(l,0,0)},S=[0,-1,0,-1,4,-1,0,-1,0],b=t=>I(t,S,!0),w=[0,-1,0,-1,5,-1,0,-1,0],D=(t,e=1)=>{for(let r=0;r<e;++r)I(t,w,!0);return t},P=[1/9,1/9,1/9,1/9,1/9,1/9,1/9,1/9,1/9],k=(t,e=1)=>{for(let r=0;r<e;++r)I(t,P,!0);return t},R=/([\d.]+)(px|pt|pc|in|cm|mm|%|em|ex|ch|rem|q)/i,y=(()=>{const t=new Map;return e=>{const r=t.get(e);if(r)return r;const n=R.exec(e);if(!n)return 0;let i=Number(n[1]);switch(n[2]){case"pt":i/=.75;break;case"pc":i*=16;break;case"in":i*=96;break;case"cm":i*=96/2.54;break;case"mm":i*=96/25.4;break;case"em":case"rem":i*=16/.75;break;case"q":i*=96/25.4/4}return t.set(e,i),i}})(),$=(t,e,r)=>{const n=[],i=[],s=t.measureText(" ").width;for(const a of e.split(/\r?\n/)){let e=r;for(const o of a.split(" ")){const a=t.measureText(o).width,h=a+s;h>e?(i.length&&(n.push(i.join(" ")),i.length=0),i.push(o),e=r-a):(e-=h,i.push(o))}i.length&&(n.push(i.join(" ")),i.length=0)}return n.join("\n")},G=(t,...e)=>`${t}(${e.join(" ")})`,L=t=>`#${t}`,z=(t,e,r)=>`rgb(${t}, ${e}, ${r})`,B=(t,e,r,n)=>`rgba(${t}, ${e}, ${r}, ${n})`,X=(t,e,r)=>`hsl(${t}, ${e}%, ${r}%)`,Y=(t,e,r,n)=>`hsla(${t}, ${e}%, ${r}%, ${n})`,A=t=>t;export{i as Canvas,k as blur,v as brightness,A as color,I as convolute,C as darkness,b as edge,G as filter,R as fontRegExp,h as fontVariant,y as getFontHeight,l as grayscale,u as greyscale,L as hex,X as hsl,Y as hsla,c as invert,g as invertGrayscale,p as invertGreyscale,m as invertedThreshold,a as loadFont,T as myOldFriend,o as registerFont,s as resolveImage,z as rgb,B as rgba,x as sepia,D as sharpen,d as silhouette,$ as textWrap,f as threshold};
//# sourceMappingURL=skia.mjs.map
