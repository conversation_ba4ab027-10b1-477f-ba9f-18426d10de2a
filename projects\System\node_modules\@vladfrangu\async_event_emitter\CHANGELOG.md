# Changelog
All notable changes to this project will be documented in this file.

# [2.2.2](https://github.com/vladfrangu/async_event_emitter/compare/v2.2.1...v2.2.2) - (2023-05-11)

## 🐛 Bug Fixes

- Listener count was always 0 for one listener ([9b78e19](https://github.com/vladfrangu/async_event_emitter/commit/9b78e1992db649004dee852359240b3d0baaac2d))

# [2.2.1](https://github.com/vladfrangu/async_event_emitter/compare/v2.2.0...v2.2.1) - (2023-04-08)

## 🐛 Bug Fixes

- Include comment for throwing error on emit ([19de045](https://github.com/vladfrangu/async_event_emitter/commit/19de0452702a0d9e35e9241259d100ca6d6f5447))

# [2.2.0](https://github.com/vladfrangu/async_event_emitter/compare/v2.1.4...v2.2.0) - (2023-04-07)

## 🚀 Features

- Speed 🚀 ([23eb908](https://github.com/vladfrangu/async_event_emitter/commit/23eb90852ff8a6ceb4d6105c6df44c646642efae))

# [2.1.4](https://github.com/vladfrangu/async_event_emitter/compare/v2.1.3...v2.1.4) - (2023-02-18)

## 🐛 Bug Fixes

- Remove predefined error event to allow extensions ([4224bbe](https://github.com/vladfrangu/async_event_emitter/commit/4224bbeae5c25cb94d4073600a9dff7ae3abcceb))

# [2.1.2](https://github.com/vladfrangu/async_event_emitter/compare/v2.1.1...v2.1.2) - (2022-09-19)

## 🐛 Bug Fixes

- Don't use any `@types/node` types ([e4babce](https://github.com/vladfrangu/async_event_emitter/commit/e4babce88c17befdb6f84c73c0de2e0602260681))

# [2.1.1](https://github.com/vladfrangu/async_event_emitter/compare/v2.1.0...v2.1.1) - (2022-09-19)

## 🐛 Bug Fixes

- Correct type errors when building with other types too ([72a03ae](https://github.com/vladfrangu/async_event_emitter/commit/72a03ae1ac30456241b4003a7c2ea93d27e8de5e))

# [2.1.0](https://github.com/vladfrangu/async_event_emitter/compare/v2.0.1...v2.1.0) - (2022-09-18)

## 🚀 Features

- Bring in line with nodejs EventEmitters ([5a14ed0](https://github.com/vladfrangu/async_event_emitter/commit/5a14ed04bf87ec6a34cd33e26e3f25f101f87bcd))

# [2.0.1](https://github.com/vladfrangu/async_event_emitter/compare/v2.0.0...v2.0.1) - (2022-07-09)

## 🐛 Bug Fixes

- Error event not properly emitting ([b849b38](https://github.com/vladfrangu/async_event_emitter/commit/b849b387c36515c60234c06681bfd4ec32ee5336))

# [2.0.0](https://github.com/vladfrangu/async_event_emitter/compare/v1.0.1...v2.0.0) - (2022-06-29)

## 🚀 Features

- Use stringified bigints instead of uuids for promise map ([8c69419](https://github.com/vladfrangu/async_event_emitter/commit/8c694199da1a0a231feb1be3b0d7cfdb18cefd0b))

   ### 💥 Breaking Changes:


# Changelog

All notable changes to this project will be documented in this file.
