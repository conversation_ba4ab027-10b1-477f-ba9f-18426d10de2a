{"version": 3, "file": "objectid.d.ts", "sourceRoot": "", "sources": ["../src/objectid.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,cAAc,CAAC;AAWzC,cAAc;AACd,MAAM,WAAW,YAAY;IAC3B,EAAE,EAAE,MAAM,GAAG,UAAU,CAAC;IACxB,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,WAAW,IAAI,MAAM,CAAC;CACvB;AAED,cAAc;AACd,MAAM,WAAW,gBAAgB;IAC/B,IAAI,EAAE,MAAM,CAAC;CACd;AAED,QAAA,MAAM,GAAG,eAAe,CAAC;AAEzB;;;;GAIG;AACH,qBAAa,QAAS,SAAQ,SAAS;IACrC,IAAI,SAAS,IAAI,UAAU,CAE1B;IAED,gBAAgB;IAChB,OAAO,CAAC,MAAM,CAAC,KAAK,CAAwC;IAE5D,MAAM,CAAC,cAAc,EAAE,OAAO,CAAC;IAE/B,+BAA+B;IAC/B,OAAO,CAAC,CAAC,GAAG,CAAC,CAAc;IAC3B,yCAAyC;IACzC,OAAO,CAAC,IAAI,CAAC,CAAS;IAEtB;;;;OAIG;gBACS,OAAO,CAAC,EAAE,MAAM,GAAG,MAAM,GAAG,QAAQ,GAAG,YAAY,GAAG,UAAU;IAkD5E;;;OAGG;IACH,IAAI,EAAE,IAAI,UAAU,CAEnB;IAED,IAAI,EAAE,CAAC,KAAK,EAAE,UAAU,EAKvB;IAED,0EAA0E;IAC1E,WAAW,IAAI,MAAM;IAcrB;;;OAGG;IACH,OAAO,CAAC,MAAM,CAAC,MAAM;IAIrB;;;;OAIG;IACH,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,MAAM,GAAG,UAAU;IA+B1C;;;OAGG;IACH,QAAQ,CAAC,QAAQ,CAAC,EAAE,KAAK,GAAG,QAAQ,GAAG,MAAM;IAO7C,uEAAuE;IACvE,MAAM,IAAI,MAAM;IAIhB;;;;OAIG;IACH,MAAM,CAAC,OAAO,EAAE,MAAM,GAAG,QAAQ,GAAG,YAAY,GAAG,OAAO;IAuC1D,0FAA0F;IAC1F,YAAY,IAAI,IAAI;IAOpB,gBAAgB;IAChB,MAAM,CAAC,QAAQ,IAAI,QAAQ;IAI3B;;;;OAIG;IACH,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,MAAM,GAAG,QAAQ;IAQ7C;;;;OAIG;IACH,MAAM,CAAC,mBAAmB,CAAC,SAAS,EAAE,MAAM,GAAG,QAAQ;IAQvD,wDAAwD;IACxD,MAAM,CAAC,gBAAgB,CAAC,MAAM,EAAE,MAAM,GAAG,QAAQ;IAQjD;;;;OAIG;IACH,MAAM,CAAC,OAAO,CAAC,EAAE,EAAE,MAAM,GAAG,MAAM,GAAG,QAAQ,GAAG,YAAY,GAAG,UAAU,GAAG,OAAO;IAWnF,gBAAgB;IAChB,cAAc,IAAI,gBAAgB;IAKlC,gBAAgB;IAChB,MAAM,CAAC,gBAAgB,CAAC,GAAG,EAAE,gBAAgB,GAAG,QAAQ;IAcxD,OAAO,IAAI,MAAM;CAGlB"}