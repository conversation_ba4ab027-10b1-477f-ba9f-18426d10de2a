{"version": 3, "file": "bson.d.ts", "sourceRoot": "", "sources": ["../src/bson.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,UAAU,CAAC;AACxC,OAAO,EAAE,IAAI,EAAE,MAAM,QAAQ,CAAC;AAC9B,OAAO,EAAE,KAAK,EAAE,MAAM,UAAU,CAAC;AACjC,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAC1C,OAAO,EAAE,MAAM,EAAE,MAAM,UAAU,CAAC;AAClC,OAAO,EAAE,KAAK,EAAE,MAAM,UAAU,CAAC;AACjC,OAAO,EAAE,IAAI,EAAE,MAAM,QAAQ,CAAC;AAC9B,OAAO,EAAE,MAAM,EAAE,MAAM,WAAW,CAAC;AACnC,OAAO,EAAE,MAAM,EAAE,MAAM,WAAW,CAAC;AACnC,OAAO,EAAE,QAAQ,EAAE,MAAM,YAAY,CAAC;AAGtC,OAAO,EAAuB,kBAAkB,EAAE,MAAM,uBAAuB,CAAC;AAChF,OAAO,EAAiB,gBAAgB,EAAE,MAAM,qBAAqB,CAAC;AACtE,OAAO,EAAE,UAAU,EAAE,MAAM,UAAU,CAAC;AACtC,OAAO,EAAE,UAAU,EAAE,MAAM,UAAU,CAAC;AACtC,OAAO,EAAE,SAAS,EAAE,MAAM,aAAa,CAAC;AAExC,YAAY,EAAE,YAAY,EAAE,cAAc,EAAE,oBAAoB,EAAE,cAAc,EAAE,MAAM,UAAU,CAAC;AACnG,YAAY,EAAE,YAAY,EAAE,MAAM,QAAQ,CAAC;AAC3C,YAAY,EAAE,SAAS,EAAE,MAAM,UAAU,CAAC;AAC1C,YAAY,EAAE,kBAAkB,EAAE,MAAM,cAAc,CAAC;AACvD,YAAY,EAAE,cAAc,EAAE,MAAM,UAAU,CAAC;AAC/C,YAAY,EAAE,YAAY,EAAE,MAAM,iBAAiB,CAAC;AACpD,YAAY,EAAE,aAAa,EAAE,MAAM,UAAU,CAAC;AAC9C,YAAY,EAAE,YAAY,EAAE,MAAM,QAAQ,CAAC;AAC3C,YAAY,EAAE,cAAc,EAAE,MAAM,WAAW,CAAC;AAChD,YAAY,EAAE,cAAc,EAAE,MAAM,WAAW,CAAC;AAChD,YAAY,EAAE,gBAAgB,EAAE,YAAY,EAAE,MAAM,YAAY,CAAC;AACjE,YAAY,EAAE,kBAAkB,EAAE,wBAAwB,EAAE,MAAM,UAAU,CAAC;AAC7E,YAAY,EAAE,kBAAkB,EAAE,MAAM,UAAU,CAAC;AACnD,YAAY,EAAE,oBAAoB,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,MAAM,aAAa,CAAC;AAC/F,YAAY,EAAE,yBAAyB,EAAE,MAAM,aAAa,CAAC;AAC7D,YAAY,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,CAAC;AAErD,OAAO,EACL,IAAI,EACJ,UAAU,EACV,KAAK,EACL,MAAM,EACN,QAAQ,EACR,IAAI,EACJ,IAAI,EACJ,SAAS,EACT,MAAM,EACN,KAAK,EACL,MAAM,EACN,MAAM,EACN,UAAU,EACV,UAAU,EACX,CAAC;AACF,OAAO,EAAE,SAAS,EAAE,MAAM,cAAc,CAAC;AACzC,OAAO,EAAE,SAAS,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,MAAM,SAAS,CAAC;AACxE,OAAO,EAAE,QAAQ,EAAE,MAAM,aAAa,CAAC;AACvC,OAAO,EAAE,KAAK,EAAE,MAAM,iBAAiB,CAAC;AAExC,cAAc;AACd,MAAM,WAAW,QAAQ;IAEvB,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAC;CACpB;AASD;;;;;GAKG;AACH,wBAAgB,qBAAqB,CAAC,IAAI,EAAE,MAAM,GAAG,IAAI,CAKxD;AAED;;;;;;GAMG;AACH,wBAAgB,SAAS,CAAC,MAAM,EAAE,QAAQ,EAAE,OAAO,GAAE,gBAAqB,GAAG,UAAU,CAmCtF;AAED;;;;;;;;GAQG;AACH,wBAAgB,2BAA2B,CACzC,MAAM,EAAE,QAAQ,EAChB,WAAW,EAAE,UAAU,EACvB,OAAO,GAAE,gBAAqB,GAC7B,MAAM,CAyBR;AAED;;;;;;GAMG;AACH,wBAAgB,WAAW,CAAC,MAAM,EAAE,UAAU,EAAE,OAAO,GAAE,kBAAuB,GAAG,QAAQ,CAE1F;AAED,cAAc;AACd,MAAM,MAAM,0BAA0B,GAAG,IAAI,CAC3C,gBAAgB,EAChB,oBAAoB,GAAG,iBAAiB,CACzC,CAAC;AAEF;;;;;;GAMG;AACH,wBAAgB,mBAAmB,CACjC,MAAM,EAAE,QAAQ,EAChB,OAAO,GAAE,0BAA+B,GACvC,MAAM,CASR;AAED;;;;;;;;;;;GAWG;AACH,wBAAgB,iBAAiB,CAC/B,IAAI,EAAE,UAAU,GAAG,WAAW,EAC9B,UAAU,EAAE,MAAM,EAClB,iBAAiB,EAAE,MAAM,EACzB,SAAS,EAAE,QAAQ,EAAE,EACrB,aAAa,EAAE,MAAM,EACrB,OAAO,EAAE,kBAAkB,GAC1B,MAAM,CA0BR"}