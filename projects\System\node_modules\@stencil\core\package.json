{"name": "@stencil/core", "version": "2.22.3", "license": "MIT", "main": "./internal/stencil-core/index.cjs", "module": "./internal/stencil-core/index.js", "types": "./internal/stencil-core/index.d.ts", "bin": {"stencil": "bin/stencil"}, "files": ["!**/*.map", "!**/*.stub.ts", "!**/*.stub.tsx", "bin/", "cli/", "compiler/", "dependencies.json", "dev-server/", "internal/", "mock-doc/", "screenshot/", "sys/", "testing/"], "scripts": {"build": "npm run tsc.scripts && npm run tsc.prod && npm run rollup.prod.ci", "clean": "rm -rf build/ cli/ compiler/ dev-server/ internal/ mock-doc/ sys/ testing/ && npm run clean-scripts", "clean-scripts": "rm -rf scripts/build", "license": "node scripts/build --license", "lint": "eslint 'bin/*' 'scripts/*.ts' 'scripts/**/*.ts' 'src/*.ts' 'src/**/*.ts' 'src/**/*.tsx'", "prettier": "npm run prettier.base -- --write", "prettier.base": "prettier --cache \"./({bin,scripts,src,test}/**/*.{ts,tsx,js,jsx})|bin/stencil|.github/(**/)?*.(yml|yaml)|*.js\"", "prettier.dry-run": "npm run prettier.base -- --list-different", "release": "npm run tsc.scripts && node scripts/build --release --publish", "release.prepare": "npm run tsc.scripts && node scripts/build --release --prepare", "rollup": "rollup --config", "rollup.prod": "rollup --config --config-prod", "rollup.prod.ci": "rollup --config --config-prod --config-ci", "rollup.watch": "rollup --watch --config", "start": "npm run watch", "test": "jest --coverage", "test.analysis": "cd test && npm run analysis.build-and-analyze", "test.bundlers": "cd test && npm run bundlers", "test.dist": "node scripts/build --validate-build", "test.end-to-end": "cd test/end-to-end && npm ci && npm test && npm run test.dist", "test.jest": "jest", "test.karma": "cd test/karma && npm ci && npm run karma", "test.karma.prod": "cd test/karma && npm ci && npm run karma.prod", "test.prod": "npm run test.dist && npm run test.end-to-end && npm run test.jest && npm run test.karma && npm run test.sys.node && npm run test.testing && npm run test.analysis", "test.testing": "node scripts/test/validate-testing.js", "test.watch": "jest --watch", "test.watch-all": "jest --watchAll --coverage", "tsc": "tsc --incremental", "tsc.prod": "tsc", "tsc.scripts": "tsc --build --force scripts/tsconfig.json", "tsc.watch": "tsc --incremental --watch"}, "devDependencies": {"@ionic/prettier-config": "^2.0.0", "@rollup/plugin-commonjs": "15.1.0", "@rollup/plugin-json": "6.0.0", "@rollup/plugin-node-resolve": "9.0.0", "@rollup/plugin-replace": "2.3.4", "@rollup/pluginutils": "5.0.2", "@types/eslint": "^8.4.6", "@types/exit": "^0.1.31", "@types/fs-extra": "^9.0.8", "@types/glob": "^8.0.0", "@types/graceful-fs": "^4.1.5", "@types/inquirer": "^7.3.1", "@types/jest": "^27.0.3", "@types/listr": "^0.14.4", "@types/node": "^16.11.62", "@types/pixelmatch": "^5.2.4", "@types/pngjs": "^6.0.1", "@types/prompts": "^2.0.9", "@types/semver": "^7.3.12", "@types/sizzle": "^2.3.2", "@types/webpack": "^4.41.26", "@types/ws": "^8.5.4", "@types/yarnpkg__lockfile": "^1.1.5", "@typescript-eslint/eslint-plugin": "^5.38.0", "@typescript-eslint/parser": "^5.38.0", "@yarnpkg/lockfile": "^1.1.0", "ansi-colors": "4.1.3", "autoprefixer": "10.4.13", "conventional-changelog-cli": "^2.2.2", "dts-bundle-generator": "~7.1.0", "eslint": "^8.23.1", "eslint-config-prettier": "^8.5.0", "eslint-plugin-jest": "^27.0.4", "eslint-plugin-jsdoc": "^39.3.1", "eslint-plugin-simple-import-sort": "^9.0.0", "execa": "4.1.0", "exit": "^0.1.2", "fs-extra": "^11.0.0", "glob": "8.1.0", "graceful-fs": "~4.2.6", "hash.js": "^1.1.7", "inquirer": "^7.3.3", "jest": "^27.4.5", "jest-cli": "^27.4.5", "jest-environment-node": "^27.4.4", "listr": "^0.14.3", "magic-string": "^0.27.0", "merge-source-map": "^1.1.0", "mime-db": "^1.46.0", "minimatch": "5.1.2", "node-fetch": "2.6.7", "open": "^8.4.0", "open-in-editor": "2.2.0", "parse5": "7.1.2", "path-browserify": "^1.0.1", "pixelmatch": "5.3.0", "postcss": "^8.2.8", "prettier": "2.8.3", "prompts": "2.4.2", "puppeteer": "~10.0.0", "rollup": "2.42.3", "rollup-plugin-sourcemaps": "^0.6.3", "semver": "^7.3.7", "sizzle": "^2.3.6", "terser": "5.16.1", "typescript": "4.9.4", "webpack": "^4.46.0", "ws": "8.12.0"}, "engines": {"node": ">=12.10.0", "npm": ">=6.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/ionic-team/stencil.git"}, "author": "Ionic Team", "homepage": "https://stenciljs.com/", "description": "A Compiler for Web Components and Progressive Web Apps", "keywords": ["web components", "components", "stencil", "ionic", "webapp", "custom elements", "pwa", "progressive web app"], "prettier": "@ionic/prettier-config", "volta": {"node": "16.13.0", "npm": "8.1.1"}}