"use strict";var t=require("node:util"),e=require("@napi-rs/canvas");class r{constructor(t,r,n){this.canvas=e.createCanvas(t,r),this.context=this.canvas.getContext("2d",n)}get width(){return this.canvas.width}set width(t){this.canvas.width=t}get height(){return this.canvas.height}set height(t){this.canvas.height=t}get direction(){return this.context.direction}get font(){return this.context.font}get globalAlpha(){return this.context.globalAlpha}get imageSmoothingEnabled(){return this.context.imageSmoothingEnabled}get imageSmoothingQuality(){return this.context.imageSmoothingQuality}get contextAttributes(){return this.context.getContextAttributes()}get transform(){return this.context.getTransform()}get textFontHeight(){return m(this.context.font)}get lineDash(){return this.context.getLineDash()}changeCanvasSize(t,e){return this.changeCanvasWidth(t).changeCanvasHeight(e)}changeCanvasWidth(t){return this.width=t,this}changeCanvasHeight(t){return this.height=t,this}save(){return this.context.save(),this}restore(){return this.context.restore(),this}rotate(t){return this.context.rotate(t),this}scale(t,e){return this.context.scale(t,e),this}translate(t,e){return this.context.translate(t,e),this}clip(...t){return this.context.clip(...t),this}setTransform(...t){return this.context.setTransform(...t),this}resetTransform(){return this.context.resetTransform(),this}resetFilters(){return this.setFilter("none")}getImageData(t,e,r,n){return this.context.getImageData(null!=t?t:0,null!=e?e:0,null!=r?r:this.width,null!=n?n:this.height)}putImageData(...t){return this.context.putImageData(...t),this}fill(...t){return this.context.fill(...t),this}printText(t,e,r,...n){return this.context.fillText(t,e,r,...n),this}printResponsiveText(t,e,r,n){const[i,s,o]=this.parseFont(this.context.font);if("number"!=typeof s)return this.printText(t,e,r);const{width:a}=this.measureText(t);if(a<=n)return this.printText(t,e,r);const h=n/a*s;return this.save().setTextFont(`${i}${h}${o}`).printText(t,e,r).restore()}printMultilineText(t,e,r){const n=t.split(/\r?\n/);if(n.length<=1)return this.printText(t,e,r);const i=this.textFontHeight;let s=r;for(const t of n)this.printText(t,e,Math.floor(s)),s+=i;return this}printWrappedText(t,e,r,n){const i=C(this,t,n);return this.printMultilineText(i,e,r)}stroke(){return this.context.stroke(),this}printStrokeRectangle(t,e,r,n){return this.context.strokeRect(t,e,r,n),this}printStrokeText(t,e,r,n){return this.context.strokeText(t,e,r,n),this}measureText(t){return this.context.measureText(t)}setTextSize(t){const e=this.parseFont(this.context.font);return 1===e.length?this:this.setTextFont(`${e[0]}${t}${e[2]}`)}setStroke(t){return this.context.strokeStyle=t,this}setLineWidth(t){return this.context.lineWidth=t,this}setStrokeWidth(t){return this.setLineWidth(t)}setLineDashOffset(t){return this.context.lineDashOffset=t,this}setLineJoin(t){return this.context.lineJoin=t,this}setLineCap(t){return this.context.lineCap=t,this}setLineDash(t){return this.context.setLineDash(t),this}printImage(t,...e){var n;return this.context.drawImage((n=t)instanceof r?n.canvas:n,...e),this}printCircularImage(t,e,r,n,{fit:i="fill"}={}){const{positionX:s,positionY:o,sizeX:a,sizeY:h}=this.resolveCircularCoordinates(t,e,r,n,i);return this.save().createCircularClip(e,r,n,0,2*Math.PI,!1).printImage(t,s,o,a,h).restore()}printRoundedImage(t,e,r,n,i,s){return this.save().createRoundedClip(e,r,n,i,s).printImage(t,e,r,n,i).restore()}printCircle(t,e,r){return this.save().createCircularPath(t,e,r).fill().restore()}printRectangle(t,e,r,n){return this.context.fillRect(t,e,r,n),this}printRoundedRectangle(t,e,r,n,i){return this.save().createRoundedPath(t,e,r,n,i).fill().restore()}createCircularPath(t,e,r,n=0,i=2*Math.PI,s=!1){return this.context.beginPath(),this.context.arc(t,e,r,n,i,s),this}createCircularClip(t,e,r,n,i,s){return this.createCircularPath(t,e,r,n,i,s).clip()}createRectanglePath(t,e,r,n){return this.context.rect(t,e,r,n),this}createRectangleClip(t,e,r,n){return this.createRectanglePath(t,e,r,n).clip()}createRoundedPath(t,e,r,n,i){if(r>0&&n>0){let s;"number"==typeof i?s={tl:i=Math.min(i,r/2,n/2),tr:i,br:i,bl:i}:(s=i,i=Math.min(5,r/2,n/2));const{tl:o=i,tr:a=i,br:h=i,bl:c=i}=s;this.context.beginPath(),this.context.moveTo(t+o,e),this.context.lineTo(t+r-a,e),this.context.quadraticCurveTo(t+r,e,t+r,e+a),this.context.lineTo(t+r,e+n-h),this.context.quadraticCurveTo(t+r,e+n,t+r-h,e+n),this.context.lineTo(t+c,e+n),this.context.quadraticCurveTo(t,e+n,t,e+n-c),this.context.lineTo(t,e+o),this.context.quadraticCurveTo(t,e,t+o,e),this.context.closePath()}return this}createRoundedClip(t,e,r,n,i){return this.createRoundedPath(t,e,r,n,i).clip()}setColor(t){return this.context.fillStyle=t,this}setTextFont(t){return this.context.font=t,this}setTextAlign(t){return this.context.textAlign=t,this}setTextBaseline(t){return this.context.textBaseline=t,this}setFilter(t){return this.context.filter=t,this}beginPath(){return this.context.beginPath(),this}closePath(){return this.context.closePath(),this}createPattern(t,e){return this.context.createPattern((n=t)instanceof r?n.getImageData():n,e);var n}printPattern(t,e){return this.setColor(this.createPattern(t,e))}createLinearGradient(t,e,r,n,i=[]){const s=this.context.createLinearGradient(t,e,r,n);for(const t of i)s.addColorStop(t.position,t.color);return s}printLinearColorGradient(t,e,r,n,i){const s=this.createLinearGradient(t,e,r,n,i);return this.setColor(s)}printLinearStrokeGradient(t,e,r,n,i){const s=this.createLinearGradient(t,e,r,n,i);return this.setStroke(s)}createRadialGradient(t,e,r,n,i,s,o=[]){const a=this.context.createRadialGradient(t,e,r,n,i,s);for(const t of o)a.addColorStop(t.position,t.color);return a}printRadialColorGradient(t,e,r,n,i,s,o){const a=this.createRadialGradient(t,e,r,n,i,s,o);return this.setColor(a)}printRadialStrokeGradient(t,e,r,n,i,s,o){const a=this.createRadialGradient(t,e,r,n,i,s,o);return this.setStroke(a)}createConicGradient(t,e,r,n=[]){const i=this.context.createConicGradient(t,e,r);for(const t of n)i.addColorStop(t.position,t.color);return i}printConicColorGradient(t,e,r,n){const i=this.createConicGradient(t,e,r,n);return this.setColor(i)}printConicStrokeGradient(t,e,r,n){const i=this.createConicGradient(t,e,r,n);return this.setStroke(i)}createEllipsePath(t,e,r,n,i,s,o,a){return this.context.ellipse(t,e,r,n,i,s,o,a),this}createEllipseClip(t,e,r,n,i,s,o,a){return this.createEllipsePath(t,e,r,n,i,s,o,a).clip()}arc(t,e,r,n,i,s){return this.context.arc(t,e,r,n,i,s),this}arcTo(t,e,r,n,i){return this.context.arcTo(t,e,r,n,i),this}quadraticCurveTo(t,e,r,n){return this.context.quadraticCurveTo(t,e,r,n),this}bezierCurveTo(t,e,r,n,i,s){return this.context.bezierCurveTo(t,e,r,n,i,s),this}lineTo(t,e){return this.context.lineTo(t,e),this}moveTo(t,e){return this.context.moveTo(t,e),this}setShadowBlur(t){return this.context.shadowBlur=t,this}setShadowColor(t){return this.context.shadowColor=t,this}setShadowOffsetX(t){return this.context.shadowOffsetX=t,this}setShadowOffsetY(t){return this.context.shadowOffsetY=t,this}setMiterLimit(t){return this.context.miterLimit=t,this}setGlobalCompositeOperation(t){return this.context.globalCompositeOperation=t,this}setGlobalAlpha(t){return this.context.globalAlpha=t,this}setImageSmoothingEnabled(t){return this.context.imageSmoothingEnabled=t,this}setImageSmoothingQuality(t){return this.context.imageSmoothingQuality=t,this}resetShadows(){return this.setShadowBlur(0).setShadowOffsetX(0).setShadowOffsetY(0).setShadowColor("transparent")}clearCircle(t,e,r,n=0,i=2*Math.PI,s=!1){return this.createCircularClip(t,e,r,n,i,s).clearRectangle(t-r,e-r,2*r,2*r)}clearRectangle(t=0,e=0,r=this.width,n=this.height){return this.context.clearRect(t,e,r,n),this}isPointInPath(...t){return this.context.isPointInPath(...t)}isPointInStroke(...t){return this.context.isPointInStroke(...t)}process(t,...e){return t.call(this,this,...e),this}wrapText(t,e){return C(this,t,e)}createImageData(...t){return this.context.createImageData(...t)}jpeg(t){return this.canvas.encodeSync("jpeg",t)}jpegAsync(t){return this.canvas.encode("jpeg",t)}webp(t){return this.canvas.encodeSync("webp",t)}webpAsync(t){return this.canvas.encode("webp",t)}png(){return this.canvas.encodeSync("png")}pngAsync(){return this.canvas.encode("png")}avif(t){return this.canvas.encodeSync("avif",t)}avifAsync(t){return this.canvas.encode("avif",t)}toDataURL(...t){return this.canvas.toDataURL(...t)}toDataURLAsync(...t){return this.canvas.toDataURLAsync(...t)}parseFont(t){const e=f.exec(t);return null===e?[t]:[t.slice(0,e.index),Number(e[1]),t.slice(e.index+e[1].length)]}resolveCircularCoordinates(t,e,r,n,i){const{width:s,height:o}=t;if("none"===i)return{positionX:e-s/2,positionY:r-o/2,sizeX:s,sizeY:o};const a=s/o,h=2*n;if("fill"===i||1===a)return{positionX:e-n,positionY:r-n,sizeX:h,sizeY:h};if("contain"===i)return a>1?{positionX:e-n,positionY:r-n/a,sizeX:h,sizeY:h/a}:{positionX:e-n*a,positionY:r-n,sizeX:h*a,sizeY:h};if(a>1){const t=h*a;return{positionX:e-t/2,positionY:r-h/2,sizeX:t,sizeY:h}}const c=h/a;return{positionX:e-h/2,positionY:r-c/2,sizeX:h,sizeY:c}}}const n=t.deprecate(e.loadImage,"resolveImage() is deprecated. Use loadImage() instead.");function i(t,r){return"string"==typeof t?e.GlobalFonts.registerFromPath(t,r):e.GlobalFonts.register(t,r)}const s=t.deprecate(i,"registerFont() is deprecated. Use loadFont() instead.");const o=t=>{const e=t.getImageData(),{data:r}=e;for(let t=0;t<r.length;t+=4){const e=.2126*r[t]+.7152*r[t+1]+.0722*r[t+2];r[t]=e,r[t+1]=e,r[t+2]=e}return t.putImageData(e,0,0)},a=o,h=t=>{const e=t.getImageData(),{data:r}=e;for(let t=0;t<r.length;t+=4){const e=255-(.2126*r[t]+.7152*r[t+1]+.0722*r[t+2]);r[t]=e,r[t+1]=e,r[t+2]=e}return t.putImageData(e,0,0)},c=h,l=(t,e)=>{const r=t.getImageData(),{data:n}=r;for(let t=0;t<n.length;t+=4)n[t]-=e,n[t+1]-=e,n[t+2]-=e;return t.putImageData(r,0,0)},u=l,p=(t,e,r=!0)=>{const n=Math.round(Math.sqrt(e.length)),i=Math.floor(n/2),s=t.getImageData(),o=s.data,a=s.width,h=s.height,c=a,l=h,u=t.getImageData(),p=u.data,g=r?1:0;for(let t=0;t<l;t++)for(let r=0;r<c;r++){const s=t,l=r,u=4*(t*c+r);let x=0,d=0,f=0,m=0;for(let t=0;t<n;t++)for(let r=0;r<n;r++){const c=s+t-i,u=l+r-i;if(c>=0&&c<h&&u>=0&&u<a){const i=4*(c*a+u),s=e[t*n+r];x+=o[i]*s,d+=o[i+1]*s,f+=o[i+2]*s,m+=o[i+3]*s}}p[u]=x,p[u+1]=d,p[u+2]=f,p[u+3]=m+g*(255-m)}return t.putImageData(u,0,0)},g=[0,-1,0,-1,4,-1,0,-1,0],x=[0,-1,0,-1,5,-1,0,-1,0],d=[1/9,1/9,1/9,1/9,1/9,1/9,1/9,1/9,1/9],f=/([\d.]+)(px|pt|pc|in|cm|mm|%|em|ex|ch|rem|q)/i,m=(()=>{const t=new Map;return e=>{const r=t.get(e);if(r)return r;const n=f.exec(e);if(!n)return 0;let i=Number(n[1]);switch(n[2]){case"pt":i/=.75;break;case"pc":i*=16;break;case"in":i*=96;break;case"cm":i*=96/2.54;break;case"mm":i*=96/25.4;break;case"em":case"rem":i*=16/.75;break;case"q":i*=96/25.4/4}return t.set(e,i),i}})(),C=(t,e,r)=>{const n=[],i=[],s=t.measureText(" ").width;for(const o of e.split(/\r?\n/)){let e=r;for(const a of o.split(" ")){const o=t.measureText(a).width,h=o+s;h>e?(i.length&&(n.push(i.join(" ")),i.length=0),i.push(a),e=r-o):(e-=h,i.push(a))}i.length&&(n.push(i.join(" ")),i.length=0)}return n.join("\n")};Object.defineProperty(exports,"GlobalFonts",{enumerable:!0,get:function(){return e.GlobalFonts}}),Object.defineProperty(exports,"Image",{enumerable:!0,get:function(){return e.Image}}),Object.defineProperty(exports,"Path2D",{enumerable:!0,get:function(){return e.Path2D}}),Object.defineProperty(exports,"loadImage",{enumerable:!0,get:function(){return e.loadImage}}),exports.Canvas=r,exports.blur=(t,e=1)=>{for(let r=0;r<e;++r)p(t,d,!0);return t},exports.brightness=(t,e)=>{const r=t.getImageData(),{data:n}=r;for(let t=0;t<n.length;t+=4)n[t]+=e,n[t+1]+=e,n[t+2]+=e;return t.putImageData(r,0,0)},exports.color=t=>t,exports.convolute=p,exports.darkness=l,exports.edge=t=>p(t,g,!0),exports.filter=(t,...e)=>`${t}(${e.join(" ")})`,exports.fontRegExp=f,exports.fontVariant=function(...t){return t.join(" ")},exports.getFontHeight=m,exports.grayscale=a,exports.greyscale=o,exports.hex=t=>`#${t}`,exports.hsl=(t,e,r)=>`hsl(${t}, ${e}%, ${r}%)`,exports.hsla=(t,e,r,n)=>`hsla(${t}, ${e}%, ${r}%, ${n})`,exports.invert=t=>t.save().setGlobalCompositeOperation("difference").setColor("white").printRectangle(0,0,t.width,t.height).restore(),exports.invertGrayscale=h,exports.invertGreyscale=c,exports.invertedThreshold=(t,e)=>{const r=t.getImageData(),{data:n}=r;for(let t=0;t<n.length;t+=4){const r=.2126*n[t]+.7152*n[t+1]+.0722*n[t+2]>=e?0:255;n[t]=r,n[t+1]=r,n[t+2]=r}return t.putImageData(r,0,0)},exports.loadFont=i,exports.loadFontsFromDirectory=function(t){return e.GlobalFonts.loadFontsFromDir(t)},exports.myOldFriend=u,exports.registerFont=s,exports.resolveImage=n,exports.rgb=(t,e,r)=>`rgb(${t}, ${e}, ${r})`,exports.rgba=(t,e,r,n)=>`rgba(${t}, ${e}, ${r}, ${n})`,exports.sepia=t=>{const e=t.getImageData(),{data:r}=e;for(let t=0;t<r.length;t+=4){const e=r[t],n=r[t+1],i=r[t+2];r[t]=.393*e+.769*n+.189*i,r[t+1]=.349*e+.686*n+.168*i,r[t+2]=.272*e+.534*n+.131*i}return t.putImageData(e,0,0)},exports.sharpen=(t,e=1)=>{for(let r=0;r<e;++r)p(t,x,!0);return t},exports.silhouette=t=>{const e=t.getImageData(),{data:r}=e;for(let t=0;t<r.length;t+=4)r[t]=0,r[t+1]=0,r[t+2]=0;return t.putImageData(e,0,0)},exports.textWrap=C,exports.threshold=(t,e)=>{const r=t.getImageData(),{data:n}=r;for(let t=0;t<n.length;t+=4){const r=.2126*n[t]+.7152*n[t+1]+.0722*n[t+2]>=e?255:0;n[t]=r,n[t+1]=r,n[t+2]=r}return t.putImageData(r,0,0)};
//# sourceMappingURL=napi-rs.js.map
