{"version": 3, "file": "long.d.ts", "sourceRoot": "", "sources": ["../src/long.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,cAAc,CAAC;AAEzC,OAAO,KAAK,EAAE,YAAY,EAAE,MAAM,iBAAiB,CAAC;AACpD,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,aAAa,CAAC;AA+E7C,cAAc;AACd,MAAM,WAAW,YAAY;IAC3B,WAAW,EAAE,MAAM,CAAC;CACrB;AAED;;;;;;;;;;;;;;;;;;GAkBG;AACH,qBAAa,IAAK,SAAQ,SAAS;IACjC,IAAI,SAAS,IAAI,MAAM,CAEtB;IAED,6EAA6E;IAC7E,IAAI,UAAU,IAAI,OAAO,CAExB;IAED;;OAEG;IACH,IAAI,EAAG,MAAM,CAAC;IAEd;;OAEG;IACH,GAAG,EAAG,MAAM,CAAC;IAEb;;OAEG;IACH,QAAQ,EAAG,OAAO,CAAC;IAEnB;;;;;;;;;;;;OAYG;gBACS,GAAG,GAAE,MAAM,GAAG,MAAM,GAAG,MAAU,EAAE,IAAI,CAAC,EAAE,MAAM,GAAG,OAAO,EAAE,QAAQ,CAAC,EAAE,OAAO;IAa1F,MAAM,CAAC,UAAU,OAAgC;IAEjD,8BAA8B;IAC9B,MAAM,CAAC,kBAAkB,OAAuD;IAChF,kBAAkB;IAClB,MAAM,CAAC,IAAI,OAAmB;IAC9B,qBAAqB;IACrB,MAAM,CAAC,KAAK,OAAyB;IACrC,kBAAkB;IAClB,MAAM,CAAC,GAAG,OAAmB;IAC7B,oBAAoB;IACpB,MAAM,CAAC,IAAI,OAAyB;IACpC,2BAA2B;IAC3B,MAAM,CAAC,OAAO,OAAoB;IAClC,4BAA4B;IAC5B,MAAM,CAAC,SAAS,OAAwD;IACxE,4BAA4B;IAC5B,MAAM,CAAC,SAAS,OAA2C;IAE3D;;;;;;;OAOG;IACH,MAAM,CAAC,QAAQ,CAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,CAAC,EAAE,OAAO,GAAG,IAAI;IAI5E;;;;;OAKG;IACH,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,CAAC,EAAE,OAAO,GAAG,IAAI;IAuBvD;;;;;OAKG;IACH,MAAM,CAAC,UAAU,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,CAAC,EAAE,OAAO,GAAG,IAAI;IAa1D;;;;;OAKG;IACH,MAAM,CAAC,UAAU,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,CAAC,EAAE,OAAO,GAAG,IAAI;IAI1D;;;;;;OAMG;IACH,MAAM,CAAC,UAAU,CAAC,GAAG,EAAE,MAAM,EAAE,QAAQ,CAAC,EAAE,OAAO,EAAE,KAAK,CAAC,EAAE,MAAM,GAAG,IAAI;IAuCxE;;;;;;OAMG;IACH,MAAM,CAAC,SAAS,CAAC,KAAK,EAAE,MAAM,EAAE,EAAE,QAAQ,CAAC,EAAE,OAAO,EAAE,EAAE,CAAC,EAAE,OAAO,GAAG,IAAI;IAIzE;;;;;OAKG;IACH,MAAM,CAAC,WAAW,CAAC,KAAK,EAAE,MAAM,EAAE,EAAE,QAAQ,CAAC,EAAE,OAAO,GAAG,IAAI;IAQ7D;;;;;OAKG;IACH,MAAM,CAAC,WAAW,CAAC,KAAK,EAAE,MAAM,EAAE,EAAE,QAAQ,CAAC,EAAE,OAAO,GAAG,IAAI;IAQ7D;;OAEG;IACH,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,OAAO,GAAG,KAAK,IAAI,IAAI;IAS5C;;;OAGG;IACH,MAAM,CAAC,SAAS,CACd,GAAG,EAAE,MAAM,GAAG,MAAM,GAAG;QAAE,GAAG,EAAE,MAAM,CAAC;QAAC,IAAI,EAAE,MAAM,CAAC;QAAC,QAAQ,CAAC,EAAE,OAAO,CAAA;KAAE,EACxE,QAAQ,CAAC,EAAE,OAAO,GACjB,IAAI;IAWP,sDAAsD;IACtD,GAAG,CAAC,MAAM,EAAE,MAAM,GAAG,MAAM,GAAG,IAAI,GAAG,SAAS,GAAG,IAAI;IAiCrD;;;OAGG;IACH,GAAG,CAAC,KAAK,EAAE,MAAM,GAAG,MAAM,GAAG,IAAI,GAAG,SAAS,GAAG,IAAI;IAKpD;;;OAGG;IACH,OAAO,CAAC,KAAK,EAAE,MAAM,GAAG,MAAM,GAAG,IAAI,GAAG,SAAS,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAgB9D,+CAA+C;IAC/C,IAAI,CAAC,KAAK,EAAE,MAAM,GAAG,MAAM,GAAG,IAAI,GAAG,SAAS,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAI3D;;;OAGG;IACH,MAAM,CAAC,OAAO,EAAE,MAAM,GAAG,MAAM,GAAG,IAAI,GAAG,SAAS,GAAG,IAAI;IAqGzD,6CAA6C;IAC7C,GAAG,CAAC,OAAO,EAAE,MAAM,GAAG,MAAM,GAAG,IAAI,GAAG,SAAS,GAAG,IAAI;IAItD;;;OAGG;IACH,MAAM,CAAC,KAAK,EAAE,MAAM,GAAG,MAAM,GAAG,IAAI,GAAG,SAAS,GAAG,OAAO;IAO1D,8CAA8C;IAC9C,EAAE,CAAC,KAAK,EAAE,MAAM,GAAG,MAAM,GAAG,IAAI,GAAG,SAAS,GAAG,OAAO;IAItD,iDAAiD;IACjD,WAAW,IAAI,MAAM;IAIrB,oDAAoD;IACpD,mBAAmB,IAAI,MAAM;IAI7B,gDAAgD;IAChD,UAAU,IAAI,MAAM;IAIpB,mDAAmD;IACnD,kBAAkB,IAAI,MAAM;IAI5B,mFAAmF;IACnF,aAAa,IAAI,MAAM;IAWvB,kEAAkE;IAClE,WAAW,CAAC,KAAK,EAAE,MAAM,GAAG,MAAM,GAAG,IAAI,GAAG,SAAS,GAAG,OAAO;IAI/D,mDAAmD;IACnD,EAAE,CAAC,KAAK,EAAE,MAAM,GAAG,MAAM,GAAG,IAAI,GAAG,SAAS,GAAG,OAAO;IAItD,2EAA2E;IAC3E,kBAAkB,CAAC,KAAK,EAAE,MAAM,GAAG,MAAM,GAAG,IAAI,GAAG,SAAS,GAAG,OAAO;IAItE,0DAA0D;IAC1D,GAAG,CAAC,KAAK,EAAE,MAAM,GAAG,MAAM,GAAG,IAAI,GAAG,SAAS,GAAG,OAAO;IAGvD,0DAA0D;IAC1D,EAAE,CAAC,KAAK,EAAE,MAAM,GAAG,MAAM,GAAG,IAAI,GAAG,SAAS,GAAG,OAAO;IAItD,0CAA0C;IAC1C,MAAM,IAAI,OAAO;IAIjB,8CAA8C;IAC9C,UAAU,IAAI,OAAO;IAIrB,yCAAyC;IACzC,KAAK,IAAI,OAAO;IAIhB,8CAA8C;IAC9C,UAAU,IAAI,OAAO;IAIrB,8CAA8C;IAC9C,MAAM,IAAI,OAAO;IAIjB,+DAA+D;IAC/D,QAAQ,CAAC,KAAK,EAAE,MAAM,GAAG,MAAM,GAAG,IAAI,GAAG,SAAS,GAAG,OAAO;IAI5D,iDAAiD;IACjD,EAAE,CAAC,KAAK,EAAE,MAAM,GAAG,MAAM,GAAG,IAAI,GAAG,SAAS,GAAG,OAAO;IAItD,wEAAwE;IACxE,eAAe,CAAC,KAAK,EAAE,MAAM,GAAG,MAAM,GAAG,IAAI,GAAG,SAAS,GAAG,OAAO;IAInE,uDAAuD;IACvD,GAAG,CAAC,KAAK,EAAE,MAAM,GAAG,MAAM,GAAG,IAAI,GAAG,SAAS,GAAG,OAAO;IAIvD,8CAA8C;IAC9C,MAAM,CAAC,OAAO,EAAE,MAAM,GAAG,MAAM,GAAG,IAAI,GAAG,SAAS,GAAG,IAAI;IAiBzD,8CAA8C;IAC9C,GAAG,CAAC,OAAO,EAAE,MAAM,GAAG,MAAM,GAAG,IAAI,GAAG,SAAS,GAAG,IAAI;IAGtD,8CAA8C;IAC9C,GAAG,CAAC,OAAO,EAAE,MAAM,GAAG,MAAM,GAAG,IAAI,GAAG,SAAS,GAAG,IAAI;IAItD;;;;OAIG;IACH,QAAQ,CAAC,UAAU,EAAE,MAAM,GAAG,MAAM,GAAG,IAAI,GAAG,SAAS,GAAG,IAAI;IA+D9D,gDAAgD;IAChD,GAAG,CAAC,UAAU,EAAE,MAAM,GAAG,MAAM,GAAG,IAAI,GAAG,SAAS,GAAG,IAAI;IAIzD,iDAAiD;IACjD,MAAM,IAAI,IAAI;IAKd,8CAA8C;IAC9C,GAAG,IAAI,IAAI;IAIX,4CAA4C;IAC5C,GAAG,IAAI,IAAI;IAIX,+DAA+D;IAC/D,SAAS,CAAC,KAAK,EAAE,MAAM,GAAG,MAAM,GAAG,IAAI,GAAG,SAAS,GAAG,OAAO;IAI7D,iDAAiD;IACjD,GAAG,CAAC,KAAK,EAAE,MAAM,GAAG,MAAM,GAAG,IAAI,GAAG,SAAS,GAAG,OAAO;IAGvD,iDAAiD;IACjD,EAAE,CAAC,KAAK,EAAE,MAAM,GAAG,MAAM,GAAG,IAAI,GAAG,SAAS,GAAG,OAAO;IAItD;;OAEG;IACH,EAAE,CAAC,KAAK,EAAE,MAAM,GAAG,MAAM,GAAG,IAAI,GAAG,IAAI;IAKvC;;;;OAIG;IACH,SAAS,CAAC,OAAO,EAAE,MAAM,GAAG,IAAI,GAAG,IAAI;IAYvC,iDAAiD;IACjD,GAAG,CAAC,OAAO,EAAE,MAAM,GAAG,IAAI,GAAG,IAAI;IAIjC;;;;OAIG;IACH,UAAU,CAAC,OAAO,EAAE,MAAM,GAAG,IAAI,GAAG,IAAI;IAYxC,kDAAkD;IAClD,GAAG,CAAC,OAAO,EAAE,MAAM,GAAG,IAAI,GAAG,IAAI;IAIjC;;;;OAIG;IACH,kBAAkB,CAAC,OAAO,EAAE,IAAI,GAAG,MAAM,GAAG,IAAI;IAkBhD,0DAA0D;IAC1D,KAAK,CAAC,OAAO,EAAE,MAAM,GAAG,IAAI,GAAG,IAAI;IAGnC,0DAA0D;IAC1D,IAAI,CAAC,OAAO,EAAE,MAAM,GAAG,IAAI,GAAG,IAAI;IAIlC;;;;OAIG;IACH,QAAQ,CAAC,UAAU,EAAE,MAAM,GAAG,MAAM,GAAG,IAAI,GAAG,SAAS,GAAG,IAAI;IAK9D,gDAAgD;IAChD,GAAG,CAAC,UAAU,EAAE,MAAM,GAAG,MAAM,GAAG,IAAI,GAAG,SAAS,GAAG,IAAI;IAIzD,8EAA8E;IAC9E,KAAK,IAAI,MAAM;IAIf,gHAAgH;IAChH,QAAQ,IAAI,MAAM;IAKlB,2DAA2D;IAC3D,QAAQ,IAAI,MAAM;IAKlB;;;;OAIG;IACH,OAAO,CAAC,EAAE,CAAC,EAAE,OAAO,GAAG,MAAM,EAAE;IAI/B;;;OAGG;IACH,SAAS,IAAI,MAAM,EAAE;IAerB;;;OAGG;IACH,SAAS,IAAI,MAAM,EAAE;IAerB;;OAEG;IACH,QAAQ,IAAI,IAAI;IAKhB;;;;OAIG;IACH,QAAQ,CAAC,KAAK,CAAC,EAAE,MAAM,GAAG,MAAM;IAqChC,sCAAsC;IACtC,UAAU,IAAI,IAAI;IAKlB,8DAA8D;IAC9D,GAAG,CAAC,KAAK,EAAE,IAAI,GAAG,MAAM,GAAG,MAAM,GAAG,IAAI;IAKxC,8CAA8C;IAC9C,GAAG,IAAI,OAAO;IAId,uDAAuD;IACvD,EAAE,CAAC,KAAK,EAAE,MAAM,GAAG,MAAM,GAAG,IAAI,GAAG,SAAS,GAAG,OAAO;IAStD,cAAc,CAAC,OAAO,CAAC,EAAE,YAAY,GAAG,MAAM,GAAG,YAAY;IAI7D,MAAM,CAAC,gBAAgB,CACrB,GAAG,EAAE;QAAE,WAAW,EAAE,MAAM,CAAA;KAAE,EAC5B,OAAO,CAAC,EAAE,YAAY,GACrB,MAAM,GAAG,IAAI,GAAG,MAAM;IA8BzB,OAAO,IAAI,MAAM;CAGlB"}