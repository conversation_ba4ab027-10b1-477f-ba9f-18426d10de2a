!function(t,e){for(var n in e)t[n]=e[n]}(exports,function(t){var e={};function n(o){if(e[o])return e[o].exports;var r=e[o]={i:o,l:!1,exports:{}};return t[o].call(r.exports,r,r.exports,n),r.l=!0,r.exports}return n.m=t,n.c=e,n.d=function(t,e,o){n.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:o})},n.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},n.t=function(t,e){if(1&e&&(t=n(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var o=Object.create(null);if(n.r(o),Object.defineProperty(o,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var r in t)n.d(o,r,function(e){return t[e]}.bind(null,r));return o},n.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return n.d(e,"a",e),e},n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},n.p="",n(n.s=0)}([function(t,e,n){t.exports=n(1)},function(t,e,n){var o,r,c=n(2),i=n(3),u=n(5),f=n(7),s=n(8);function a(t,e){Object.defineProperty(t,o,{get:function(){return e}})}"function"==typeof Symbol&&"function"==typeof Symbol.for?(o=Symbol.for("graceful-fs.queue"),r=Symbol.for("graceful-fs.previous")):(o="___graceful-fs.queue",r="___graceful-fs.previous");var l,p=function(){};if(s.debuglog?p=s.debuglog("gfs4"):/\bgfs4\b/i.test(process.env.NODE_DEBUG||"")&&(p=function(){var t=s.format.apply(s,arguments);t="GFS4: "+t.split(/\n/).join("\nGFS4: "),console.error(t)}),!c[o]){var h=global[o]||[];a(c,h),c.close=function(t){function e(e,n){return t.call(c,e,(function(t){t||m(),"function"==typeof n&&n.apply(this,arguments)}))}return Object.defineProperty(e,r,{value:t}),e}(c.close),c.closeSync=function(t){function e(e){t.apply(c,arguments),m()}return Object.defineProperty(e,r,{value:t}),e}(c.closeSync),/\bgfs4\b/i.test(process.env.NODE_DEBUG||"")&&process.on("exit",(function(){p(c[o]),n(9).equal(c[o].length,0)}))}function d(t){i(t),t.gracefulify=d,t.createReadStream=function(e,n){return new t.ReadStream(e,n)},t.createWriteStream=function(e,n){return new t.WriteStream(e,n)};var e=t.readFile;t.readFile=function(t,n,o){"function"==typeof n&&(o=n,n=null);return function t(n,o,r,c){return e(n,o,(function(e){!e||"EMFILE"!==e.code&&"ENFILE"!==e.code?"function"==typeof r&&r.apply(this,arguments):y([t,[n,o,r],e,c||Date.now(),Date.now()])}))}(t,n,o)};var n=t.writeFile;t.writeFile=function(t,e,o,r){"function"==typeof o&&(r=o,o=null);return function t(e,o,r,c,i){return n(e,o,r,(function(n){!n||"EMFILE"!==n.code&&"ENFILE"!==n.code?"function"==typeof c&&c.apply(this,arguments):y([t,[e,o,r,c],n,i||Date.now(),Date.now()])}))}(t,e,o,r)};var o=t.appendFile;o&&(t.appendFile=function(t,e,n,r){"function"==typeof n&&(r=n,n=null);return function t(e,n,r,c,i){return o(e,n,r,(function(o){!o||"EMFILE"!==o.code&&"ENFILE"!==o.code?"function"==typeof c&&c.apply(this,arguments):y([t,[e,n,r,c],o,i||Date.now(),Date.now()])}))}(t,e,n,r)});var r=t.copyFile;r&&(t.copyFile=function(t,e,n,o){"function"==typeof n&&(o=n,n=0);return function t(e,n,o,c,i){return r(e,n,o,(function(r){!r||"EMFILE"!==r.code&&"ENFILE"!==r.code?"function"==typeof c&&c.apply(this,arguments):y([t,[e,n,o,c],r,i||Date.now(),Date.now()])}))}(t,e,n,o)});var c=t.readdir;t.readdir=function(t,e,n){"function"==typeof e&&(n=e,e=null);var o=f.test(process.version)?function(t,e,n,o){return c(t,r(t,e,n,o))}:function(t,e,n,o){return c(t,e,r(t,e,n,o))};return o(t,e,n);function r(t,e,n,r){return function(c,i){!c||"EMFILE"!==c.code&&"ENFILE"!==c.code?(i&&i.sort&&i.sort(),"function"==typeof n&&n.call(this,c,i)):y([o,[t,e,n],c,r||Date.now(),Date.now()])}}};var f=/^v[0-5]\./;if("v0.8"===process.version.substr(0,4)){var s=u(t);m=s.ReadStream,b=s.WriteStream}var a=t.ReadStream;a&&(m.prototype=Object.create(a.prototype),m.prototype.open=function(){var t=this;v(t.path,t.flags,t.mode,(function(e,n){e?(t.autoClose&&t.destroy(),t.emit("error",e)):(t.fd=n,t.emit("open",n),t.read())}))});var l=t.WriteStream;l&&(b.prototype=Object.create(l.prototype),b.prototype.open=function(){var t=this;v(t.path,t.flags,t.mode,(function(e,n){e?(t.destroy(),t.emit("error",e)):(t.fd=n,t.emit("open",n))}))}),Object.defineProperty(t,"ReadStream",{get:function(){return m},set:function(t){m=t},enumerable:!0,configurable:!0}),Object.defineProperty(t,"WriteStream",{get:function(){return b},set:function(t){b=t},enumerable:!0,configurable:!0});var p=m;Object.defineProperty(t,"FileReadStream",{get:function(){return p},set:function(t){p=t},enumerable:!0,configurable:!0});var h=b;function m(t,e){return this instanceof m?(a.apply(this,arguments),this):m.apply(Object.create(m.prototype),arguments)}function b(t,e){return this instanceof b?(l.apply(this,arguments),this):b.apply(Object.create(b.prototype),arguments)}Object.defineProperty(t,"FileWriteStream",{get:function(){return h},set:function(t){h=t},enumerable:!0,configurable:!0});var S=t.open;function v(t,e,n,o){return"function"==typeof n&&(o=n,n=null),function t(e,n,o,r,c){return S(e,n,o,(function(i,u){!i||"EMFILE"!==i.code&&"ENFILE"!==i.code?"function"==typeof r&&r.apply(this,arguments):y([t,[e,n,o,r],i,c||Date.now(),Date.now()])}))}(t,e,n,o)}return t.open=v,t}function y(t){p("ENQUEUE",t[0].name,t[1]),c[o].push(t),b()}function m(){for(var t=Date.now(),e=0;e<c[o].length;++e)c[o][e].length>2&&(c[o][e][3]=t,c[o][e][4]=t);b()}function b(){if(clearTimeout(l),l=void 0,0!==c[o].length){var t=c[o].shift(),e=t[0],n=t[1],r=t[2],i=t[3],u=t[4];if(void 0===i)p("RETRY",e.name,n),e.apply(null,n);else if(Date.now()-i>=6e4){p("TIMEOUT",e.name,n);var f=n.pop();"function"==typeof f&&f.call(null,r)}else{var s=Date.now()-u,a=Math.max(u-i,1);s>=Math.min(1.2*a,100)?(p("RETRY",e.name,n),e.apply(null,n.concat([i]))):c[o].push(t)}void 0===l&&(l=setTimeout(b,0))}}global[o]||a(global,c[o]),t.exports=d(f(c)),process.env.TEST_GRACEFUL_FS_GLOBAL_PATCH&&!c.__patched&&(t.exports=d(c),c.__patched=!0)},function(t,e){t.exports=require("fs")},function(t,e,n){var o=n(4),r=process.cwd,c=null,i=process.env.GRACEFUL_FS_PLATFORM||process.platform;process.cwd=function(){return c||(c=r.call(process)),c};try{process.cwd()}catch(t){}if("function"==typeof process.chdir){var u=process.chdir;process.chdir=function(t){c=null,u.call(process,t)},Object.setPrototypeOf&&Object.setPrototypeOf(process.chdir,u)}t.exports=function(t){o.hasOwnProperty("O_SYMLINK")&&process.version.match(/^v0\.6\.[0-2]|^v0\.5\./)&&function(t){t.lchmod=function(e,n,r){t.open(e,o.O_WRONLY|o.O_SYMLINK,n,(function(e,o){e?r&&r(e):t.fchmod(o,n,(function(e){t.close(o,(function(t){r&&r(e||t)}))}))}))},t.lchmodSync=function(e,n){var r,c=t.openSync(e,o.O_WRONLY|o.O_SYMLINK,n),i=!0;try{r=t.fchmodSync(c,n),i=!1}finally{if(i)try{t.closeSync(c)}catch(t){}else t.closeSync(c)}return r}}(t);t.lutimes||function(t){o.hasOwnProperty("O_SYMLINK")&&t.futimes?(t.lutimes=function(e,n,r,c){t.open(e,o.O_SYMLINK,(function(e,o){e?c&&c(e):t.futimes(o,n,r,(function(e){t.close(o,(function(t){c&&c(e||t)}))}))}))},t.lutimesSync=function(e,n,r){var c,i=t.openSync(e,o.O_SYMLINK),u=!0;try{c=t.futimesSync(i,n,r),u=!1}finally{if(u)try{t.closeSync(i)}catch(t){}else t.closeSync(i)}return c}):t.futimes&&(t.lutimes=function(t,e,n,o){o&&process.nextTick(o)},t.lutimesSync=function(){})}(t);t.chown=r(t.chown),t.fchown=r(t.fchown),t.lchown=r(t.lchown),t.chmod=e(t.chmod),t.fchmod=e(t.fchmod),t.lchmod=e(t.lchmod),t.chownSync=c(t.chownSync),t.fchownSync=c(t.fchownSync),t.lchownSync=c(t.lchownSync),t.chmodSync=n(t.chmodSync),t.fchmodSync=n(t.fchmodSync),t.lchmodSync=n(t.lchmodSync),t.stat=u(t.stat),t.fstat=u(t.fstat),t.lstat=u(t.lstat),t.statSync=f(t.statSync),t.fstatSync=f(t.fstatSync),t.lstatSync=f(t.lstatSync),t.chmod&&!t.lchmod&&(t.lchmod=function(t,e,n){n&&process.nextTick(n)},t.lchmodSync=function(){});t.chown&&!t.lchown&&(t.lchown=function(t,e,n,o){o&&process.nextTick(o)},t.lchownSync=function(){});"win32"===i&&(t.rename="function"!=typeof t.rename?t.rename:function(e){function n(n,o,r){var c=Date.now(),i=0;e(n,o,(function u(f){if(f&&("EACCES"===f.code||"EPERM"===f.code)&&Date.now()-c<6e4)return setTimeout((function(){t.stat(o,(function(t,c){t&&"ENOENT"===t.code?e(n,o,u):r(f)}))}),i),void(i<100&&(i+=10));r&&r(f)}))}return Object.setPrototypeOf&&Object.setPrototypeOf(n,e),n}(t.rename));function e(e){return e?function(n,o,r){return e.call(t,n,o,(function(t){s(t)&&(t=null),r&&r.apply(this,arguments)}))}:e}function n(e){return e?function(n,o){try{return e.call(t,n,o)}catch(t){if(!s(t))throw t}}:e}function r(e){return e?function(n,o,r,c){return e.call(t,n,o,r,(function(t){s(t)&&(t=null),c&&c.apply(this,arguments)}))}:e}function c(e){return e?function(n,o,r){try{return e.call(t,n,o,r)}catch(t){if(!s(t))throw t}}:e}function u(e){return e?function(n,o,r){function c(t,e){e&&(e.uid<0&&(e.uid+=4294967296),e.gid<0&&(e.gid+=4294967296)),r&&r.apply(this,arguments)}return"function"==typeof o&&(r=o,o=null),o?e.call(t,n,o,c):e.call(t,n,c)}:e}function f(e){return e?function(n,o){var r=o?e.call(t,n,o):e.call(t,n);return r&&(r.uid<0&&(r.uid+=4294967296),r.gid<0&&(r.gid+=4294967296)),r}:e}function s(t){return!t||("ENOSYS"===t.code||!(process.getuid&&0===process.getuid()||"EINVAL"!==t.code&&"EPERM"!==t.code))}t.read="function"!=typeof t.read?t.read:function(e){function n(n,o,r,c,i,u){var f;if(u&&"function"==typeof u){var s=0;f=function(a,l,p){if(a&&"EAGAIN"===a.code&&s<10)return s++,e.call(t,n,o,r,c,i,f);u.apply(this,arguments)}}return e.call(t,n,o,r,c,i,f)}return Object.setPrototypeOf&&Object.setPrototypeOf(n,e),n}(t.read),t.readSync="function"!=typeof t.readSync?t.readSync:(a=t.readSync,function(e,n,o,r,c){for(var i=0;;)try{return a.call(t,e,n,o,r,c)}catch(t){if("EAGAIN"===t.code&&i<10){i++;continue}throw t}});var a}},function(t,e){t.exports=require("constants")},function(t,e,n){var o=n(6).Stream;t.exports=function(t){return{ReadStream:function e(n,r){if(!(this instanceof e))return new e(n,r);o.call(this);var c=this;this.path=n,this.fd=null,this.readable=!0,this.paused=!1,this.flags="r",this.mode=438,this.bufferSize=65536,r=r||{};for(var i=Object.keys(r),u=0,f=i.length;u<f;u++){var s=i[u];this[s]=r[s]}this.encoding&&this.setEncoding(this.encoding);if(void 0!==this.start){if("number"!=typeof this.start)throw TypeError("start must be a Number");if(void 0===this.end)this.end=1/0;else if("number"!=typeof this.end)throw TypeError("end must be a Number");if(this.start>this.end)throw new Error("start must be <= end");this.pos=this.start}if(null!==this.fd)return void process.nextTick((function(){c._read()}));t.open(this.path,this.flags,this.mode,(function(t,e){if(t)return c.emit("error",t),void(c.readable=!1);c.fd=e,c.emit("open",e),c._read()}))},WriteStream:function e(n,r){if(!(this instanceof e))return new e(n,r);o.call(this),this.path=n,this.fd=null,this.writable=!0,this.flags="w",this.encoding="binary",this.mode=438,this.bytesWritten=0,r=r||{};for(var c=Object.keys(r),i=0,u=c.length;i<u;i++){var f=c[i];this[f]=r[f]}if(void 0!==this.start){if("number"!=typeof this.start)throw TypeError("start must be a Number");if(this.start<0)throw new Error("start must be >= zero");this.pos=this.start}this.busy=!1,this._queue=[],null===this.fd&&(this._open=t.open,this._queue.push([this._open,this.path,this.flags,this.mode,void 0]),this.flush())}}}},function(t,e){t.exports=require("stream")},function(t,e,n){"use strict";t.exports=function(t){if(null===t||"object"!=typeof t)return t;if(t instanceof Object)var e={__proto__:o(t)};else e=Object.create(null);return Object.getOwnPropertyNames(t).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e};var o=Object.getPrototypeOf||function(t){return t.__proto__}},function(t,e){t.exports=require("util")},function(t,e){t.exports=require("assert")}]));