/// <reference types="node" />
import { Image as NativeImage, loadImage, registerFont as loadFont, type Canvas as NativeCanvas, type CanvasRenderingContext2D as NativeCanvasRenderingContext2D, type ImageData, type JpegConfig, type JPEGStream, type PdfConfig, type PDFStream, type PngConfig, type PNGStream } from 'canvas';
export interface BeveledRadiusOptions {
    /**
     * Top left corner.
     */
    tl: number;
    /**
     * Top right corner.
     */
    tr: number;
    /**
     * Bottom right corner.
     */
    br: number;
    /**
     * Bottom left corner.
     */
    bl: number;
}
export interface GradientStop {
    position: number;
    color: string;
}
export interface PrintCircularOptions {
    /**
     * The fit options, this is similar to CSS's object-fit.
     * @see https://developer.mozilla.org/en-US/docs/Web/CSS/object-fit
     */
    fit?: 'fill' | 'contain' | 'cover' | 'none';
}
export type ImageResolvable = Canvas | NativeImage | NativeCanvas;
export type PatternResolvable = Canvas | NativeImage | NativeImage;
export type PatternRepeat = 'repeat' | 'repeat-x' | 'repeat-y' | 'no-repeat' | null;
export type Transform = ReturnType<NativeCanvasRenderingContext2D['getTransform']>;
export type AntiAlias = NativeCanvasRenderingContext2D['antialias'];
export type TextDrawingMode = NativeCanvasRenderingContext2D['textDrawingMode'];
export type PatternQuality = NativeCanvasRenderingContext2D['patternQuality'];
export declare class Canvas {
    /**
     * The constructed Canvas.
     */
    canvas: NativeCanvas;
    /**
     * The 2D context for the Canvas.
     */
    context: NativeCanvasRenderingContext2D;
    /**
     * Initialize canvas-constructor with `canvas`.
     * @param width The width of the canvas.
     * @param height The height of the canvas.
     * @param type The type of Canvas.
     */
    constructor(width: number, height: number, type?: 'pdf' | 'svg');
    /**
     * For PDF canvases, adds another page.
     * @param width The width of the new PDF page, defaults to the canvas's initial width.
     * @param height The height of the new PDF page, defaults to the canvas's initial height.
     * @note This is a `canvas` extension.
     */
    addPage(width?: number, height?: number): this;
    /**
     * The image width of this canvas
     */
    get width(): number;
    set width(value: number);
    /**
     * The image height of this canvas
     */
    get height(): number;
    set height(value: number);
    /**
     * The current text style to use when drawing text. This string uses the same syntax as the
     * [CSS font](https://developer.mozilla.org/en-US/docs/Web/CSS/font) specifier.
     * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/font
     */
    get font(): string;
    /**
     * The alpha (transparency) value that is applied to shapes and images before they are drawn onto the canvas.
     * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/globalAlpha
     */
    get globalAlpha(): number;
    /**
     * Gets the anti-aliasing mode.
     * @note This is a `canvas` extension.
     */
    get antialias(): AntiAlias;
    /**
     * Gets the text drawing mode.
     * @note This is a `canvas` extension.
     */
    get textDrawingMode(): TextDrawingMode;
    /**
     * Gets the pattern quality.
     * @note This is a `canvas` extension.
     */
    get patternQuality(): PatternQuality;
    /**
     * Returns the current transformation matrix being applied to the context.
     * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/getTransform
     */
    get transform(): Transform;
    /**
     * The font height
     */
    get textFontHeight(): number;
    /**
     * A list of numbers that specifies distances to alternately draw a line and a gap (in coordinate space units).
     * If the number, when setting the elements, was odd, the elements of the array get copied and concatenated. For
     * example, setting the line dash to [5, 15, 25] will result in getting back [5, 15, 25, 5, 15, 25].
     * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/getLineDash
     * @example
     * new Canvas(400, 300)
     *     .beginPath()
     *     .setLineDash([5, 15])
     *     .moveTo(0, 50)
     *     .lineTo(400, 50)
     *     .stroke()
     *     .png();
     */
    get lineDash(): number[];
    /**
     * Change the current canvas' size.
     * @param width The new width for the canvas.
     * @param height The new height for the canvas.
     */
    changeCanvasSize(width: number, height: number): this;
    /**
     * Change the current canvas' width.
     * @param width The new width for the canvas.
     */
    changeCanvasWidth(width: number): this;
    /**
     * Change the current canvas' height.
     * @param height The new height for the canvas.
     */
    changeCanvasHeight(height: number): this;
    /**
     * Save the entire state of the canvas by pushing the current state onto a stack.
     * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/save
     */
    save(): this;
    /**
     * Restores the most recently saved canvas by popping the top entry in the drawing state stack. If there is no saved
     * state, this method does nothing.
     * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/restore
     */
    restore(): this;
    /**
     * Adds a rotation to the transformation matrix. The angle argument represents a clockwise rotation angle and is
     * expressed in radians.
     * @param angle The angle to rotate clockwise in radians.
     * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/rotate
     */
    rotate(angle: number): this;
    /**
     * Adds a scaling transformation to the canvas units by X horizontally and by y vertically.
     * @param x Scaling factor in the horizontal direction.
     * @param y Scaling factor in the vertical direction.
     * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/scale
     */
    scale(x: number, y: number): this;
    /**
     * Adds a translation transformation by moving the canvas and its origin X horizontally and y vertically on the grid.
     * @param x Distance to move in the horizontal direction.
     * @param y Distance to move in the vertical direction.
     * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/translate
     */
    translate(x: number, y: number): this;
    /**
     * Turns the path currently being built into the current clipping path.
     * @param fillRule The algorithm by which to determine if a point is inside a path or outside a path.
     * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/clip
     */
    clip(fillRule?: CanvasFillRule): this;
    /**
     * Turns the path currently being built into the current clipping path.
     * @param path The path to use.
     * @param fillRule The algorithm by which to determine if a point is inside a path or outside a path.
     * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/clip
     */
    clip(path: Path2D, fillRule?: CanvasFillRule): this;
    /**
     * Resets (overrides) the current transformation to the identity matrix and then invokes a transformation described
     * by the arguments of this method.
     * @param a Horizontal scaling.
     * @param b Horizontal skewing.
     * @param c Vertical skewing.
     * @param d Vertical scaling.
     * @param e Horizontal moving.
     * @param f Vertical moving.
     * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/setTransform
     */
    setTransform(a: number, b: number, c: number, d: number, e: number, f: number): this;
    /**
     * Resets (overrides) the current transformation to the identity matrix and then invokes a transformation described
     * by the arguments of this method.
     * @param matrix The new transform matrix.
     * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/setTransform
     */
    setTransform(transform?: DOMMatrix): this;
    /**
     * Resets the transformation.
     * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/resetTransform
     */
    resetTransform(): this;
    /**
     * Returns an ImageData object representing the underlying pixel data for the area of the canvas
     * denoted by the entire Canvas. This method is not affected by the canvas transformation matrix.
     * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/getImageData
     */
    getImageData(): ImageData;
    /**
     * Returns an ImageData object representing the underlying pixel data for the area of the canvas denoted by the rectangle which starts at (sx, sy)
     * and has an sw width and sh height. This method is not affected by the canvas transformation matrix.
     * @param x The X coordinate of the upper left corner of the rectangle from which the ImageData will be extracted.
     * @param y The Y coordinate of the upper left corner of the rectangle from which the ImageData will be extracted.
     * @param width The width of the rectangle from which the ImageData will be extracted.
     * @param height The height of the rectangle from which the ImageData will be extracted.
     * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/getImageData
     */
    getImageData(x: number, y: number, width: number, height: number): ImageData;
    /**
     * The CanvasRenderingContext2D.putImageData() method of the Canvas 2D API paints data from the given ImageData object onto the bitmap.
     * This method is not affected by the canvas transformation matrix.
     * @param imagedata An ImageData object containing the array of pixel values.
     * @param dx Horizontal position (x-coordinate) at which to place the image data in the destination canvas.
     * @param dy Vertical position (y-coordinate) at which to place the image data in the destination canvas.
     * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/putImageData
     */
    putImageData(imagedata: ImageData, dx: number, dy: number): this;
    /**
     * The CanvasRenderingContext2D.putImageData() method of the Canvas 2D API paints data from the given ImageData object onto the bitmap.
     * Only the pixels from that rectangle are painted.
     * This method is not affected by the canvas transformation matrix.
     * @param imagedata An ImageData object containing the array of pixel values.
     * @param x Horizontal position (x-coordinate) at which to place the image data in the destination canvas.
     * @param y Vertical position (y-coordinate) at which to place the image data in the destination canvas.
     * @param dirtyX Horizontal position (x-coordinate). The X coordinate of the top left hand corner of your Image data. Defaults to 0.
     * @param dirtyY Vertical position (y-coordinate). The Y coordinate of the top left hand corner of your Image data. Defaults to 0.
     * @param dirtyWidth Width of the rectangle to be painted. Defaults to the width of the image data.
     * @param dirtyHeight Height of the rectangle to be painted. Defaults to the height of the image data.
     * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/putImageData
     */
    putImageData(imagedata: ImageData, x: number, y: number, dirtyX: number, dirtyY: number, dirtyWidth: number, dirtyHeight: number): this;
    /**
     * Fills the current or given path with the current fill style using the non-zero or even-odd winding rule.
     * @param fillRule The algorithm by which to determine if a point is inside a path or outside a path.
     * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/fill
     */
    fill(fillRule?: CanvasFillRule): this;
    /**
     * Fills the current or given path with the current fill style using the non-zero or even-odd winding rule.
     * @param path The path to fill.
     * @param fillRule The algorithm by which to determine if a point is inside a path or outside a path.
     * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/fill
     */
    fill(path: Path2D, fillRule?: CanvasFillRule): this;
    /**
     * Add a text.
     * @param text The text to write.
     * @param x The position x to start drawing the element.
     * @param y The position y to start drawing the element.
     * @param maxWidth The maximum width to draw. If specified, and the string is computed to be wider than this width,
     * the font is adjusted to use a more horizontally condensed font.
     * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/fillText
     */
    printText(text: string, x: number, y: number, maxWidth?: number): this;
    /**
     * Add responsive text
     * @param text The text to write.
     * @param x The position x to start drawing the element.
     * @param y The position y to start drawing the element.
     * @param maxWidth The max length in pixels for the text.
     * @example
     * new Canvas(400, 300)
     *     .setTextFont('40px Tahoma')
     *     .printResponsiveText('Hello World', 30, 30, 50)
     *     .png();
     */
    printResponsiveText(text: string, x: number, y: number, maxWidth: number): this;
    /**
     * Add text with line breaks (node-canvas and web canvas compatible)
     * @param text The text to write.
     * @param x The position x to start drawing the element.
     * @param y The position y to start drawing the element.
     * @example
     * new Canvas(400, 300)
     *     .setTextFont('25px Tahoma')
     *     .printMultilineText('This is a really\nlong text!', 139, 360)
     *     .png();
     */
    printMultilineText(text: string, x: number, y: number): this;
    /**
     * Wrap the text in multiple lines and write it
     * @param text The text to wrap and write.
     * @param x The position x to start drawing the element.
     * @param y The position y to start drawing the element.
     * @param wrapWidth The width in pixels of the line wrap
     * @example
     * new Canvas(400, 300)
     *     .setTextFont('25px Tahoma')
     *     .printWrappedText('This is a really long text!', 139, 360)
     *     .png();
     */
    printWrappedText(text: string, x: number, y: number, wrapWidth: number): this;
    /**
     * Strokes the current or given path with the current stroke style using the non-zero winding rule.
     * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/stroke
     */
    stroke(): this;
    /**
     * Paints a rectangle which has a starting point at (X, Y) and has a w width and an h height onto the canvas, using
     * the current stroke style.
     * @param x The x axis of the coordinate for the rectangle starting point.
     * @param y The y axis of the coordinate for the rectangle starting point.
     * @param width The rectangle's width.
     * @param height The rectangle's height.
     * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/strokeRect
     */
    printStrokeRectangle(x: number, y: number, width: number, height: number): this;
    /**
     * Add stroked text.
     * @param text The text to write.
     * @param x The position x to start drawing the element.
     * @param y The position y to start drawing the element.
     * @param maxWidth The maximum width to draw. If specified, and the string is computed to be wider than this width,
     * the font is adjusted to use a more horizontally condensed font.
     * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/strokeText
     */
    printStrokeText(text: string, x: number, y: number, maxWidth?: number): this;
    /**
     * Measure a text's width given a string.
     * @param text The text to measure.
     * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/measureText
     * @example
     * const size = new Canvas(500, 400)
     *     .setTextFont('40px Tahoma')
     *     .measureText('Hello World!'); // Returns a number
     *
     * const newSize = size.width < 500 ? 40 : (500 / size.width) * 40;
     *
     * new Canvas(500, 400)
     *     .setTextFont(`${newSize}px Tahoma`)
     *     .printText('Hello World!', 30, 50)
     *     .png(); // Returns a Buffer
     * @example
     * new Canvas(500, 400)
     *     .setTextFont('40px Tahoma')
     *     .process((canvas) => {
     *         const size = canvas.measureText('Hello World!');
     *         const newSize = size.width < 500 ? 40 : (500 / size.width) * 40;
     *         this.setTextFont(`${newSize}px Tahoma`);
     *     })
     *     .printText('Hello World!', 30, 50)
     *     .png(); // Returns a Buffer
     */
    measureText(text: string): TextMetrics;
    /**
     * Set the new font size, unlike setTextFont, this only requires the number.
     * @param size The new size to set
     */
    setTextSize(size: number): this;
    /**
     * Specifies the color or style to use for the lines around shapes.
     * @param color A canvas' color resolvable.
     * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/strokeStyle
     */
    setStroke(color: string | CanvasGradient | CanvasPattern): this;
    /**
     * Sets the thickness of lines in space units.
     * @param width A number specifying the line width in space units.
     * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/lineWidth
     */
    setLineWidth(width: number): this;
    setStrokeWidth(width: number): this;
    /**
     * Sets the line dash pattern offset or "phase" to achieve a "marching ants" effect
     * @param value A float specifying the amount of the offset. Initially 0.0.
     * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/lineDashOffset
     */
    setLineDashOffset(value: number): this;
    /**
     * Determines how two connecting segments (of lines, arcs or curves) with non-zero lengths in a shape are joined
     * together (degenerate segments with zero lengths, whose specified endpoints and control points are exactly at the
     * same position, are skipped).
     * @param value The line join type.
     * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/lineJoin
     */
    setLineJoin(value: CanvasLineJoin): this;
    /**
     * Determines how the end points of every line are drawn. There are three possible values for this property and
     * those are: butt, round and square. By default this property is set to butt.
     * @param value The line join type.
     * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/lineCap
     */
    setLineCap(value: CanvasLineCap): this;
    /**
     * Sets the line dash pattern used when stroking lines, using an array of values which specify alternating lengths
     * of lines and gaps which describe the pattern.
     * @param segments An Array of numbers which specify distances to alternately draw a line and a gap (in coordinate
     * space units). If the number of elements in the array is odd, the elements of the array get copied and
     * concatenated. For example, [5, 15, 25] will become [5, 15, 25, 5, 15, 25]. If the array is empty, the line dash
     * list is cleared and line strokes return to being solid.
     * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/setLineDash
     */
    setLineDash(segments: number[]): this;
    /**
     * Add an image at a position (x, y) with the source image's width and height.
     * @param image The image.
     * @param dx The x-axis coordinate in the destination canvas at which to place the top-left corner of the source `image`.
     * @param dy The y-axis coordinate in the destination canvas at which to place the top-left corner of the source `image`.
     * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/drawImage
     */
    printImage(image: ImageResolvable, dx: number, dy: number): this;
    /**
     * Add an image at a position (x, y) with a given width and height.
     * @param image The image.
     * @param dx The x-axis coordinate in the destination canvas at which to place the top-left corner of the source `image`.
     * @param dy The y-axis coordinate in the destination canvas at which to place the top-left corner of the source `image`.
     * @param dw The width to draw the `image` in the destination canvas. This allows scaling of the drawn image.
     * @param dh The height to draw the `image` in the destination canvas. This allows scaling of the drawn image.
     * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/drawImage
     */
    printImage(image: ImageResolvable, dx: number, dy: number, dw: number, dh: number): this;
    /**
     * Add an image at a position (x, y) with a given width and height, from a specific source rectangle.
     * @param image The image.
     * @param sx The x-axis coordinate of the top left corner of the sub-rectangle of the source `image` to draw into the destination context.
     * @param sy The y-axis coordinate of the top left corner of the sub-rectangle of the source `image` to draw into the destination context.
     * @param sw The width of the sub-rectangle of the source `image` to draw into the destination context.
     * @param sh The height of the sub-rectangle of the source `image` to draw into the destination context.
     * @param dx The x-axis coordinate in the destination canvas at which to place the top-left corner of the source `image`.
     * @param dy The y-axis coordinate in the destination canvas at which to place the top-left corner of the source `image`.
     * @param dw The width to draw the `image` in the destination canvas. This allows scaling of the drawn image.
     * @param dh The height to draw the `image` in the destination canvas. This allows scaling of the drawn image.
     * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/drawImage
     */
    printImage(image: ImageResolvable, sx: number, sy: number, sw: number, sh: number, dx: number, dy: number, dw: number, dh: number): this;
    /**
     * Add a round image.
     * @param imageOrBuffer The image.
     * @param x The X coordinate in the destination canvas at which to place the top-left corner of the source image.
     * @param y The Y coordinate in the destination canvas at which to place the top-left corner of the source image.
     * @param width The width to draw the image in the destination canvas. This allows scaling of the drawn image. If not specified, the image is not scaled in width when drawn.
     * @param height The height to draw the image in the destination canvas. This allows scaling of the drawn image. If not specified, the image is not scaled in height when drawn.
     * @param radius The radius for the circle
     */
    printCircularImage(imageOrBuffer: ImageResolvable, x: number, y: number, radius: number, options?: PrintCircularOptions): this;
    /**
     * Add a beveled image.
     * @param imageOrBuffer The image.
     * @param x The position x to start drawing the element.
     * @param y The position y to start drawing the element.
     * @param width The width of the element.
     * @param height The height of the element.
     * @param radius The radius for the new image.
     */
    printRoundedImage(imageOrBuffer: ImageResolvable, x: number, y: number, width: number, height: number, radius: BeveledRadiusOptions | number): this;
    /**
     * Add a circle or semi circle.
     * @param x The position x in the center of the circle.
     * @param y The position y in the center of the circle.
     * @param radius The radius for the clip.
     */
    printCircle(x: number, y: number, radius: number): this;
    /**
     * Add a rectangle.
     * @param x The position x to start drawing the element.
     * @param y The position y to start drawing the element.
     * @param width The width of the element.
     * @param height The height of the element.
     * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/fillRect
     */
    printRectangle(x: number, y: number, width: number, height: number): this;
    /**
     * Add a beveled rectangle.
     * @param x The position x to start drawing the element.
     * @param y The position y to start drawing the element.
     * @param width  The width of the element.
     * @param height The height of the element.
     * @param radius The radius for the bevels.
     * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/fillRect
     * @example
     * // Radius argument
     * new Canvas(200, 200)
     *     .printRoundedRectangle(0, 0, 200, 50, 35)
     *     .png();
     *
     * @example
     * // Configured bevels
     * new Canvas(200, 200)
     *     .printRoundedRectangle(0, 0, 200, 50, {
     *         // Top left border
     *         tl: 15,
     *         // Top right border
     *         tr: 20,
     *         // Bottom left border
     *         bl: 5,
     *         // Bottom right border
     *         br: 10
     *     })
     *     .png();
     *
     * @example
     * // Top bevels only
     * new Canvas(200, 200)
     *     .printRoundedRectangle(0, 0, 200, 50, { tl: 20, tr: 20, bl: 0, br: 0 })
     *     .png();
     */
    printRoundedRectangle(x: number, y: number, width: number, height: number, radius: number | BeveledRadiusOptions): this;
    /**
     * Create a round path.
     * @param dx The position x in the center of the clip's circle.
     * @param dy The position y in the center of the clip's circle.
     * @param radius The radius for the clip.
     * @param start The degree in radians to start drawing the circle.
     * @param angle The degree in radians to finish drawing the circle, defaults to a full circle.
     * @param antiClockwise Whether the path should be anti-clockwise.
     */
    createCircularPath(dx: number, dy: number, radius: number, start?: number, angle?: number, antiClockwise?: boolean): this;
    /**
     * Create a round clip.
     * @param dx The position x in the center of the clip's circle.
     * @param dy The position y in the center of the clip's circle.
     * @param radius The radius for the clip.
     * @param start The degree in radians to start drawing the circle.
     * @param angle The degree in radians to finish drawing the circle, defaults to a full circle.
     * @param antiClockwise Whether the path should be anti-clockwise.
     * @see createRoundPath
     */
    createCircularClip(dx: number, dy: number, radius: number, start?: number, angle?: number, antiClockwise?: boolean): this;
    /**
     * Create a rectangle path.
     * @param x The position x in the left corner.
     * @param y The position y in the upper corner.
     * @param width The width of the rectangle.
     * @param height The height of the rectangle.
     */
    createRectanglePath(x: number, y: number, width: number, height: number): this;
    /**
     * Create a rectangle clip.
     * @param x The position x in the left corner.
     * @param y The position y in the upper corner.
     * @param width The width of the rectangle.
     * @param height The height of the rectangle.
     */
    createRectangleClip(x: number, y: number, width: number, height: number): this;
    /**
     * Create a beveled path.
     * @param x The position x to start drawing clip.
     * @param y The position y to start drawing clip.
     * @param width The width of clip.
     * @param height The height of clip.
     * @param radius The radius for clip's rounded borders.
     */
    createRoundedPath(x: number, y: number, width: number, height: number, radius: number | BeveledRadiusOptions): this;
    /**
     * Create a beveled clip.
     * @param x The position x to start drawing clip.
     * @param y The position y to start drawing clip.
     * @param width The width of clip.
     * @param height The height of clip.
     * @param radius The radius for clip's rounded borders.
     * @example
     * // Radius argument, fill the content
     * new Canvas(200, 200)
     *     .createRoundedClip(0, 0, 200, 50, 35)
     *     .fill()
     *     .png();
     *
     * @example
     * // Configured bevels
     * new Canvas(200, 200)
     *     .createRoundedClip(0, 0, 200, 50, {
     *         // Top left border
     *         tl: 15,
     *         // Top right border
     *         tr: 20,
     *         // Bottom left border
     *         bl: 5,
     *         // Bottom right border
     *         br: 10
     *     })
     *     // Add an image with the shape of the beveled clip using different borders
     *     .printImage(buffer, 0, 0, 200, 50)
     *     .png();
     *
     * @example
     * // Top bevels only
     * new Canvas(200, 200)
     *     .createRoundedClip(0, 0, 200, 50, { tl: 20, tr: 20, bl: 0, br: 0 })
     *     .printImage(buffer, 0, 0, 200, 50)
     *     .png();
     */
    createRoundedClip(x: number, y: number, width: number, height: number, radius: number | BeveledRadiusOptions): this;
    /**
     * Set a color for the canvas' context.
     * @param color A canvas' color resolvable.
     * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/fillStyle
     */
    setColor(color: string | CanvasGradient | CanvasPattern): this;
    /**
     * Change the font.
     * @param font The font's name to set.
     * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/font
     */
    setTextFont(font: string): this;
    /**
     * Change the font alignment.
     * @param align The font's alignment to set.
     * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/textAlign
     */
    setTextAlign(align: CanvasTextAlign): this;
    /**
     * Change the font's baseline.
     * @param baseline The font's baseline to set.
     * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/textBaseline
     */
    setTextBaseline(baseline: CanvasTextBaseline): this;
    /**
     * Starts a new path by emptying the list of sub-paths.
     * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/beginPath
     */
    beginPath(): this;
    /**
     * Causes the point of the pen to move back to the start of the current sub-path.
     * If the shape has already been closed or has only one point, this function does nothing.
     * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/closePath
     */
    closePath(): this;
    /**
     * Creates a pattern using the specified image. It repeats the source in the directions specified by the repetition
     * argument, and returns it.
     * @param image A Canvas Image to be used as the image to repeat.
     * @param repetition The repeat mode.
     * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/createPattern
     */
    createPattern(image: PatternResolvable, repetition: PatternRepeat): CanvasPattern;
    /**
     * Creates a pattern using the specified image. It repeats the source in the directions specified by the repetition
     * argument, and prints it.
     * @param image A Canvas Image to be used as the image to repeat.
     * @param repetition The repeat mode.
     * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/createPattern
     */
    printPattern(image: PatternResolvable, repetition: PatternRepeat): this;
    /**
     * Creates a gradient along the line given by the coordinates represented by the parameters.
     * The coordinates are global, the second point does not rely on the position of the first and vice versa.
     * @param x0 The x axis of the coordinate of the start point.
     * @param y0 The y axis of the coordinate of the start point.
     * @param x1 The x axis of the coordinate of the end point.
     * @param y1 The y axis of the coordinate of the end point.
     * @param steps The gradient steps.
     * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/createLinearGradient
     */
    createLinearGradient(x0: number, y0: number, x1: number, y1: number, steps?: readonly GradientStop[]): CanvasGradient;
    /**
     * Creates a gradient along the line given by the coordinates represented by the parameters.
     * The coordinates are global, the second point does not rely on the position of the first and vice versa. This
     * method is chainable and calls setColor after creating the gradient.
     * @param x0 The x axis of the coordinate of the start point.
     * @param y0 The y axis of the coordinate of the start point.
     * @param x1 The x axis of the coordinate of the end point.
     * @param y1 The y axis of the coordinate of the end point.
     * @param steps The gradient steps.
     * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/createLinearGradient
     * @example
     * new Canvas(200, 200)
     *     .printLinearColorGradient(0, 0, 200, 50, [
     *         { position: 0, color: 'white' },
     *         { position: 0.25, color: 'red' },
     *         { position: 0.5, color: 'blue' }
     *     ])
     *     .printRectangle(10, 10, 200, 100)
     */
    printLinearColorGradient(x0: number, y0: number, x1: number, y1: number, steps?: readonly GradientStop[]): this;
    /**
     * Creates a gradient along the line given by the coordinates represented by the parameters.
     * The coordinates are global, the second point does not rely on the position of the first and vice versa. This
     * method is chainable and calls setStroke after creating the gradient.
     * @param x0 The x axis of the coordinate of the start point.
     * @param y0 The y axis of the coordinate of the start point.
     * @param x1 The x axis of the coordinate of the end point.
     * @param y1 The y axis of the coordinate of the end point.
     * @param steps The gradient steps.
     * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/createLinearGradient
     * @example
     * new Canvas(200, 200)
     *     .printLinearStrokeGradient(0, 0, 200, 50, [
     *         { position: 0, color: 'white' },
     *         { position: 0.25, color: 'red' },
     *         { position: 0.5, color: 'blue' }
     *     ])
     *     .printRectangle(10, 10, 200, 100)
     */
    printLinearStrokeGradient(x0: number, y0: number, x1: number, y1: number, steps?: readonly GradientStop[]): this;
    /**
     * Creates a radial gradient given by the coordinates of the two circles represented by the parameters.
     * @param x0 The x axis of the coordinate of the start circle.
     * @param y0 The y axis of the coordinate of the start circle.
     * @param r0 The radius of the start circle.
     * @param x1 The x axis of the coordinate of the end circle.
     * @param y1 The y axis of the coordinate of the end circle.
     * @param r1 The radius of the end circle.
     * @param steps The gradient steps.
     * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/createRadialGradient
     */
    createRadialGradient(x0: number, y0: number, r0: number, x1: number, y1: number, r1: number, steps?: readonly GradientStop[]): CanvasGradient;
    /**
     * Creates a radial gradient given by the coordinates of the two circles represented by the parameters. This
     * method is chainable and calls setColor after creating the gradient.
     * @param x0 The x axis of the coordinate of the start circle.
     * @param y0 The y axis of the coordinate of the start circle.
     * @param r0 The radius of the start circle.
     * @param x1 The x axis of the coordinate of the end circle.
     * @param y1 The y axis of the coordinate of the end circle.
     * @param r1 The radius of the end circle.
     * @param steps The gradient steps.
     * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/createRadialGradient
     */
    printRadialColorGradient(x0: number, y0: number, r0: number, x1: number, y1: number, r1: number, steps?: readonly GradientStop[]): this;
    /**
     * Creates a radial gradient given by the coordinates of the two circles represented by the parameters. This
     * method is chainable and calls setStroke after creating the gradient.
     * @param x0 The x axis of the coordinate of the start circle.
     * @param y0 The y axis of the coordinate of the start circle.
     * @param r0 The radius of the start circle.
     * @param x1 The x axis of the coordinate of the end circle.
     * @param y1 The y axis of the coordinate of the end circle.
     * @param r1 The radius of the end circle.
     * @param steps The gradient steps.
     * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/createRadialGradient
     */
    printRadialStrokeGradient(x0: number, y0: number, r0: number, x1: number, y1: number, r1: number, steps?: readonly GradientStop[]): this;
    /**
     * Adds an ellipse to the path which is centered at (X, Y) position with the radius radiusX and radiusY starting at
     * startAngle and ending at endAngle going in the given direction by anticlockwise (defaulting to clockwise).
     * @param x The x axis of the coordinate for the ellipse's center.
     * @param y The y axis of the coordinate for the ellipse's center.
     * @param radiusX The ellipse's major-axis radius.
     * @param radiusY The ellipse's minor-axis radius.
     * @param rotation The rotation for this ellipse, expressed in radians.
     * @param startAngle The starting point, measured from the x axis, from which it will be drawn, expressed in radians.
     * @param endAngle The end ellipse's angle to which it will be drawn, expressed in radians.
     * @param anticlockwise An optional Boolean which, if true, draws the ellipse anticlockwise (counter-clockwise), otherwise in a clockwise direction.
     * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/ellipse
     */
    createEllipsePath(x: number, y: number, radiusX: number, radiusY: number, rotation: number, startAngle: number, endAngle: number, anticlockwise?: boolean): this;
    /**
     * Creates an ellipse clip which is centered at (X, Y) position with the radius radiusX and radiusY starting at
     * startAngle and ending at endAngle going in the given direction by anticlockwise (defaulting to clockwise).
     * @param x The x axis of the coordinate for the ellipse's center.
     * @param y The y axis of the coordinate for the ellipse's center.
     * @param radiusX The ellipse's major-axis radius.
     * @param radiusY The ellipse's minor-axis radius.
     * @param rotation The rotation for this ellipse, expressed in radians.
     * @param startAngle The starting point, measured from the x axis, from which it will be drawn, expressed in radians.
     * @param endAngle The end ellipse's angle to which it will be drawn, expressed in radians.
     * @param anticlockwise An optional Boolean which, if true, draws the ellipse anticlockwise (counter-clockwise), otherwise in a clockwise direction.
     * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/ellipse
     */
    createEllipseClip(x: number, y: number, radiusX: number, radiusY: number, rotation: number, startAngle: number, endAngle: number, anticlockwise?: boolean): this;
    /**
     * Adds an arc to the path which is centered at (X, Y) position with radius r starting at startAngle and ending at
     * endAngle going in the given direction by anticlockwise (defaulting to clockwise).
     * @param x The X coordinate of the arc's center.
     * @param y The Y coordinate of the arc's center.
     * @param radius The arc's radius.
     * @param startAngle The angle at which the arc starts, measured clockwise from the positive x axis and expressed in radians.
     * @param endAngle The angle at which the arc ends, measured clockwise from the positive x axis and expressed in radians.
     * @param anticlockwise An optional Boolean which, if true, causes the arc to be drawn counter-clockwise between the two angles. By default it is drawn clockwise.
     * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/arc
     */
    arc(x: number, y: number, radius: number, startAngle: number, endAngle: number, anticlockwise?: boolean): this;
    /**
     * Adds an arc to the path with the given control points and radius, connected to the previous point by a straight line.
     * @param x1 The x axis of the coordinate for the first control point.
     * @param y1 The y axis of the coordinate for the first control point.
     * @param x2 The x axis of the coordinate for the second control point.
     * @param y2 The y axis of the coordinate for the second control point.
     * @param radius The arc's radius.
     * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/arcTo
     */
    arcTo(x1: number, y1: number, x2: number, y2: number, radius: number): this;
    /**
     * Adds a quadratic Bézier curve to the path. It requires two points. The first point is a control point and the
     * second one is the end point. The starting point is the last point in the current path, which can be changed using
     * moveTo() before creating the quadratic Bézier curve.
     * @param cpx The x axis of the coordinate for the control point.
     * @param cpy The y axis of the coordinate for the control point.
     * @param x The x axis of the coordinate for the end point.
     * @param y The y axis of the coordinate for the end point.
     * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/quadraticCurveTo
     */
    quadraticCurveTo(cpx: number, cpy: number, x: number, y: number): this;
    /**
     * Adds a cubic Bézier curve to the path. It requires three points. The first two points are control points and the
     * third one is the end point. The starting point is the last point in the current path, which can be changed using
     * moveTo() before creating the Bézier curve.
     * @param cp1x The x axis of the coordinate for the first control point.
     * @param cp1y The y axis of the coordinate for first control point.
     * @param cp2x The x axis of the coordinate for the second control point.
     * @param cp2y The y axis of the coordinate for the second control point.
     * @param x The x axis of the coordinate for the end point.
     * @param y The y axis of the coordinate for the end point.
     * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/bezierCurveTo
     */
    bezierCurveTo(cp1x: number, cp1y: number, cp2x: number, cp2y: number, x: number, y: number): this;
    /**
     * Connects the last point in the sub-path to the x, y coordinates with a straight line
     * @param x The x axis of the coordinate for the end of the line.
     * @param y The y axis of the coordinate for the end of the line.
     * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/lineTo
     */
    lineTo(x: number, y: number): this;
    /**
     * Moves the starting point of a new sub-path to the (X, Y) coordinates.
     * @param x The x axis of the point.
     * @param y The y axis of the point.
     * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/moveTo
     */
    moveTo(x: number, y: number): this;
    /**
     * Set the shadow's blur.
     * @param radius The shadow's blur radius to set.
     * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/shadowBlur
     */
    setShadowBlur(radius: number): this;
    /**
     * Set the shadow's color.
     * @param color A canvas' color resolvable to set as shadow's color.
     * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/shadowColor
     */
    setShadowColor(color: string): this;
    /**
     * Set the property that specifies the distance that the shadow will be offset in horizontal distance.
     * @param value The value in pixels for the distance.
     * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/shadowOffsetX
     */
    setShadowOffsetX(value: number): this;
    /**
     * Set the property that specifies the distance that the shadow will be offset in vertical distance.
     * @param value The value in pixels for the distance.
     * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/shadowOffsetY
     */
    setShadowOffsetY(value: number): this;
    /**
     * Sets the miter limit ratio in space units. When getting, it returns the current value (10.0 by default). When
     * setting, zero, negative, Infinity and NaN values are ignored; otherwise the current value is set to the new value.
     * @param value A number specifying the miter limit ratio in space units. Zero, negative, Infinity and NaN values
     * are ignored.
     * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/miterLimit
     */
    setMiterLimit(value: number): this;
    /**
     * Sets the type of compositing operation to apply when drawing new shapes, where type is a string identifying which
     * of the compositing or blending mode operations to use.
     * @param type The global composite operation mode.
     * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/globalCompositeOperation
     */
    setGlobalCompositeOperation(type: GlobalCompositeOperation): this;
    /**
     * Modify the alpha value that is applied to shapes and images before they are drawn into the canvas.
     * @param value The alpha value, from 0.0 (fully transparent) to 1.0 (fully opaque)
     * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/globalAlpha
     */
    setGlobalAlpha(value: number): this;
    /**
     * Modify whether or not image smoothing should be enabled.
     * @param value Whether or not image smoothing should be enabled.
     * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/imageSmoothingEnabled
     */
    setImageSmoothingEnabled(value: boolean): this;
    /**
     * Set antialias mode.
     * @param antialias The antialias mode.
     * @note This is a `canvas` extension.
     */
    setAntialiasMode(antialias: AntiAlias): this;
    /**
     * Set the text drawing mode. Using glyph is much faster than path for drawing, and when using a PDF context will
     * embed the text natively, so will be selectable and lower file size. The downside is that cairo does not have
     * any subpixel precision for glyph, so this will be noticeably lower quality for text positioning in cases such
     * as rotated text. Also, strokeText in glyph will act the same as fillText, except using the stroke style for
     * the fill.
     * @param mode The drawing mode.
     * @note This is a `canvas` extension.
     */
    setTextDrawingMode(mode: TextDrawingMode): this;
    /**
     * Change the pattern quality
     * @param pattern The pattern quality.
     * @note This is a `canvas` extension.
     */
    setPatternQuality(pattern: PatternQuality): this;
    /**
     * Sets the shadow blur and offsets to zero, then sets the shadow color to transparent. If shadows are not longer
     * used in a canvas and performance is critical, `.setShadowColor('transparent')` should be used instead, as of the
     * [note from Mozilla Developer Network](https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/shadowColor).
     * @example
     * new Canvas(500, 500)
     *     // Set a shadow color and blur
     *     .setShadowColor('rgba(23, 23, 23, 0.2)')
     *     .setShadowBlur(5)
     *     // Render the text with a blow effect
     *     .printText('Hello', 30, 50)
     *     // Reset the shadows
     *     .resetShadows()
     *     // Render the text without shadows
     *     .printText('World!', 30, 100);
     */
    resetShadows(): this;
    /**
     * Clear a circle.
     * @param x The position x in the center of the clip's circle.
     * @param y The position y in the center of the clip's circle.
     * @param radius The radius for the clip.
     * @param start The degree in radians to start drawing the circle.
     * @param angle The degree in radians to finish drawing the circle, defaults to a full circle.
     * @param antiClockwise Whether or not the angle should be anti-clockwise.
     */
    clearCircle(x: number, y: number, radius: number, start?: number, angle?: number, antiClockwise?: boolean): this;
    /**
     * Clear an area.
     * @param dx The position x to start drawing the element.
     * @param dy The position y to start drawing the element.
     * @param width The width of the element.
     * @param height The height of the element.
     * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/clearRect
     */
    clearRectangle(dx?: number, dy?: number, width?: number, height?: number): this;
    /**
     * Reports whether or not the specified point is contained in the current path.
     * @param x The X coordinate of the point to check.
     * @param y The Y coordinate of the point to check.
     * @param fillRule The algorithm by which to determine if a point is inside a path or outside a path.
     * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/isPointInPath
     */
    isPointInPath(x: number, y: number, fillRule?: CanvasFillRule): boolean;
    /**
     * Reports whether or not the specified point is contained in the given path.
     * @param path The {@link Path2D} to check against.
     * @param x The X coordinate of the point to check.
     * @param y The Y coordinate of the point to check.
     * @param fillRule The algorithm by which to determine if a point is inside a path or outside a path.
     * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/isPointInPath
     */
    isPointInPath(path: Path2D, x: number, y: number, fillRule?: CanvasFillRule): boolean;
    /**
     * Process data with this as the context
     * @param fn A callback function
     * @param args Extra arguments to pass to the function
     */
    process<Args extends readonly any[]>(fn: (this: this, canvas: this, ...args: Args) => unknown, ...args: Args): this;
    /**
     * Wraps a text into a width-limited multi-line text.
     * @param text The text to wrap
     * @param wrapWidth The wrap width
     * @example
     * // Calculate the wrapped text and return it, which
     * // is useful for storage to avoid re-calculating the
     * // wrapped text
     * const wrappedText = new Canvas(500, 300)
     *     .setTextFont('48px Verdana')
     *     .wrapText('Hello World, this is a quite\nlong text.', 300);
     * @example
     * // Wrap the text and add it
     * const buffer = new Canvas(500, 300)
     *     .setTextFont('48px Verdana')
     *     .process((canvas) => {
     *         const wrappedText = canvas.wrapText('Hello World, this is a quite\nlong text.');
     *         return canvas
     *             .setTextAlign('center')
     *             .addMultilineText(wrappedText, 250, 50)
     *     })
     *     .png(); // Returns a Buffer
     */
    wrapText(text: string, wrapWidth: number): string;
    /**
     * Creates a new, blank {@link ImageData} object with the specified dimensions. All of the pixels in the new object are transparent black.
     * @param sw The width to give the new {@link ImageData} object. A negative value flips the rectangle around the vertical axis.
     * @param sh The height to give the new {@link ImageData} object. A negative value flips the rectangle around the horizontal axis.
     * @param settings The settings to be used.
     */
    createImageData(sw: number, sh: number, settings?: ImageDataSettings): ImageData;
    /**
     * Creates a new, blank {@link ImageData} object with the dimensions of the specified object. All of the pixels in the new object are transparent black.
     * @param imageData An existing {@link ImageData} object from which to copy the width and height. The image itself is not copied.
     */
    createImageData(imageData: ImageData): ImageData;
    /**
     * Gets a JPEG buffer.
     * @param config The render configuration.
     * @returns A JPEG buffer.
     * @see {@link jpegAsync} for the async version.
     */
    jpeg(config?: JpegConfig): Buffer;
    /**
     * Gets a JPEG buffer.
     * @param config The render configuration.
     * @returns A JPEG buffer.
     * @see {@link jpeg} for the sync version.
     */
    jpegAsync(config?: JpegConfig): Promise<Buffer>;
    /**
     * Creates a JPEG stream.
     * @param config The config to use.
     * @note This is a `canvas` extension.
     */
    jpegStream(config?: JpegConfig): JPEGStream;
    /**
     * Gets a PNG buffer.
     * @param config The render configuration.
     * @returns A PNG buffer.
     * @see {@link pngAsync} for the async version.
     */
    png(config?: PngConfig): Buffer;
    /**
     * Gets a PNG buffer.
     * @param config The render configuration.
     * @returns A PNG buffer.
     * @see {@link png} for the sync version.
     */
    pngAsync(config?: PngConfig): Promise<Buffer>;
    /**
     * Creates a PNG stream.
     * @param config The config to use.
     * @note This is a `canvas` extension.
     */
    pngStream(config?: PngConfig): PNGStream;
    /**
     * Gets a PDF buffer.
     * @param config The render configuration.
     * @returns A PDF buffer.
     * @see {@link pdfAsync} for the async version.
     */
    pdf(config?: PdfConfig): Buffer;
    /**
     * Gets a PDF buffer.
     * @param config The render configuration.
     * @returns A PDF buffer.
     * @see {@link pdf} for the sync version.
     */
    pdfAsync(config?: PdfConfig): Promise<Buffer>;
    /**
     * Creates a PDF stream.
     * @param config The config to use.
     * @note This is a `canvas` extension.
     */
    pdfStream(config?: PdfConfig): PDFStream;
    /**
     * For image canvases, encodes the canvas as a PNG. For PDF canvases, encodes the canvas as a PDF. For SVG
     * canvases, encodes the canvas as an SVG.
     */
    toBuffer(): Buffer;
    /**
     * Encodes the canvas as a PNG.
     * @param mimeType the standard MIME type for the image format to return.
     * @param config The render configuration.
     */
    toBuffer(mimeType: 'image/png', config?: PngConfig): Buffer;
    /**
     * Encodes the canvas as a JPG.
     * @param mimeType the standard MIME type for the image format to return.
     * @param config The render configuration.
     */
    toBuffer(mimeType: 'image/jpeg', config?: JpegConfig): Buffer;
    /**
     * Encodes the canvas as a PDF.
     * @param mimeType the standard MIME type for the image format to return.
     * @param config The render configuration.
     */
    toBuffer(mimeType: 'application/pdf', config?: PdfConfig): Buffer;
    /**
     * Returns the unencoded pixel data, top-to-bottom. On little-endian (most) systems, the array will be ordered
     * BGRA; on big-endian systems, it will be ARGB.
     * @param mimeType the standard MIME type for the image format to return.
     */
    toBuffer(mimeType: 'raw'): Buffer;
    /**
     * For image canvases, encodes the canvas as a PNG. For PDF canvases, encodes the canvas as a PDF. For SVG
     * canvases, encodes the canvas as an SVG.
     */
    toBufferAsync(): Promise<Buffer>;
    /**
     * Encodes the canvas as a PNG.
     * @param mimeType the standard MIME type for the image format to return.
     * @param config The render configuration.
     */
    toBufferAsync(mimeType: 'image/png', config?: PngConfig): Promise<Buffer>;
    /**
     * Encodes the canvas as a JPG.
     * @param mimeType the standard MIME type for the image format to return.
     * @param config The render configuration.
     */
    toBufferAsync(mimeType: 'image/jpeg', config?: JpegConfig): Promise<Buffer>;
    /**
     * Encodes the canvas as a PDF.
     * @param mimeType the standard MIME type for the image format to return.
     * @param config The render configuration.
     */
    toBufferAsync(mimeType: 'application/pdf', config?: PdfConfig): Promise<Buffer>;
    /**
     * Render the canvas into a PNG Data URL.
     * @see https://developer.mozilla.org/en-US/docs/Web/API/HTMLCanvasElement/toDataURL
     */
    toDataURL(): string;
    /**
     * Render the canvas into a PNG Data URL.
     * @param type the standard MIME type for the image format to return.
     * @see https://developer.mozilla.org/en-US/docs/Web/API/HTMLCanvasElement/toDataURL
     */
    toDataURL(mimeType: 'image/png'): string;
    /**
     * Render the canvas into a JPEG Data URL.
     * @param type the standard MIME type for the image format to return.
     * @param quality The quality for the JPEG.
     * @see https://developer.mozilla.org/en-US/docs/Web/API/HTMLCanvasElement/toDataURL
     */
    toDataURL(mimeType: 'image/jpeg', quality?: number): string;
    protected parseFont(font: string): readonly [string] | readonly [string, number, string];
    protected resolveCircularCoordinates(imageOrBuffer: ImageResolvable, x: number, y: number, radius: number, fit: NonNullable<PrintCircularOptions['fit']>): ResolvedCircularCoordinates;
}
interface ResolvedCircularCoordinates {
    positionX: number;
    positionY: number;
    sizeX: number;
    sizeY: number;
}
export { loadImage, loadFont, NativeImage as Image };
export declare const resolveImage: typeof loadImage;
export declare const registerFont: typeof loadFont;
/**
 * @see https://developer.mozilla.org/en-US/docs/Web/CSS/font-variant-ligatures
 */
export type FontVariantLigatures = 'common-ligatures' | 'no-common-ligatures' | 'discretionary-ligatures' | 'no-discretionary-ligatures' | 'historical-ligatures' | 'no-historical-ligatures' | 'contextual' | 'no-contextual';
/**
 * @see https://developer.mozilla.org/en-US/docs/Web/CSS/font-variant-alternates
 */
export type FontVariantAlternates = 'historical-forms' | `stylistic(${string})` | `styleset(${string})` | `character-variant(${string})` | `swash(${string})` | `ornaments(${string})` | `annotation()${string}'`;
/**
 * @see https://developer.mozilla.org/en-US/docs/Web/CSS/font-variant-caps
 */
export type FontVariantCaps = 'small-caps' | 'all-small-caps' | 'petite-caps';
/**
 * @see https://developer.mozilla.org/en-US/docs/Web/CSS/font-variant-numeric
 */
export type FontVariantNumeric = 'lining-nums' | 'oldstyle-nums' | 'proportional-nums' | 'tabular-nums' | 'diagonal-fractions' | 'stacked-fractions' | 'ordinal' | 'slashed-zero';
/**
 * @see https://developer.mozilla.org/en-US/docs/Web/CSS/font-variant-east-asian
 */
export type FontVariantEastAsian = 'jis78' | 'jis83' | 'jis90' | 'jis04' | 'simplified' | 'traditional' | 'full-width' | 'proportional-width' | 'ruby';
export type FontVariantString = 'normal' | 'none' | string;
export type FontVariants = FontVariantLigatures | FontVariantAlternates | FontVariantCaps | FontVariantNumeric | FontVariantEastAsian;
type GetFontVariant<K extends FontVariants> = K extends FontVariantLigatures ? FontVariantLigatures : K extends FontVariantAlternates ? FontVariantAlternates : K extends FontVariantCaps ? FontVariantCaps : K extends FontVariantNumeric ? FontVariantNumeric : FontVariantEastAsian;
export declare function fontVariant<K1 extends FontVariantString>(k1: K1): K1;
export declare function fontVariant<K1 extends FontVariants, K2 extends Exclude<FontVariants, GetFontVariant<K1>>>(k1: K1, k2: K2): `${K1} ${K2}`;
export declare function fontVariant<K1 extends FontVariants, K2 extends Exclude<FontVariants, GetFontVariant<K1>>, K3 extends Exclude<FontVariants, GetFontVariant<K2>>>(k1: K1, k2: K2, k3: K3): `${K1} ${K2} ${K3}`;
export declare function fontVariant<K1 extends FontVariants, K2 extends Exclude<FontVariants, GetFontVariant<K1>>, K3 extends Exclude<FontVariants, GetFontVariant<K2>>, K4 extends Exclude<FontVariants, GetFontVariant<K3>>>(k1: K1, k2: K2, k3: K3, k4: K4): `${K1} ${K2} ${K3} ${K4}`;
export declare function fontVariant<K1 extends FontVariants, K2 extends Exclude<FontVariants, GetFontVariant<K1>>, K3 extends Exclude<FontVariants, GetFontVariant<K2>>, K4 extends Exclude<FontVariants, GetFontVariant<K3>>, K5 extends Exclude<FontVariants, GetFontVariant<K4>>>(k1: K1, k2: K2, k3: K3, k4: K4, k5: K5): `${K1} ${K2} ${K3} ${K4} ${K5}`;
/**
 * Invert an image
 * @param canvas The Canvas instance
 */
export declare const invert: (canvas: Canvas) => Canvas;
/**
 * Greyscale an image
 * @param canvas The Canvas instance
 */
export declare const greyscale: (canvas: Canvas) => Canvas;
export declare const grayscale: (canvas: Canvas) => Canvas;
/**
 * Invert then greyscale an image
 * @param canvas The Canvas instance
 */
export declare const invertGrayscale: (canvas: Canvas) => Canvas;
export declare const invertGreyscale: (canvas: Canvas) => Canvas;
/**
 * Give an image a sepia tone
 * @param canvas The Canvas instance
 */
export declare const sepia: (canvas: Canvas) => Canvas;
/**
 * Turn an image into a silhouette
 * @param canvas The Canvas instance
 */
export declare const silhouette: (canvas: Canvas) => Canvas;
/**
 * Apply a threshold to the image
 * @param canvas The Canvas instance
 * @param threshold The threshold to apply in a range of 0 to 255
 */
export declare const threshold: (canvas: Canvas, threshold: number) => Canvas;
/**
 * Apply an inverted threshold to the image
 * @param canvas The Canvas instance
 * @param threshold The threshold to apply in a range of 0 to 255
 */
export declare const invertedThreshold: (canvas: Canvas, threshold: number) => Canvas;
/**
 * Brighten an image
 * @param canvas The Canvas instance
 * @param brightness The brightness to apply in a range of 0 to 255
 */
export declare const brightness: (canvas: Canvas, brightness: number) => Canvas;
/**
 * Darken an image
 * @param canvas The Canvas instance
 * @param darkness The darkness to apply in a range of 0 to 255
 */
export declare const darkness: (canvas: Canvas, darkness: number) => Canvas;
export declare const myOldFriend: (canvas: Canvas, darkness: number) => Canvas;
/**
 * Convolute a image. This filter needs a fix.
 * @param canvas The Canvas instance
 * @param weights The weights
 * @param opaque Whether or not pixels should try to be opaque
 * @see https://www.html5rocks.com/en/tutorials/canvas/imagefilters/
 */
export declare const convolute: (canvas: Canvas, weights: readonly number[], opaque?: boolean) => Canvas;
/**
 * Display an image's edges
 * @param canvas The Canvas instance
 */
export declare const edge: (canvas: Canvas) => Canvas;
/**
 * Sharpen an image
 * @param canvas The Canvas instance
 * @param passes The amount of iterations to do
 */
export declare const sharpen: (canvas: Canvas, passes?: number) => Canvas;
/**
 * Blur an image
 * @param canvas The Canvas instance
 * @param passes The amount of iterations to do
 */
export declare const blur: (canvas: Canvas, passes?: number) => Canvas;
export declare const fontRegExp: RegExp;
export declare const getFontHeight: (font: string) => number;
export declare const textWrap: (canvas: Canvas, text: string, wrapWidth: number) => string;
/**
 * The names of the filters that take a string argument.
 */
type LiteralFilters = 'url';
export type Percentage<T extends number = number> = `${T}%`;
/**
 * The names of the filters that take a percentage argument.
 */
type PercentageFilters = 'brightness' | 'contrast' | 'grayscale' | 'invert' | 'opacity' | 'saturate' | 'sepia';
type RelativeLengthUnits = 'cap' | 'ch' | 'em' | 'ex' | 'ic' | 'lh' | 'rem' | 'rlh';
type RelativeUnits = RelativeLengthUnits | '%';
type ViewportPercentageUnits = 'vh' | 'vw' | 'vi' | 'vb' | 'vmin' | 'vmax';
type AbsoluteLengthUnits = 'px' | 'cm' | 'mm' | 'Q' | 'in' | 'pc' | 'pt';
type LengthUnits = RelativeUnits | ViewportPercentageUnits | AbsoluteLengthUnits;
export type Length<T extends number = number> = `${T}${LengthUnits}`;
/**
 * The names of the filters that take a length argument.
 */
type LengthFilters = 'blur';
type AngleUnits = 'deg' | 'grad' | 'rad' | 'turn';
export type Angle<T extends number = number> = `${T}${AngleUnits}`;
/**
 * The names of the filters that take an angle argument.
 */
type AngleFilters = 'hue-rotate';
export type Color = ColorKeyword | ColorHexadecimal | ColorRGB | ColorRGBA | ColorHSL | ColorHSLA;
interface Filter {
    <K extends LiteralFilters, V extends string>(name: K, url: V): `${K}(${V})`;
    <K extends PercentageFilters, V extends Percentage>(name: K, percentage: V): `${K}(${V})`;
    <K extends LengthFilters, V extends Length>(name: K, length: V): `${K}(${V})`;
    <K extends AngleFilters, V extends Angle>(name: K, angle: V): `${K}(${V})`;
    <Vx extends Length, Vy extends Length>(name: 'drop-shadow', x: Vx, y: Vy): `drop-shadow(${Vx} ${Vy})`;
    <Vx extends Length, Vy extends Length, Vb extends Length>(name: 'drop-shadow', x: Vx, y: Vy, blur: Vb): `drop-shadow(${Vx} ${Vy} ${Vb})`;
    <Vx extends Length, Vy extends Length, Vc extends Color>(name: 'drop-shadow', x: Vx, y: Vy, color: Vc): `drop-shadow(${Vx} ${Vy} ${Vc})`;
    <Vx extends Length, Vy extends Length, Vb extends Length, Vc extends Color>(name: 'drop-shadow', x: Vx, y: Vy, blur: Vb, color: Vc): `drop-shadow(${Vx} ${Vy} ${Vb} ${Vc})`;
    (value: 'none'): 'none';
}
export declare const filter: Filter;
/**
 * Represents a formatted hexadecimal value.
 */
export type ColorHexadecimal<T extends string = string> = `#${T}`;
/**
 * Utility to format an hexadecimal string into a CSS hexadecimal string.
 * @param hex The hexadecimal code.
 * @example
 * hex('FFF'); // -> '#FFF'
 * hex('0F0F0F'); // -> '#0F0F0F'
 */
export declare const hex: <T extends string>(hex: T) => `#${T}`;
/**
 * Represents a formatted RGB value.
 */
export type ColorRGB<R extends number = number, G extends number = number, B extends number = number> = `rgb(${R}, ${G}, ${B})`;
/**
 * Utility to format a RGB set of values into a string.
 * @param red The red value, must be a number between 0 and 255 inclusive.
 * @param green The green value, must be a number between 0 and 255 inclusive.
 * @param blue The blue value, must be a number between 0 and 255 inclusive.
 * @see https://en.wikipedia.org/wiki/RGB_color_model#Geometric_representation
 * @example
 * rgb(255, 150, 65); // -> 'rgb(255, 150, 65)'
 */
export declare const rgb: <R extends number, G extends number, B extends number>(red: R, green: G, blue: B) => `rgb(${R}, ${G}, ${B})`;
/**
 * Represents a formatted RGBA value.
 */
export type ColorRGBA<R extends number = number, G extends number = number, B extends number = number, A extends number = number> = `rgba(${R}, ${G}, ${B}, ${A})`;
/**
 * Utility to format a RGBA set of values into a string.
 * @param red The red value, must be a number between 0 and 255 inclusive.
 * @param green The green value, must be a number between 0 and 255 inclusive.
 * @param blue The blue value, must be a number between 0 and 255 inclusive.
 * @param alpha The alpha value, must be a number between 0 and 1 inclusive.
 * @see https://en.wikipedia.org/wiki/RGB_color_model#Geometric_representation
 * @example
 * rgba(255, 150, 65, 0.3); // -> 'rgba(255, 150, 65, 0.3)'
 */
export declare const rgba: <R extends number, G extends number, B extends number, A extends number>(red: R, green: G, blue: B, alpha: A) => `rgba(${R}, ${G}, ${B}, ${A})`;
/**
 * Represents a formatted HSL value.
 */
export type ColorHSL<H extends number = number, S extends number = number, L extends number = number> = `hsl(${H}, ${S}%, ${L}%)`;
/**
 * Utility to format a HSL set of values into a string.
 * @param hue The hue, must be a number between 0 and 360 inclusive.
 * @param saturation The saturation, must be a number between 0 and 100 inclusive.
 * @param lightness The lightness, must be a number between 0 and 100 inclusive, 0 will make it black, 100 will make it white.
 * @see https://en.wikipedia.org/wiki/HSL_and_HSV
 * @example
 * hsl(120, 100, 40); // -> 'hsl(120, 100, 40)'
 */
export declare const hsl: <H extends number, S extends number, L extends number>(hue: H, saturation: S, lightness: L) => `hsl(${H}, ${S}%, ${L}%)`;
/**
 * Represents a formatted HSL value.
 */
export type ColorHSLA<H extends number = number, S extends number = number, L extends number = number, A extends number = number> = `hsla(${H}, ${S}%, ${L}%, ${A})`;
/**
 * Utility to format a HSLA set of values into a string.
 * @param hue The hue, must be a number between 0 and 360 inclusive.
 * @param saturation The saturation, must be a number between 0 and 100 inclusive.
 * @param lightness The lightness, must be a number between 0 and 100 inclusive, 0 will make it black, 100 will make it white
 * @param alpha The alpha value, must be a number between 0 and 1 inclusive.
 * @see https://en.wikipedia.org/wiki/HSL_and_HSV
 * @example
 * hsla(120, 100, 40, 0.4); // -> 'hsla(120, 100, 40, 0.4)'
 */
export declare const hsla: <H extends number, S extends number, L extends number, A extends number>(hue: H, saturation: S, lightness: L, alpha: A) => `hsla(${H}, ${S}%, ${L}%, ${A})`;
/**
 * Utility to type-safely use CSS colors.
 * @param color The CSS keyword color.
 * @example
 * color('silver'); // ✔
 * color('some-imaginary-number'); // ❌
 */
export declare const color: (color: ColorKeyword) => ColorKeyword;
export type ColorKeyword = ColorKeywordLevel1 | ColorKeywordLevel2 | ColorKeywordLevel3 | ColorKeywordLevel4;
export type ColorKeywordLevel1 = 'black' | 'silver' | 'gray' | 'white' | 'maroon' | 'red' | 'purple' | 'fuchsia' | 'green' | 'lime' | 'olive' | 'yellow' | 'navy' | 'blue' | 'teal' | 'aqua';
export type ColorKeywordLevel2 = 'orange';
export type ColorKeywordLevel3 = 'aliceblue' | 'antiquewhite' | 'aquamarine' | 'azure' | 'beige' | 'bisque' | 'blanchedalmond' | 'blueviolet' | 'brown' | 'burlywood' | 'cadetblue' | 'chartreuse' | 'chocolate' | 'coral' | 'cornflowerblue' | 'cornsilk' | 'crimson' | 'cyan' | 'darkblue' | 'darkcyan' | 'darkgoldenrod' | 'darkgray' | 'darkgreen' | 'darkgrey' | 'darkkhaki' | 'darkmagenta' | 'darkolivegreen' | 'darkorange' | 'darkorchid' | 'darkred' | 'darksalmon' | 'darkseagreen' | 'darkslateblue' | 'darkslategray' | 'darkslategrey' | 'darkturquoise' | 'darkviolet' | 'deeppink' | 'deepskyblue' | 'dimgray' | 'dimgrey' | 'dodgerblue' | 'firebrick' | 'floralwhite' | 'forestgreen' | 'gainsboro' | 'ghostwhite' | 'gold' | 'goldenrod' | 'greenyellow' | 'grey' | 'honeydew' | 'hotpink' | 'indianred' | 'indigo' | 'ivory' | 'khaki' | 'lavender' | 'lavenderblush' | 'lawngreen' | 'lemonchiffon' | 'lightblue' | 'lightcoral' | 'lightcyan' | 'lightgoldenrodyellow' | 'lightgray' | 'lightgreen' | 'lightgrey' | 'lightpink' | 'lightsalmon' | 'lightseagreen' | 'lightskyblue' | 'lightslategray' | 'lightslategrey' | 'lightsteelblue' | 'lightyellow' | 'limegreen' | 'linen' | 'magenta' | 'mediumaquamarine' | 'mediumblue' | 'mediumorchid' | 'mediumpurple' | 'mediumseagreen' | 'mediumslateblue' | 'mediumspringgreen' | 'mediumturquoise' | 'mediumvioletred' | 'midnightblue' | 'mintcream' | 'mistyrose' | 'moccasin' | 'navajowhite' | 'oldlace' | 'olivedrab' | 'orangered' | 'orchid' | 'palegoldenrod' | 'palegreen' | 'paleturquoise' | 'palevioletred' | 'papayawhip' | 'peachpuff' | 'peru' | 'pink' | 'plum' | 'powderblue' | 'rosybrown' | 'royalblue' | 'saddlebrown' | 'salmon' | 'sandybrown' | 'seagreen' | 'seashell' | 'sienna' | 'skyblue' | 'slateblue' | 'slategray' | 'slategrey' | 'snow' | 'springgreen' | 'steelblue' | 'tan' | 'thistle' | 'tomato' | 'turquoise' | 'violet' | 'wheat' | 'whitesmoke' | 'yellowgreen';
export type ColorKeywordLevel4 = 'rebeccapurple';
//# sourceMappingURL=cairo.d.ts.map