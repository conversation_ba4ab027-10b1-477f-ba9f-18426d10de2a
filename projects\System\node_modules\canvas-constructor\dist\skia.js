"use strict";var t=require("node:util"),e=require("skia-canvas");class r{constructor(t,r){"number"==typeof t?(this.canvas=new e.Canvas(t,r),this.context=this.canvas.getContext("2d")):(this.canvas=t,this.context=null!=r?r:this.canvas.getContext("2d"))}addPage(t,e){return new r(this.canvas,this.canvas.newPage(t,e))}get width(){return this.canvas.width}set width(t){this.canvas.width=t}get height(){return this.canvas.height}set height(t){this.canvas.height=t}get direction(){return this.context.direction}get font(){return this.context.font}get globalAlpha(){return this.context.globalAlpha}get imageSmoothingEnabled(){return this.context.imageSmoothingEnabled}get imageSmoothingQuality(){return this.context.imageSmoothingQuality}get pages(){return this.canvas.pages}get fontVariant(){return this.context.fontVariant}get textTracking(){return this.context.textTracking}get textWrap(){return this.context.textWrap}get transform(){return this.context.getTransform()}get textFontHeight(){return m(this.context.font)}get lineDash(){return this.context.getLineDash()}changeCanvasSize(t,e){return this.changeCanvasWidth(t).changeCanvasHeight(e)}changeCanvasWidth(t){return this.width=t,this}changeCanvasHeight(t){return this.height=t,this}save(){return this.context.save(),this}restore(){return this.context.restore(),this}rotate(t){return this.context.rotate(t),this}scale(t,e){return this.context.scale(t,e),this}translate(t,e){return this.context.translate(t,e),this}clip(...t){return this.context.clip(...t),this}setTransform(...t){return this.context.setTransform(...t),this}resetTransform(){return this.context.resetTransform(),this}resetFilters(){return this.setFilter("none")}getImageData(t,e,r,n){return this.context.getImageData(null!=t?t:0,null!=e?e:0,null!=r?r:this.width,null!=n?n:this.height)}putImageData(...t){return this.context.putImageData(...t),this}fill(...t){return this.context.fill(...t),this}printText(t,e,r,...n){return this.context.fillText(t,e,r,...n),this}printResponsiveText(t,e,r,n){const[s,i,a]=this.parseFont(this.context.font);if("number"!=typeof i)return this.printText(t,e,r);const{width:o}=this.measureText(t);if(o<=n)return this.printText(t,e,r);const h=n/o*i;return this.save().setTextFont(`${s}${h}${a}`).printText(t,e,r).restore()}printMultilineText(t,e,r){const n=t.split(/\r?\n/);if(n.length<=1)return this.printText(t,e,r);const s=this.textFontHeight;let i=r;for(const t of n)this.printText(t,e,Math.floor(i)),i+=s;return this}printWrappedText(t,e,r,n){const s=v(this,t,n);return this.printMultilineText(s,e,r)}stroke(){return this.context.stroke(),this}printStrokeRectangle(t,e,r,n){return this.context.strokeRect(t,e,r,n),this}printStrokeText(t,e,r,n){return this.context.strokeText(t,e,r,n),this}measureText(t){return this.context.measureText(t)}setTextSize(t){const e=this.parseFont(this.context.font);return 1===e.length?this:this.setTextFont(`${e[0]}${t}${e[2]}`)}setStroke(t){return this.context.strokeStyle=t,this}setLineWidth(t){return this.context.lineWidth=t,this}setStrokeWidth(t){return this.setLineWidth(t)}setLineDashOffset(t){return this.context.lineDashOffset=t,this}setLineJoin(t){return this.context.lineJoin=t,this}setLineCap(t){return this.context.lineCap=t,this}setLineDash(t){return this.context.setLineDash(t),this}printImage(t,...e){var n;return this.context.drawImage((n=t)instanceof r?n.canvas:n,...e),this}printCircularImage(t,e,r,n,{fit:s="fill"}={}){const{positionX:i,positionY:a,sizeX:o,sizeY:h}=this.resolveCircularCoordinates(t,e,r,n,s);return this.save().createCircularClip(e,r,n,0,2*Math.PI,!1).printImage(t,i,a,o,h).restore()}printRoundedImage(t,e,r,n,s,i){return this.save().createRoundedClip(e,r,n,s,i).printImage(t,e,r,n,s).restore()}printCircle(t,e,r){return this.save().createCircularPath(t,e,r).fill().restore()}printRectangle(t,e,r,n){return this.context.fillRect(t,e,r,n),this}printRoundedRectangle(t,e,r,n,s){return this.save().createRoundedPath(t,e,r,n,s).fill().restore()}createCircularPath(t,e,r,n=0,s=2*Math.PI,i=!1){return this.context.beginPath(),this.context.arc(t,e,r,n,s,i),this}createCircularClip(t,e,r,n,s,i){return this.createCircularPath(t,e,r,n,s,i).clip()}createRectanglePath(t,e,r,n){return this.context.rect(t,e,r,n),this}createRectangleClip(t,e,r,n){return this.createRectanglePath(t,e,r,n).clip()}createRoundedPath(t,e,r,n,s){if(r>0&&n>0){let i;"number"==typeof s?i={tl:s=Math.min(s,r/2,n/2),tr:s,br:s,bl:s}:(i=s,s=Math.min(5,r/2,n/2));const{tl:a=s,tr:o=s,br:h=s,bl:c=s}=i;this.context.beginPath(),this.context.moveTo(t+a,e),this.context.lineTo(t+r-o,e),this.context.quadraticCurveTo(t+r,e,t+r,e+o),this.context.lineTo(t+r,e+n-h),this.context.quadraticCurveTo(t+r,e+n,t+r-h,e+n),this.context.lineTo(t+c,e+n),this.context.quadraticCurveTo(t,e+n,t,e+n-c),this.context.lineTo(t,e+a),this.context.quadraticCurveTo(t,e,t+a,e),this.context.closePath()}return this}createRoundedClip(t,e,r,n,s){return this.createRoundedPath(t,e,r,n,s).clip()}setColor(t){return this.context.fillStyle=t,this}setTextFont(t){return this.context.font=t,this}setTextAlign(t){return this.context.textAlign=t,this}setTextBaseline(t){return this.context.textBaseline=t,this}setFilter(t){return this.context.filter=t,this}beginPath(){return this.context.beginPath(),this}closePath(){return this.context.closePath(),this}createPattern(t,e){return this.context.createPattern((n=t)instanceof r?n.canvas:n,e);var n}printPattern(t,e){return this.setColor(this.createPattern(t,e))}createLinearGradient(t,e,r,n,s=[]){const i=this.context.createLinearGradient(t,e,r,n);for(const t of s)i.addColorStop(t.position,t.color);return i}printLinearColorGradient(t,e,r,n,s){const i=this.createLinearGradient(t,e,r,n,s);return this.setColor(i)}printLinearStrokeGradient(t,e,r,n,s){const i=this.createLinearGradient(t,e,r,n,s);return this.setStroke(i)}createRadialGradient(t,e,r,n,s,i,a=[]){const o=this.context.createRadialGradient(t,e,r,n,s,i);for(const t of a)o.addColorStop(t.position,t.color);return o}printRadialColorGradient(t,e,r,n,s,i,a){const o=this.createRadialGradient(t,e,r,n,s,i,a);return this.setColor(o)}printRadialStrokeGradient(t,e,r,n,s,i,a){const o=this.createRadialGradient(t,e,r,n,s,i,a);return this.setStroke(o)}createConicGradient(t,e,r,n=[]){const s=this.context.createConicGradient(t,e,r);for(const t of n)s.addColorStop(t.position,t.color);return s}printConicColorGradient(t,e,r,n){const s=this.createConicGradient(t,e,r,n);return this.setColor(s)}printConicStrokeGradient(t,e,r,n){const s=this.createConicGradient(t,e,r,n);return this.setStroke(s)}createEllipsePath(t,e,r,n,s,i,a,o){return this.context.ellipse(t,e,r,n,s,i,a,o),this}createEllipseClip(t,e,r,n,s,i,a,o){return this.createEllipsePath(t,e,r,n,s,i,a,o).clip()}arc(t,e,r,n,s,i){return this.context.arc(t,e,r,n,s,i),this}arcTo(t,e,r,n,s){return this.context.arcTo(t,e,r,n,s),this}quadraticCurveTo(t,e,r,n){return this.context.quadraticCurveTo(t,e,r,n),this}bezierCurveTo(t,e,r,n,s,i){return this.context.bezierCurveTo(t,e,r,n,s,i),this}lineTo(t,e){return this.context.lineTo(t,e),this}moveTo(t,e){return this.context.moveTo(t,e),this}setShadowBlur(t){return this.context.shadowBlur=t,this}setShadowColor(t){return this.context.shadowColor=t,this}setShadowOffsetX(t){return this.context.shadowOffsetX=t,this}setShadowOffsetY(t){return this.context.shadowOffsetY=t,this}setMiterLimit(t){return this.context.miterLimit=t,this}setGlobalCompositeOperation(t){return this.context.globalCompositeOperation=t,this}setGlobalAlpha(t){return this.context.globalAlpha=t,this}setImageSmoothingEnabled(t){return this.context.imageSmoothingEnabled=t,this}setImageSmoothingQuality(t){return this.context.imageSmoothingQuality=t,this}setFontVariant(t){return this.context.fontVariant=t,this}setTextTracking(t){return this.context.textTracking=t,this}setTextWrap(t){return this.context.textWrap=t,this}resetShadows(){return this.setShadowBlur(0).setShadowOffsetX(0).setShadowOffsetY(0).setShadowColor("transparent")}clearCircle(t,e,r,n=0,s=2*Math.PI,i=!1){return this.createCircularClip(t,e,r,n,s,i).clearRectangle(t-r,e-r,2*r,2*r)}clearRectangle(t=0,e=0,r=this.width,n=this.height){return this.context.clearRect(t,e,r,n),this}isPointInPath(...t){return this.context.isPointInPath(...t)}isPointInStroke(...t){return this.context.isPointInStroke(...t)}process(t,...e){return t.call(this,this,...e),this}wrapText(t,e){return v(this,t,e)}createImageData(...t){return this.context.createImageData(...t)}jpeg(t){return this.canvas.toBufferSync("jpeg",t)}jpegAsync(t){return this.canvas.toBuffer("jpeg",t)}png(t){return this.canvas.toBufferSync("png",t)}pngAsync(t){return this.canvas.toBuffer("png",t)}pdf(t){return this.canvas.toBufferSync("pdf",t)}pdfAsync(t){return this.canvas.toBuffer("pdf",t)}svg(t){return this.canvas.toBufferSync("svg",t)}svgAsync(t){return this.canvas.toBuffer("svg",t)}toBuffer(...t){return this.canvas.toBufferSync(...t)}toBufferAsync(...t){return this.canvas.toBuffer(...t)}toDataURL(...t){return this.canvas.toDataURL(...t)}saveAs(...t){return this.canvas.saveAsSync(...t),this}async saveAsAsync(...t){return await this.canvas.saveAs(...t),this}parseFont(t){const e=f.exec(t);return null===e?[t]:[t.slice(0,e.index),Number(e[1]),t.slice(e.index+e[1].length)]}resolveCircularCoordinates(t,e,r,n,s){const{width:i,height:a}=t;if("none"===s)return{positionX:e-i/2,positionY:r-a/2,sizeX:i,sizeY:a};const o=i/a,h=2*n;if("fill"===s||1===o)return{positionX:e-n,positionY:r-n,sizeX:h,sizeY:h};if("contain"===s)return o>1?{positionX:e-n,positionY:r-n/o,sizeX:h,sizeY:h/o}:{positionX:e-n*o,positionY:r-n,sizeX:h*o,sizeY:h};if(o>1){const t=h*o;return{positionX:e-t/2,positionY:r-h/2,sizeX:t,sizeY:h}}const c=h/o;return{positionX:e-h/2,positionY:r-c/2,sizeX:h,sizeY:c}}}const n=t.deprecate(e.loadImage,"resolveImage() is deprecated. Use loadImage() instead.");function s(...t){return e.FontLibrary.use(...t)}const i=t.deprecate(s,"registerFont() is deprecated. Use loadFont() instead.");const a=t=>{const e=t.getImageData(),{data:r}=e;for(let t=0;t<r.length;t+=4){const e=.2126*r[t]+.7152*r[t+1]+.0722*r[t+2];r[t]=e,r[t+1]=e,r[t+2]=e}return t.putImageData(e,0,0)},o=a,h=t=>{const e=t.getImageData(),{data:r}=e;for(let t=0;t<r.length;t+=4){const e=255-(.2126*r[t]+.7152*r[t+1]+.0722*r[t+2]);r[t]=e,r[t+1]=e,r[t+2]=e}return t.putImageData(e,0,0)},c=h,u=(t,e)=>{const r=t.getImageData(),{data:n}=r;for(let t=0;t<n.length;t+=4)n[t]-=e,n[t+1]-=e,n[t+2]-=e;return t.putImageData(r,0,0)},l=u,p=(t,e,r=!0)=>{const n=Math.round(Math.sqrt(e.length)),s=Math.floor(n/2),i=t.getImageData(),a=i.data,o=i.width,h=i.height,c=o,u=h,l=t.getImageData(),p=l.data,g=r?1:0;for(let t=0;t<u;t++)for(let r=0;r<c;r++){const i=t,u=r,l=4*(t*c+r);let x=0,d=0,f=0,m=0;for(let t=0;t<n;t++)for(let r=0;r<n;r++){const c=i+t-s,l=u+r-s;if(c>=0&&c<h&&l>=0&&l<o){const s=4*(c*o+l),i=e[t*n+r];x+=a[s]*i,d+=a[s+1]*i,f+=a[s+2]*i,m+=a[s+3]*i}}p[l]=x,p[l+1]=d,p[l+2]=f,p[l+3]=m+g*(255-m)}return t.putImageData(l,0,0)},g=[0,-1,0,-1,4,-1,0,-1,0],x=[0,-1,0,-1,5,-1,0,-1,0],d=[1/9,1/9,1/9,1/9,1/9,1/9,1/9,1/9,1/9],f=/([\d.]+)(px|pt|pc|in|cm|mm|%|em|ex|ch|rem|q)/i,m=(()=>{const t=new Map;return e=>{const r=t.get(e);if(r)return r;const n=f.exec(e);if(!n)return 0;let s=Number(n[1]);switch(n[2]){case"pt":s/=.75;break;case"pc":s*=16;break;case"in":s*=96;break;case"cm":s*=96/2.54;break;case"mm":s*=96/25.4;break;case"em":case"rem":s*=16/.75;break;case"q":s*=96/25.4/4}return t.set(e,s),s}})(),v=(t,e,r)=>{const n=[],s=[],i=t.measureText(" ").width;for(const a of e.split(/\r?\n/)){let e=r;for(const o of a.split(" ")){const a=t.measureText(o).width,h=a+i;h>e?(s.length&&(n.push(s.join(" ")),s.length=0),s.push(o),e=r-a):(e-=h,s.push(o))}s.length&&(n.push(s.join(" ")),s.length=0)}return n.join("\n")};Object.defineProperty(exports,"FontLibrary",{enumerable:!0,get:function(){return e.FontLibrary}}),Object.defineProperty(exports,"Image",{enumerable:!0,get:function(){return e.Image}}),Object.defineProperty(exports,"Path2D",{enumerable:!0,get:function(){return e.Path2D}}),Object.defineProperty(exports,"loadImage",{enumerable:!0,get:function(){return e.loadImage}}),exports.Canvas=r,exports.blur=(t,e=1)=>{for(let r=0;r<e;++r)p(t,d,!0);return t},exports.brightness=(t,e)=>{const r=t.getImageData(),{data:n}=r;for(let t=0;t<n.length;t+=4)n[t]+=e,n[t+1]+=e,n[t+2]+=e;return t.putImageData(r,0,0)},exports.color=t=>t,exports.convolute=p,exports.darkness=u,exports.edge=t=>p(t,g,!0),exports.filter=(t,...e)=>`${t}(${e.join(" ")})`,exports.fontRegExp=f,exports.fontVariant=function(...t){return t.join(" ")},exports.getFontHeight=m,exports.grayscale=o,exports.greyscale=a,exports.hex=t=>`#${t}`,exports.hsl=(t,e,r)=>`hsl(${t}, ${e}%, ${r}%)`,exports.hsla=(t,e,r,n)=>`hsla(${t}, ${e}%, ${r}%, ${n})`,exports.invert=t=>t.save().setGlobalCompositeOperation("difference").setColor("white").printRectangle(0,0,t.width,t.height).restore(),exports.invertGrayscale=h,exports.invertGreyscale=c,exports.invertedThreshold=(t,e)=>{const r=t.getImageData(),{data:n}=r;for(let t=0;t<n.length;t+=4){const r=.2126*n[t]+.7152*n[t+1]+.0722*n[t+2]>=e?0:255;n[t]=r,n[t+1]=r,n[t+2]=r}return t.putImageData(r,0,0)},exports.loadFont=s,exports.myOldFriend=l,exports.registerFont=i,exports.resolveImage=n,exports.rgb=(t,e,r)=>`rgb(${t}, ${e}, ${r})`,exports.rgba=(t,e,r,n)=>`rgba(${t}, ${e}, ${r}, ${n})`,exports.sepia=t=>{const e=t.getImageData(),{data:r}=e;for(let t=0;t<r.length;t+=4){const e=r[t],n=r[t+1],s=r[t+2];r[t]=.393*e+.769*n+.189*s,r[t+1]=.349*e+.686*n+.168*s,r[t+2]=.272*e+.534*n+.131*s}return t.putImageData(e,0,0)},exports.sharpen=(t,e=1)=>{for(let r=0;r<e;++r)p(t,x,!0);return t},exports.silhouette=t=>{const e=t.getImageData(),{data:r}=e;for(let t=0;t<r.length;t+=4)r[t]=0,r[t+1]=0,r[t+2]=0;return t.putImageData(e,0,0)},exports.textWrap=v,exports.threshold=(t,e)=>{const r=t.getImageData(),{data:n}=r;for(let t=0;t<n.length;t+=4){const r=.2126*n[t]+.7152*n[t+1]+.0722*n[t+2]>=e?255:0;n[t]=r,n[t+1]=r,n[t+2]=r}return t.putImageData(r,0,0)};
//# sourceMappingURL=skia.js.map
