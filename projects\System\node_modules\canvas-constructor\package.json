{"name": "canvas-constructor", "version": "7.0.1", "description": "A ES6 class for node-canvas with built-in functions and chained methods.", "author": "kyranet", "license": "MIT", "contributors": ["<PERSON><PERSON> Rom<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON><PERSON>", "favna <<EMAIL>>"], "browser": "dist/browser.umd.js", "unpkg": "dist/browser.umd.js", "exports": {"./browser": {"types": "./dist/browser.d.ts", "import": "./dist/browser.mjs", "require": "./dist/browser.js"}, "./cairo": {"types": "./dist/cairo.d.ts", "import": "./dist/cairo.mjs", "require": "./dist/cairo.js"}, "./napi-rs": {"types": "./dist/napi-rs.d.ts", "import": "./dist/napi-rs.mjs", "require": "./dist/napi-rs.js"}, "./skia": {"types": "./dist/skia.d.ts", "import": "./dist/skia.mjs", "require": "./dist/skia.js"}}, "sideEffects": false, "scripts": {"lint": "eslint scripts --ext mjs --fix", "docs": "yarn build:gen && typedoc", "update": "yarn upgrade-interactive --latest", "build": "yarn build:gen && yarn build:ts", "build:gen": "node ./scripts/generate.mjs", "build:ts": "rollup -c rollup.config.mjs", "watch": "yarn build -w", "bump": "cliff-jumper", "clean": "rimraf {browser,cairo,napi-rs,skia}.{d.ts,js,mjs} dist", "check-update": "cliff-jumper --dry-run", "prepack": "yarn build && pinst --disable", "_postinstall": "husky install .github/husky", "postpack": "pinst --enable"}, "dependencies": {"tslib": "^2.5.2"}, "devDependencies": {"@commitlint/cli": "^17.6.3", "@commitlint/config-conventional": "^17.6.3", "@favware/cliff-jumper": "^2.0.0", "@napi-rs/canvas": "0.1.40", "@sapphire/eslint-config": "^4.4.2", "@sapphire/prettier-config": "1.4.5", "@sapphire/ts-config": "4.0.0", "@typescript-eslint/eslint-plugin": "^5.59.6", "@typescript-eslint/parser": "^5.59.6", "canvas": "^2.11.2", "cz-conventional-changelog": "^3.3.0", "eslint": "^8.40.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "husky": "^8.0.3", "lint-staged": "^13.2.2", "pinst": "^3.0.0", "prettier": "^2.8.8", "pretty-quick": "^3.1.3", "rimraf": "^5.0.1", "rollup": "^3.22.0", "rollup-plugin-cleaner": "^1.0.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.34.1", "skia-canvas": "^1.0.1", "ts-node": "^10.9.1", "typedoc": "^0.24.7", "typescript": "^5.0.4"}, "resolutions": {"ansi-regex": "^5.0.1", "minimist": "^1.2.8"}, "repository": {"type": "git", "url": "git+https://github.com/kyranet/canvas-constructor.git"}, "files": ["dist", "browser.js", "browser.mjs", "browser.d.ts", "cairo.js", "cairo.mjs", "cairo.d.ts", "napi-rs.js", "napi-rs.mjs", "napi-rs.d.ts", "skia.js", "skia.mjs", "skia.d.ts"], "engines": {"node": ">=v14.18.0"}, "keywords": ["canvas", "graphic", "graphics", "image", "images", "avif", "jpeg", "jpg", "pdf", "png", "svg", "webp", "node-canvas", "skia-canvas", "napi-rs", "constructor", "chainable", "browser", "nodejs", "node.js", "cjs", "commonjs", "esm", "umd", "skia"], "bugs": {"url": "https://github.com/kyranet/canvas-constructor/issues"}, "homepage": "https://github.com/kyranet/canvas-constructor#readme", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "lint-staged": {"*.{mjs,js,ts}": "eslint --fix --ext mjs,js,ts"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "publishConfig": {"access": "public"}, "eslintConfig": {"extends": "@sapphire"}, "prettier": "@sapphire/prettier-config", "packageManager": "yarn@3.5.1", "volta": {"node": "18.16.0"}}