{"version": 3, "file": "binary.d.ts", "sourceRoot": "", "sources": ["../src/binary.ts"], "names": [], "mappings": "AACA,OAAO,KAAK,EAAE,YAAY,EAAE,MAAM,iBAAiB,CAAC;AAIpD,OAAO,EAAE,SAAS,EAAE,MAAM,cAAc,CAAC;AAEzC,cAAc;AACd,MAAM,MAAM,cAAc,GAAG,UAAU,GAAG,MAAM,EAAE,CAAC;AAEnD,cAAc;AACd,MAAM,WAAW,oBAAoB;IACnC,KAAK,EAAE,MAAM,CAAC;IACd,OAAO,EAAE,MAAM,CAAC;CACjB;AAED,cAAc;AACd,MAAM,WAAW,cAAc;IAC7B,OAAO,EAAE;QACP,OAAO,EAAE,MAAM,CAAC;QAChB,MAAM,EAAE,MAAM,CAAC;KAChB,CAAC;CACH;AAED;;;;GAIG;AACH,qBAAa,MAAO,SAAQ,SAAS;IACnC,IAAI,SAAS,IAAI,QAAQ,CAExB;IAED;;;OAGG;IACH,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,2BAA2B,CAAK;IAExD,kCAAkC;IAClC,MAAM,CAAC,QAAQ,CAAC,WAAW,OAAO;IAClC,wBAAwB;IACxB,MAAM,CAAC,QAAQ,CAAC,eAAe,KAAK;IACpC,yBAAyB;IACzB,MAAM,CAAC,QAAQ,CAAC,gBAAgB,KAAK;IACrC,2BAA2B;IAC3B,MAAM,CAAC,QAAQ,CAAC,kBAAkB,KAAK;IACvC,oEAAoE;IACpE,MAAM,CAAC,QAAQ,CAAC,gBAAgB,KAAK;IACrC,qBAAqB;IACrB,MAAM,CAAC,QAAQ,CAAC,YAAY,KAAK;IACjC,oBAAoB;IACpB,MAAM,CAAC,QAAQ,CAAC,WAAW,KAAK;IAChC,0BAA0B;IAC1B,MAAM,CAAC,QAAQ,CAAC,iBAAiB,KAAK;IACtC,uBAAuB;IACvB,MAAM,CAAC,QAAQ,CAAC,cAAc,KAAK;IACnC,qBAAqB;IACrB,MAAM,CAAC,QAAQ,CAAC,oBAAoB,OAAO;IAE3C,MAAM,EAAG,UAAU,CAAC;IACpB,QAAQ,EAAG,MAAM,CAAC;IAClB,QAAQ,EAAG,MAAM,CAAC;IAElB;;;;;;;;;;OAUG;gBACS,MAAM,CAAC,EAAE,MAAM,GAAG,cAAc,EAAE,OAAO,CAAC,EAAE,MAAM;IAoC9D;;;;OAIG;IACH,GAAG,CAAC,SAAS,EAAE,MAAM,GAAG,MAAM,GAAG,UAAU,GAAG,MAAM,EAAE,GAAG,IAAI;IA+B7D;;;;;OAKG;IACH,KAAK,CAAC,QAAQ,EAAE,MAAM,GAAG,cAAc,EAAE,MAAM,EAAE,MAAM,GAAG,IAAI;IAwB9D;;;;;OAKG;IACH,IAAI,CAAC,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,GAAG,cAAc;IAOtD;;;;;OAKG;IACH,KAAK,CAAC,KAAK,CAAC,EAAE,OAAO,GAAG,MAAM,GAAG,cAAc;IAgB/C,wCAAwC;IACxC,MAAM,IAAI,MAAM;IAIhB,MAAM,IAAI,MAAM;IAIhB,QAAQ,CAAC,QAAQ,CAAC,EAAE,KAAK,GAAG,QAAQ,GAAG,MAAM,GAAG,OAAO,GAAG,MAAM;IAOhE,gBAAgB;IAChB,cAAc,CAAC,OAAO,CAAC,EAAE,YAAY,GAAG,oBAAoB,GAAG,cAAc;IAmB7E,MAAM,IAAI,IAAI;IAUd,yDAAyD;IACzD,MAAM,CAAC,mBAAmB,CAAC,GAAG,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,MAAM,GAAG,MAAM;IAIjE,sDAAsD;IACtD,MAAM,CAAC,gBAAgB,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,MAAM,GAAG,MAAM;IAIjE,gBAAgB;IAChB,MAAM,CAAC,gBAAgB,CACrB,GAAG,EAAE,oBAAoB,GAAG,cAAc,GAAG,YAAY,EACzD,OAAO,CAAC,EAAE,YAAY,GACrB,MAAM;IA6BT,OAAO,IAAI,MAAM;CAIlB;AAED,cAAc;AACd,MAAM,MAAM,YAAY,GAAG;IACzB,KAAK,EAAE,MAAM,CAAC;CACf,CAAC;AAMF;;;GAGG;AACH,qBAAa,IAAK,SAAQ,MAAM;IAC9B,yGAAyG;IACzG,MAAM,CAAC,cAAc,UAAS;IAC9B;;;;;;OAMG;gBACS,KAAK,CAAC,EAAE,MAAM,GAAG,UAAU,GAAG,IAAI;IAkB9C;;;OAGG;IACH,IAAI,EAAE,IAAI,UAAU,CAEnB;IAED,IAAI,EAAE,CAAC,KAAK,EAAE,UAAU,EAEvB;IAED;;;OAGG;IACH,WAAW,CAAC,aAAa,UAAO,GAAG,MAAM;IAazC;;OAEG;IACH,QAAQ,CAAC,QAAQ,CAAC,EAAE,KAAK,GAAG,QAAQ,GAAG,MAAM;IAM7C;;;OAGG;IACH,MAAM,IAAI,MAAM;IAIhB;;;;OAIG;IACH,MAAM,CAAC,OAAO,EAAE,MAAM,GAAG,UAAU,GAAG,IAAI,GAAG,OAAO;IAgBpD;;OAEG;IACH,QAAQ,IAAI,MAAM;IAIlB;;OAEG;IACH,MAAM,CAAC,QAAQ,IAAI,UAAU;IAW7B;;;OAGG;IACH,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE,MAAM,GAAG,UAAU,GAAG,IAAI,GAAG,MAAM,GAAG,OAAO;IAoBnE;;;OAGG;WACa,mBAAmB,CAAC,SAAS,EAAE,MAAM,GAAG,IAAI;IAK5D,sEAAsE;WACtD,gBAAgB,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI;IAItD,gBAAgB;IAChB,MAAM,CAAC,eAAe,CAAC,cAAc,EAAE,MAAM;IAS7C;;;;;OAKG;IACH,MAAM,CAAC,iBAAiB,CAAC,cAAc,EAAE,MAAM;IAc/C,OAAO,IAAI,MAAM;CAGlB"}