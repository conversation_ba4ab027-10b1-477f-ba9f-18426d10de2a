{"version": 3, "file": "napi-rs.mjs", "sources": ["../src/napi-rs.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/unified-signatures */\n\nimport { deprecate } from 'node:util';\n\nimport {\n\tcreateCanvas,\n\tGlobalFonts,\n\tImage as NativeImage,\n\tloadImage,\n\tPath2D,\n\ttype AvifConfig,\n\ttype Canvas as NativeCanvas,\n\ttype SKRSContext2D as NativeCanvasRenderingContext2D\n} from '@napi-rs/canvas';\n\nexport interface BeveledRadiusOptions {\n\t/**\n\t * Top left corner.\n\t */\n\ttl: number;\n\n\t/**\n\t * Top right corner.\n\t */\n\ttr: number;\n\n\t/**\n\t * Bottom right corner.\n\t */\n\tbr: number;\n\n\t/**\n\t * Bottom left corner.\n\t */\n\tbl: number;\n}\n\nexport interface GradientStop {\n\tposition: number;\n\tcolor: string;\n}\n\nexport interface PrintCircularOptions {\n\t/**\n\t * The fit options, this is similar to CSS's object-fit.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/CSS/object-fit\n\t */\n\tfit?: 'fill' | 'contain' | 'cover' | 'none';\n}\n\nexport type ImageResolvable = Canvas | NativeImage | NativeCanvas;\nfunction _resolveImage(resolvable: ImageResolvable) {\n\treturn resolvable instanceof Canvas ? resolvable.canvas : resolvable;\n}\n\nexport type PatternResolvable = Canvas | NativeImage | ImageData;\nfunction _resolvePattern(resolvable: PatternResolvable) {\n\tif (resolvable instanceof Canvas) return resolvable.getImageData();\n\treturn resolvable;\n}\n\nexport type PatternRepeat = 'repeat' | 'repeat-x' | 'repeat-y' | 'no-repeat' | null;\nexport type Transform = ReturnType<NativeCanvasRenderingContext2D['getTransform']>;\n\nexport type ContextAttributes = ReturnType<NativeCanvasRenderingContext2D['getContextAttributes']>;\n\nexport class Canvas {\n\t/**\n\t * The constructed Canvas.\n\t */\n\tpublic canvas: NativeCanvas;\n\n\t/**\n\t * The 2D context for the Canvas.\n\t */\n\tpublic context: NativeCanvasRenderingContext2D;\n\n\t/**\n\t * Initialize canvas-constructor with `@napi-rs/canvas`.\n\t * @param width The width of the canvas.\n\t * @param height The height of the canvas.\n\t * @param contextAttributes The attributes for the underlying 2D context.\n\t */\n\tpublic constructor(width: number, height: number, contextAttributes?: ContextAttributes) {\n\t\tthis.canvas = createCanvas(width, height);\n\t\tthis.context = this.canvas.getContext('2d', contextAttributes);\n\t}\n\n\t/**\n\t * The image width of this canvas\n\t */\n\tpublic get width(): number {\n\t\treturn this.canvas.width;\n\t}\n\n\tpublic set width(value: number) {\n\t\tthis.canvas.width = value;\n\t}\n\n\t/**\n\t * The image height of this canvas\n\t */\n\tpublic get height(): number {\n\t\treturn this.canvas.height;\n\t}\n\n\tpublic set height(value: number) {\n\t\tthis.canvas.height = value;\n\t}\n\n\t/**\n\t * The current text direction used to draw text.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/direction\n\t */\n\tpublic get direction(): CanvasDirection {\n\t\treturn this.context.direction;\n\t}\n\n\t/**\n\t * The current text style to use when drawing text. This string uses the same syntax as the\n\t * [CSS font](https://developer.mozilla.org/en-US/docs/Web/CSS/font) specifier.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/font\n\t */\n\tpublic get font(): string {\n\t\treturn this.context.font;\n\t}\n\n\t/**\n\t * The alpha (transparency) value that is applied to shapes and images before they are drawn onto the canvas.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/globalAlpha\n\t */\n\tpublic get globalAlpha(): number {\n\t\treturn this.context.globalAlpha;\n\t}\n\n\t/**\n\t * Whether scaled images are smoothed (true, default) or not (false).\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/imageSmoothingEnabled\n\t */\n\tpublic get imageSmoothingEnabled(): boolean {\n\t\treturn this.context.imageSmoothingEnabled;\n\t}\n\n\t/**\n\t * The quality of image smoothing.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/imageSmoothingQuality\n\t */\n\tpublic get imageSmoothingQuality(): ImageSmoothingQuality {\n\t\treturn this.context.imageSmoothingQuality;\n\t}\n\n\t/**\n\t * Returns an object that contains the actual context parameters. Context attributes can be requested with this\n\t * class instantiation.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/getContextAttributes\n\t */\n\tpublic get contextAttributes(): ContextAttributes {\n\t\treturn this.context.getContextAttributes();\n\t}\n\n\t/**\n\t * Returns the current transformation matrix being applied to the context.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/getTransform\n\t */\n\tpublic get transform(): Transform {\n\t\treturn this.context.getTransform();\n\t}\n\n\t/**\n\t * The font height\n\t */\n\tpublic get textFontHeight(): number {\n\t\treturn getFontHeight(this.context.font);\n\t}\n\n\t/**\n\t * A list of numbers that specifies distances to alternately draw a line and a gap (in coordinate space units).\n\t * If the number, when setting the elements, was odd, the elements of the array get copied and concatenated. For\n\t * example, setting the line dash to [5, 15, 25] will result in getting back [5, 15, 25, 5, 15, 25].\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/getLineDash\n\t * @example\n\t * new Canvas(400, 300)\n\t *     .beginPath()\n\t *     .setLineDash([5, 15])\n\t *     .moveTo(0, 50)\n\t *     .lineTo(400, 50)\n\t *     .stroke()\n\t *     .png();\n\t */\n\tpublic get lineDash(): number[] {\n\t\treturn this.context.getLineDash();\n\t}\n\n\t/**\n\t * Change the current canvas' size.\n\t * @param width The new width for the canvas.\n\t * @param height The new height for the canvas.\n\t */\n\tpublic changeCanvasSize(width: number, height: number): this {\n\t\treturn this.changeCanvasWidth(width).changeCanvasHeight(height);\n\t}\n\n\t/**\n\t * Change the current canvas' width.\n\t * @param width The new width for the canvas.\n\t */\n\tpublic changeCanvasWidth(width: number): this {\n\t\tthis.width = width;\n\t\treturn this;\n\t}\n\n\t/**\n\t * Change the current canvas' height.\n\t * @param height The new height for the canvas.\n\t */\n\tpublic changeCanvasHeight(height: number): this {\n\t\tthis.height = height;\n\t\treturn this;\n\t}\n\n\t/**\n\t * Save the entire state of the canvas by pushing the current state onto a stack.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/save\n\t */\n\tpublic save(): this {\n\t\tthis.context.save();\n\t\treturn this;\n\t}\n\n\t/**\n\t * Restores the most recently saved canvas by popping the top entry in the drawing state stack. If there is no saved\n\t * state, this method does nothing.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/restore\n\t */\n\tpublic restore(): this {\n\t\tthis.context.restore();\n\t\treturn this;\n\t}\n\n\t/**\n\t * Adds a rotation to the transformation matrix. The angle argument represents a clockwise rotation angle and is\n\t * expressed in radians.\n\t * @param angle The angle to rotate clockwise in radians.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/rotate\n\t */\n\tpublic rotate(angle: number): this {\n\t\tthis.context.rotate(angle);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Adds a scaling transformation to the canvas units by X horizontally and by y vertically.\n\t * @param x Scaling factor in the horizontal direction.\n\t * @param y Scaling factor in the vertical direction.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/scale\n\t */\n\tpublic scale(x: number, y: number): this {\n\t\tthis.context.scale(x, y);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Adds a translation transformation by moving the canvas and its origin X horizontally and y vertically on the grid.\n\t * @param x Distance to move in the horizontal direction.\n\t * @param y Distance to move in the vertical direction.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/translate\n\t */\n\tpublic translate(x: number, y: number): this {\n\t\tthis.context.translate(x, y);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Turns the path currently being built into the current clipping path.\n\t * @param fillRule The algorithm by which to determine if a point is inside a path or outside a path.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/clip\n\t */\n\tpublic clip(fillRule?: CanvasFillRule): this;\n\t/**\n\t * Turns the path currently being built into the current clipping path.\n\t * @param path The path to use.\n\t * @param fillRule The algorithm by which to determine if a point is inside a path or outside a path.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/clip\n\t */\n\tpublic clip(path: Path2D, fillRule?: CanvasFillRule): this;\n\tpublic clip(...args: []): this {\n\t\tthis.context.clip(...args);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Resets (overrides) the current transformation to the identity matrix and then invokes a transformation described\n\t * by the arguments of this method.\n\t * @param a Horizontal scaling.\n\t * @param b Horizontal skewing.\n\t * @param c Vertical skewing.\n\t * @param d Vertical scaling.\n\t * @param e Horizontal moving.\n\t * @param f Vertical moving.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/setTransform\n\t */\n\tpublic setTransform(a: number, b: number, c: number, d: number, e: number, f: number): this;\n\t/**\n\t * Resets (overrides) the current transformation to the identity matrix and then invokes a transformation described\n\t * by the arguments of this method.\n\t * @param matrix The new transform matrix.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/setTransform\n\t */\n\tpublic setTransform(transform?: DOMMatrix): this;\n\tpublic setTransform(...args: readonly any[]): this {\n\t\tthis.context.setTransform(...args);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Resets the transformation.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/resetTransform\n\t */\n\tpublic resetTransform(): this {\n\t\tthis.context.resetTransform();\n\t\treturn this;\n\t}\n\n\t/**\n\t * Resets the filters.\n\t */\n\tpublic resetFilters(): this {\n\t\treturn this.setFilter('none');\n\t}\n\n\t/**\n\t * Returns an ImageData object representing the underlying pixel data for the area of the canvas\n\t * denoted by the entire Canvas. This method is not affected by the canvas transformation matrix.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/getImageData\n\t */\n\tpublic getImageData(): ImageData;\n\t/**\n\t * Returns an ImageData object representing the underlying pixel data for the area of the canvas denoted by the rectangle which starts at (sx, sy)\n\t * and has an sw width and sh height. This method is not affected by the canvas transformation matrix.\n\t * @param x The X coordinate of the upper left corner of the rectangle from which the ImageData will be extracted.\n\t * @param y The Y coordinate of the upper left corner of the rectangle from which the ImageData will be extracted.\n\t * @param width The width of the rectangle from which the ImageData will be extracted.\n\t * @param height The height of the rectangle from which the ImageData will be extracted.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/getImageData\n\t */\n\tpublic getImageData(x: number, y: number, width: number, height: number): ImageData;\n\tpublic getImageData(x?: number, y?: number, width?: number, height?: number): ImageData {\n\t\treturn this.context.getImageData(x ?? 0, y ?? 0, width ?? this.width, height ?? this.height);\n\t}\n\n\t/**\n\t * The CanvasRenderingContext2D.putImageData() method of the Canvas 2D API paints data from the given ImageData object onto the bitmap.\n\t * This method is not affected by the canvas transformation matrix.\n\t * @param imagedata An ImageData object containing the array of pixel values.\n\t * @param dx Horizontal position (x-coordinate) at which to place the image data in the destination canvas.\n\t * @param dy Vertical position (y-coordinate) at which to place the image data in the destination canvas.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/putImageData\n\t */\n\tpublic putImageData(imagedata: ImageData, dx: number, dy: number): this;\n\t/**\n\t * The CanvasRenderingContext2D.putImageData() method of the Canvas 2D API paints data from the given ImageData object onto the bitmap.\n\t * Only the pixels from that rectangle are painted.\n\t * This method is not affected by the canvas transformation matrix.\n\t * @param imagedata An ImageData object containing the array of pixel values.\n\t * @param x Horizontal position (x-coordinate) at which to place the image data in the destination canvas.\n\t * @param y Vertical position (y-coordinate) at which to place the image data in the destination canvas.\n\t * @param dirtyX Horizontal position (x-coordinate). The X coordinate of the top left hand corner of your Image data. Defaults to 0.\n\t * @param dirtyY Vertical position (y-coordinate). The Y coordinate of the top left hand corner of your Image data. Defaults to 0.\n\t * @param dirtyWidth Width of the rectangle to be painted. Defaults to the width of the image data.\n\t * @param dirtyHeight Height of the rectangle to be painted. Defaults to the height of the image data.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/putImageData\n\t */\n\tpublic putImageData(imagedata: ImageData, x: number, y: number, dirtyX: number, dirtyY: number, dirtyWidth: number, dirtyHeight: number): this;\n\n\tpublic putImageData(...args: [any, any, any]): this {\n\t\tthis.context.putImageData(...args);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Fills the current or given path with the current fill style using the non-zero or even-odd winding rule.\n\t * @param fillRule The algorithm by which to determine if a point is inside a path or outside a path.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/fill\n\t */\n\tpublic fill(fillRule?: CanvasFillRule): this;\n\t/**\n\t * Fills the current or given path with the current fill style using the non-zero or even-odd winding rule.\n\t * @param path The path to fill.\n\t * @param fillRule The algorithm by which to determine if a point is inside a path or outside a path.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/fill\n\t */\n\tpublic fill(path: Path2D, fillRule?: CanvasFillRule): this;\n\tpublic fill(...args: [any]): this {\n\t\tthis.context.fill(...args);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Add a text.\n\t * @param text The text to write.\n\t * @param x The position x to start drawing the element.\n\t * @param y The position y to start drawing the element.\n\t * @param maxWidth The maximum width to draw. If specified, and the string is computed to be wider than this width,\n\t * the font is adjusted to use a more horizontally condensed font.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/fillText\n\t */\n\tpublic printText(text: string, x: number, y: number, maxWidth?: number): this;\n\tpublic printText(text: string, x: number, y: number, ...rest: readonly any[]): this {\n\t\tthis.context.fillText(text, x, y, ...rest);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Add responsive text\n\t * @param text The text to write.\n\t * @param x The position x to start drawing the element.\n\t * @param y The position y to start drawing the element.\n\t * @param maxWidth The max length in pixels for the text.\n\t * @example\n\t * new Canvas(400, 300)\n\t *     .setTextFont('40px Tahoma')\n\t *     .printResponsiveText('Hello World', 30, 30, 50)\n\t *     .png();\n\t */\n\tpublic printResponsiveText(text: string, x: number, y: number, maxWidth: number): this {\n\t\tconst [tail, height, lead] = this.parseFont(this.context.font);\n\t\tif (typeof height !== 'number') return this.printText(text, x, y);\n\n\t\t// Measure the width of the text. If it fits `maxWidth`, draw the text directly:\n\t\tconst { width } = this.measureText(text);\n\t\tif (width <= maxWidth) return this.printText(text, x, y);\n\n\t\t// Otherwise save state, set the font with a size that fits, draw the text, and restore:\n\t\tconst newHeight = (maxWidth / width) * height;\n\t\treturn this.save().setTextFont(`${tail}${newHeight}${lead}`).printText(text, x, y).restore();\n\t}\n\n\t/**\n\t * Add text with line breaks (node-canvas and web canvas compatible)\n\t * @param text The text to write.\n\t * @param x The position x to start drawing the element.\n\t * @param y The position y to start drawing the element.\n\t * @example\n\t * new Canvas(400, 300)\n\t *     .setTextFont('25px Tahoma')\n\t *     .printMultilineText('This is a really\\nlong text!', 139, 360)\n\t *     .png();\n\t */\n\tpublic printMultilineText(text: string, x: number, y: number): this {\n\t\tconst lines = text.split(/\\r?\\n/);\n\n\t\t// If there are no new lines, return using printText\n\t\tif (lines.length <= 1) return this.printText(text, x, y);\n\n\t\tconst height = this.textFontHeight;\n\n\t\tlet linePositionY = y;\n\t\tfor (const line of lines) {\n\t\t\tthis.printText(line, x, Math.floor(linePositionY));\n\t\t\tlinePositionY += height;\n\t\t}\n\n\t\treturn this;\n\t}\n\n\t/**\n\t * Wrap the text in multiple lines and write it\n\t * @param text The text to wrap and write.\n\t * @param x The position x to start drawing the element.\n\t * @param y The position y to start drawing the element.\n\t * @param wrapWidth The width in pixels of the line wrap\n\t * @example\n\t * new Canvas(400, 300)\n\t *     .setTextFont('25px Tahoma')\n\t *     .printWrappedText('This is a really long text!', 139, 360)\n\t *     .png();\n\t */\n\tpublic printWrappedText(text: string, x: number, y: number, wrapWidth: number): this {\n\t\tconst wrappedText = textWrap(this, text, wrapWidth);\n\t\treturn this.printMultilineText(wrappedText, x, y);\n\t}\n\n\t/**\n\t * Strokes the current or given path with the current stroke style using the non-zero winding rule.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/stroke\n\t */\n\tpublic stroke(): this {\n\t\tthis.context.stroke();\n\t\treturn this;\n\t}\n\n\t/**\n\t * Paints a rectangle which has a starting point at (X, Y) and has a w width and an h height onto the canvas, using\n\t * the current stroke style.\n\t * @param x The x axis of the coordinate for the rectangle starting point.\n\t * @param y The y axis of the coordinate for the rectangle starting point.\n\t * @param width The rectangle's width.\n\t * @param height The rectangle's height.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/strokeRect\n\t */\n\tpublic printStrokeRectangle(x: number, y: number, width: number, height: number): this {\n\t\tthis.context.strokeRect(x, y, width, height);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Add stroked text.\n\t * @param text The text to write.\n\t * @param x The position x to start drawing the element.\n\t * @param y The position y to start drawing the element.\n\t * @param maxWidth The maximum width to draw. If specified, and the string is computed to be wider than this width,\n\t * the font is adjusted to use a more horizontally condensed font.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/strokeText\n\t */\n\tpublic printStrokeText(text: string, x: number, y: number, maxWidth?: number): this {\n\t\tthis.context.strokeText(text, x, y, maxWidth);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Measure a text's width given a string.\n\t * @param text The text to measure.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/measureText\n\t * @example\n\t * const size = new Canvas(500, 400)\n\t *     .setTextFont('40px Tahoma')\n\t *     .measureText('Hello World!'); // Returns a number\n\t *\n\t * const newSize = size.width < 500 ? 40 : (500 / size.width) * 40;\n\t *\n\t * new Canvas(500, 400)\n\t *     .setTextFont(`${newSize}px Tahoma`)\n\t *     .printText('Hello World!', 30, 50)\n\t *     .png(); // Returns a Buffer\n\t * @example\n\t * new Canvas(500, 400)\n\t *     .setTextFont('40px Tahoma')\n\t *     .process((canvas) => {\n\t *         const size = canvas.measureText('Hello World!');\n\t *         const newSize = size.width < 500 ? 40 : (500 / size.width) * 40;\n\t *         this.setTextFont(`${newSize}px Tahoma`);\n\t *     })\n\t *     .printText('Hello World!', 30, 50)\n\t *     .png(); // Returns a Buffer\n\t */\n\tpublic measureText(text: string): TextMetrics {\n\t\treturn this.context.measureText(text);\n\t}\n\n\t/**\n\t * Set the new font size, unlike setTextFont, this only requires the number.\n\t * @param size The new size to set\n\t */\n\tpublic setTextSize(size: number): this {\n\t\tconst result = this.parseFont(this.context.font);\n\t\treturn result.length === 1 ? this : this.setTextFont(`${result[0]}${size}${result[2]}`);\n\t}\n\n\t/**\n\t * Specifies the color or style to use for the lines around shapes.\n\t * @param color A canvas' color resolvable.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/strokeStyle\n\t */\n\tpublic setStroke(color: string | CanvasGradient | CanvasPattern): this {\n\t\tthis.context.strokeStyle = color;\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the thickness of lines in space units.\n\t * @param width A number specifying the line width in space units.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/lineWidth\n\t */\n\tpublic setLineWidth(width: number): this {\n\t\tthis.context.lineWidth = width;\n\t\treturn this;\n\t}\n\n\tpublic setStrokeWidth(width: number): this {\n\t\treturn this.setLineWidth(width);\n\t}\n\n\t/**\n\t * Sets the line dash pattern offset or \"phase\" to achieve a \"marching ants\" effect\n\t * @param value A float specifying the amount of the offset. Initially 0.0.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/lineDashOffset\n\t */\n\tpublic setLineDashOffset(value: number): this {\n\t\tthis.context.lineDashOffset = value;\n\t\treturn this;\n\t}\n\n\t/**\n\t * Determines how two connecting segments (of lines, arcs or curves) with non-zero lengths in a shape are joined\n\t * together (degenerate segments with zero lengths, whose specified endpoints and control points are exactly at the\n\t * same position, are skipped).\n\t * @param value The line join type.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/lineJoin\n\t */\n\tpublic setLineJoin(value: CanvasLineJoin): this {\n\t\tthis.context.lineJoin = value;\n\t\treturn this;\n\t}\n\n\t/**\n\t * Determines how the end points of every line are drawn. There are three possible values for this property and\n\t * those are: butt, round and square. By default this property is set to butt.\n\t * @param value The line join type.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/lineCap\n\t */\n\tpublic setLineCap(value: CanvasLineCap): this {\n\t\tthis.context.lineCap = value;\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the line dash pattern used when stroking lines, using an array of values which specify alternating lengths\n\t * of lines and gaps which describe the pattern.\n\t * @param segments An Array of numbers which specify distances to alternately draw a line and a gap (in coordinate\n\t * space units). If the number of elements in the array is odd, the elements of the array get copied and\n\t * concatenated. For example, [5, 15, 25] will become [5, 15, 25, 5, 15, 25]. If the array is empty, the line dash\n\t * list is cleared and line strokes return to being solid.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/setLineDash\n\t */\n\tpublic setLineDash(segments: number[]): this {\n\t\tthis.context.setLineDash(segments);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Add an image at a position (x, y) with the source image's width and height.\n\t * @param image The image.\n\t * @param dx The x-axis coordinate in the destination canvas at which to place the top-left corner of the source `image`.\n\t * @param dy The y-axis coordinate in the destination canvas at which to place the top-left corner of the source `image`.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/drawImage\n\t */\n\tpublic printImage(image: ImageResolvable, dx: number, dy: number): this;\n\t/**\n\t * Add an image at a position (x, y) with a given width and height.\n\t * @param image The image.\n\t * @param dx The x-axis coordinate in the destination canvas at which to place the top-left corner of the source `image`.\n\t * @param dy The y-axis coordinate in the destination canvas at which to place the top-left corner of the source `image`.\n\t * @param dw The width to draw the `image` in the destination canvas. This allows scaling of the drawn image.\n\t * @param dh The height to draw the `image` in the destination canvas. This allows scaling of the drawn image.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/drawImage\n\t */\n\tpublic printImage(image: ImageResolvable, dx: number, dy: number, dw: number, dh: number): this;\n\t/**\n\t * Add an image at a position (x, y) with a given width and height, from a specific source rectangle.\n\t * @param image The image.\n\t * @param sx The x-axis coordinate of the top left corner of the sub-rectangle of the source `image` to draw into the destination context.\n\t * @param sy The y-axis coordinate of the top left corner of the sub-rectangle of the source `image` to draw into the destination context.\n\t * @param sw The width of the sub-rectangle of the source `image` to draw into the destination context.\n\t * @param sh The height of the sub-rectangle of the source `image` to draw into the destination context.\n\t * @param dx The x-axis coordinate in the destination canvas at which to place the top-left corner of the source `image`.\n\t * @param dy The y-axis coordinate in the destination canvas at which to place the top-left corner of the source `image`.\n\t * @param dw The width to draw the `image` in the destination canvas. This allows scaling of the drawn image.\n\t * @param dh The height to draw the `image` in the destination canvas. This allows scaling of the drawn image.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/drawImage\n\t */\n\tpublic printImage(image: ImageResolvable, sx: number, sy: number, sw: number, sh: number, dx: number, dy: number, dw: number, dh: number): this;\n\tpublic printImage(image: ImageResolvable, ...args: [number, number]) {\n\t\tthis.context.drawImage(_resolveImage(image), ...args);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Add a round image.\n\t * @param imageOrBuffer The image.\n\t * @param x The X coordinate in the destination canvas at which to place the top-left corner of the source image.\n\t * @param y The Y coordinate in the destination canvas at which to place the top-left corner of the source image.\n\t * @param width The width to draw the image in the destination canvas. This allows scaling of the drawn image. If not specified, the image is not scaled in width when drawn.\n\t * @param height The height to draw the image in the destination canvas. This allows scaling of the drawn image. If not specified, the image is not scaled in height when drawn.\n\t * @param radius The radius for the circle\n\t */\n\tpublic printCircularImage(imageOrBuffer: ImageResolvable, x: number, y: number, radius: number, options?: PrintCircularOptions): this;\n\tpublic printCircularImage(\n\t\timageOrBuffer: ImageResolvable,\n\t\tx: number,\n\t\ty: number,\n\t\tradius: number,\n\t\t{ fit = 'fill' }: PrintCircularOptions = {}\n\t): this {\n\t\tconst { positionX, positionY, sizeX, sizeY } = this.resolveCircularCoordinates(imageOrBuffer, x, y, radius, fit);\n\t\treturn this.save()\n\t\t\t.createCircularClip(x, y, radius, 0, Math.PI * 2, false)\n\t\t\t.printImage(imageOrBuffer, positionX, positionY, sizeX, sizeY)\n\t\t\t.restore();\n\t}\n\n\t/**\n\t * Add a beveled image.\n\t * @param imageOrBuffer The image.\n\t * @param x The position x to start drawing the element.\n\t * @param y The position y to start drawing the element.\n\t * @param width The width of the element.\n\t * @param height The height of the element.\n\t * @param radius The radius for the new image.\n\t */\n\tpublic printRoundedImage(\n\t\timageOrBuffer: ImageResolvable,\n\t\tx: number,\n\t\ty: number,\n\t\twidth: number,\n\t\theight: number,\n\t\tradius: BeveledRadiusOptions | number\n\t): this;\n\n\tpublic printRoundedImage(\n\t\timageOrBuffer: ImageResolvable,\n\t\tx: number,\n\t\ty: number,\n\t\twidth: number,\n\t\theight: number,\n\t\tradius: BeveledRadiusOptions | number\n\t): this {\n\t\treturn this.save().createRoundedClip(x, y, width, height, radius).printImage(imageOrBuffer, x, y, width, height).restore();\n\t}\n\n\t/**\n\t * Add a circle or semi circle.\n\t * @param x The position x in the center of the circle.\n\t * @param y The position y in the center of the circle.\n\t * @param radius The radius for the clip.\n\t */\n\tpublic printCircle(x: number, y: number, radius: number): this {\n\t\treturn this.save().createCircularPath(x, y, radius).fill().restore();\n\t}\n\n\t/**\n\t * Add a rectangle.\n\t * @param x The position x to start drawing the element.\n\t * @param y The position y to start drawing the element.\n\t * @param width The width of the element.\n\t * @param height The height of the element.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/fillRect\n\t */\n\tpublic printRectangle(x: number, y: number, width: number, height: number): this {\n\t\tthis.context.fillRect(x, y, width, height);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Add a beveled rectangle.\n\t * @param x The position x to start drawing the element.\n\t * @param y The position y to start drawing the element.\n\t * @param width  The width of the element.\n\t * @param height The height of the element.\n\t * @param radius The radius for the bevels.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/fillRect\n\t * @example\n\t * // Radius argument\n\t * new Canvas(200, 200)\n\t *     .printRoundedRectangle(0, 0, 200, 50, 35)\n\t *     .png();\n\t *\n\t * @example\n\t * // Configured bevels\n\t * new Canvas(200, 200)\n\t *     .printRoundedRectangle(0, 0, 200, 50, {\n\t *         // Top left border\n\t *         tl: 15,\n\t *         // Top right border\n\t *         tr: 20,\n\t *         // Bottom left border\n\t *         bl: 5,\n\t *         // Bottom right border\n\t *         br: 10\n\t *     })\n\t *     .png();\n\t *\n\t * @example\n\t * // Top bevels only\n\t * new Canvas(200, 200)\n\t *     .printRoundedRectangle(0, 0, 200, 50, { tl: 20, tr: 20, bl: 0, br: 0 })\n\t *     .png();\n\t */\n\tpublic printRoundedRectangle(x: number, y: number, width: number, height: number, radius: number | BeveledRadiusOptions): this {\n\t\treturn this.save().createRoundedPath(x, y, width, height, radius).fill().restore();\n\t}\n\n\t/**\n\t * Create a round path.\n\t * @param dx The position x in the center of the clip's circle.\n\t * @param dy The position y in the center of the clip's circle.\n\t * @param radius The radius for the clip.\n\t * @param start The degree in radians to start drawing the circle.\n\t * @param angle The degree in radians to finish drawing the circle, defaults to a full circle.\n\t * @param antiClockwise Whether the path should be anti-clockwise.\n\t */\n\tpublic createCircularPath(dx: number, dy: number, radius: number, start = 0, angle = Math.PI * 2, antiClockwise = false): this {\n\t\tthis.context.beginPath();\n\t\tthis.context.arc(dx, dy, radius, start, angle, antiClockwise);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Create a round clip.\n\t * @param dx The position x in the center of the clip's circle.\n\t * @param dy The position y in the center of the clip's circle.\n\t * @param radius The radius for the clip.\n\t * @param start The degree in radians to start drawing the circle.\n\t * @param angle The degree in radians to finish drawing the circle, defaults to a full circle.\n\t * @param antiClockwise Whether the path should be anti-clockwise.\n\t * @see createRoundPath\n\t */\n\tpublic createCircularClip(dx: number, dy: number, radius: number, start?: number, angle?: number, antiClockwise?: boolean): this {\n\t\treturn this.createCircularPath(dx, dy, radius, start, angle, antiClockwise).clip();\n\t}\n\n\t/**\n\t * Create a rectangle path.\n\t * @param x The position x in the left corner.\n\t * @param y The position y in the upper corner.\n\t * @param width The width of the rectangle.\n\t * @param height The height of the rectangle.\n\t */\n\tpublic createRectanglePath(x: number, y: number, width: number, height: number): this {\n\t\tthis.context.rect(x, y, width, height);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Create a rectangle clip.\n\t * @param x The position x in the left corner.\n\t * @param y The position y in the upper corner.\n\t * @param width The width of the rectangle.\n\t * @param height The height of the rectangle.\n\t */\n\tpublic createRectangleClip(x: number, y: number, width: number, height: number): this {\n\t\treturn this.createRectanglePath(x, y, width, height).clip();\n\t}\n\n\t/**\n\t * Create a beveled path.\n\t * @param x The position x to start drawing clip.\n\t * @param y The position y to start drawing clip.\n\t * @param width The width of clip.\n\t * @param height The height of clip.\n\t * @param radius The radius for clip's rounded borders.\n\t */\n\tpublic createRoundedPath(x: number, y: number, width: number, height: number, radius: number | BeveledRadiusOptions): this {\n\t\tif (width > 0 && height > 0) {\n\t\t\tlet radiusObject: BeveledRadiusOptions | undefined = undefined;\n\t\t\tif (typeof radius === 'number') {\n\t\t\t\tradius = Math.min(radius, width / 2, height / 2);\n\t\t\t\tradiusObject = { tl: radius, tr: radius, br: radius, bl: radius };\n\t\t\t} else {\n\t\t\t\tradiusObject = radius;\n\t\t\t\tradius = Math.min(5, width / 2, height / 2);\n\t\t\t}\n\t\t\tconst { tl = radius, tr = radius, br = radius, bl = radius } = radiusObject;\n\t\t\tthis.context.beginPath();\n\t\t\tthis.context.moveTo(x + tl, y);\n\t\t\tthis.context.lineTo(x + width - tr, y);\n\t\t\tthis.context.quadraticCurveTo(x + width, y, x + width, y + tr);\n\t\t\tthis.context.lineTo(x + width, y + height - br);\n\t\t\tthis.context.quadraticCurveTo(x + width, y + height, x + width - br, y + height);\n\t\t\tthis.context.lineTo(x + bl, y + height);\n\t\t\tthis.context.quadraticCurveTo(x, y + height, x, y + height - bl);\n\t\t\tthis.context.lineTo(x, y + tl);\n\t\t\tthis.context.quadraticCurveTo(x, y, x + tl, y);\n\t\t\tthis.context.closePath();\n\t\t}\n\t\treturn this;\n\t}\n\n\t/**\n\t * Create a beveled clip.\n\t * @param x The position x to start drawing clip.\n\t * @param y The position y to start drawing clip.\n\t * @param width The width of clip.\n\t * @param height The height of clip.\n\t * @param radius The radius for clip's rounded borders.\n\t * @example\n\t * // Radius argument, fill the content\n\t * new Canvas(200, 200)\n\t *     .createRoundedClip(0, 0, 200, 50, 35)\n\t *     .fill()\n\t *     .png();\n\t *\n\t * @example\n\t * // Configured bevels\n\t * new Canvas(200, 200)\n\t *     .createRoundedClip(0, 0, 200, 50, {\n\t *         // Top left border\n\t *         tl: 15,\n\t *         // Top right border\n\t *         tr: 20,\n\t *         // Bottom left border\n\t *         bl: 5,\n\t *         // Bottom right border\n\t *         br: 10\n\t *     })\n\t *     // Add an image with the shape of the beveled clip using different borders\n\t *     .printImage(buffer, 0, 0, 200, 50)\n\t *     .png();\n\t *\n\t * @example\n\t * // Top bevels only\n\t * new Canvas(200, 200)\n\t *     .createRoundedClip(0, 0, 200, 50, { tl: 20, tr: 20, bl: 0, br: 0 })\n\t *     .printImage(buffer, 0, 0, 200, 50)\n\t *     .png();\n\t */\n\tpublic createRoundedClip(x: number, y: number, width: number, height: number, radius: number | BeveledRadiusOptions): this {\n\t\treturn this.createRoundedPath(x, y, width, height, radius).clip();\n\t}\n\n\t/**\n\t * Set a color for the canvas' context.\n\t * @param color A canvas' color resolvable.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/fillStyle\n\t */\n\tpublic setColor(color: string | CanvasGradient | CanvasPattern): this {\n\t\tthis.context.fillStyle = color;\n\t\treturn this;\n\t}\n\n\t/**\n\t * Change the font.\n\t * @param font The font's name to set.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/font\n\t */\n\tpublic setTextFont(font: string): this {\n\t\tthis.context.font = font;\n\t\treturn this;\n\t}\n\n\t/**\n\t * Change the font alignment.\n\t * @param align The font's alignment to set.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/textAlign\n\t */\n\tpublic setTextAlign(align: CanvasTextAlign): this {\n\t\tthis.context.textAlign = align;\n\t\treturn this;\n\t}\n\n\t/**\n\t * Change the font's baseline.\n\t * @param baseline The font's baseline to set.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/textBaseline\n\t */\n\tpublic setTextBaseline(baseline: CanvasTextBaseline): this {\n\t\tthis.context.textBaseline = baseline;\n\t\treturn this;\n\t}\n\n\t/**\n\t * Change the canvas's filters.\n\t * @param filter The filter to set.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/filter\n\t */\n\tpublic setFilter(filter: string): this {\n\t\tthis.context.filter = filter;\n\t\treturn this;\n\t}\n\n\t/**\n\t * Starts a new path by emptying the list of sub-paths.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/beginPath\n\t */\n\tpublic beginPath(): this {\n\t\tthis.context.beginPath();\n\t\treturn this;\n\t}\n\n\t/**\n\t * Causes the point of the pen to move back to the start of the current sub-path.\n\t * If the shape has already been closed or has only one point, this function does nothing.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/closePath\n\t */\n\tpublic closePath(): this {\n\t\tthis.context.closePath();\n\t\treturn this;\n\t}\n\n\t/**\n\t * Creates a pattern using the specified image. It repeats the source in the directions specified by the repetition\n\t * argument, and returns it.\n\t * @param image A Canvas Image to be used as the image to repeat.\n\t * @param repetition The repeat mode.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/createPattern\n\t */\n\tpublic createPattern(image: PatternResolvable, repetition: PatternRepeat): CanvasPattern {\n\t\treturn this.context.createPattern(_resolvePattern(image), repetition)!;\n\t}\n\n\t/**\n\t * Creates a pattern using the specified image. It repeats the source in the directions specified by the repetition\n\t * argument, and prints it.\n\t * @param image A Canvas Image to be used as the image to repeat.\n\t * @param repetition The repeat mode.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/createPattern\n\t */\n\tpublic printPattern(image: PatternResolvable, repetition: PatternRepeat): this {\n\t\treturn this.setColor(this.createPattern(image, repetition));\n\t}\n\n\t/**\n\t * Creates a gradient along the line given by the coordinates represented by the parameters.\n\t * The coordinates are global, the second point does not rely on the position of the first and vice versa.\n\t * @param x0 The x axis of the coordinate of the start point.\n\t * @param y0 The y axis of the coordinate of the start point.\n\t * @param x1 The x axis of the coordinate of the end point.\n\t * @param y1 The y axis of the coordinate of the end point.\n\t * @param steps The gradient steps.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/createLinearGradient\n\t */\n\tpublic createLinearGradient(x0: number, y0: number, x1: number, y1: number, steps: readonly GradientStop[] = []): CanvasGradient {\n\t\tconst gradient = this.context.createLinearGradient(x0, y0, x1, y1);\n\t\tfor (const step of steps) {\n\t\t\tgradient.addColorStop(step.position, step.color);\n\t\t}\n\n\t\treturn gradient;\n\t}\n\n\t/**\n\t * Creates a gradient along the line given by the coordinates represented by the parameters.\n\t * The coordinates are global, the second point does not rely on the position of the first and vice versa. This\n\t * method is chainable and calls setColor after creating the gradient.\n\t * @param x0 The x axis of the coordinate of the start point.\n\t * @param y0 The y axis of the coordinate of the start point.\n\t * @param x1 The x axis of the coordinate of the end point.\n\t * @param y1 The y axis of the coordinate of the end point.\n\t * @param steps The gradient steps.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/createLinearGradient\n\t * @example\n\t * new Canvas(200, 200)\n\t *     .printLinearColorGradient(0, 0, 200, 50, [\n\t *         { position: 0, color: 'white' },\n\t *         { position: 0.25, color: 'red' },\n\t *         { position: 0.5, color: 'blue' }\n\t *     ])\n\t *     .printRectangle(10, 10, 200, 100)\n\t */\n\tpublic printLinearColorGradient(x0: number, y0: number, x1: number, y1: number, steps?: readonly GradientStop[]): this {\n\t\tconst gradient = this.createLinearGradient(x0, y0, x1, y1, steps);\n\t\treturn this.setColor(gradient);\n\t}\n\n\t/**\n\t * Creates a gradient along the line given by the coordinates represented by the parameters.\n\t * The coordinates are global, the second point does not rely on the position of the first and vice versa. This\n\t * method is chainable and calls setStroke after creating the gradient.\n\t * @param x0 The x axis of the coordinate of the start point.\n\t * @param y0 The y axis of the coordinate of the start point.\n\t * @param x1 The x axis of the coordinate of the end point.\n\t * @param y1 The y axis of the coordinate of the end point.\n\t * @param steps The gradient steps.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/createLinearGradient\n\t * @example\n\t * new Canvas(200, 200)\n\t *     .printLinearStrokeGradient(0, 0, 200, 50, [\n\t *         { position: 0, color: 'white' },\n\t *         { position: 0.25, color: 'red' },\n\t *         { position: 0.5, color: 'blue' }\n\t *     ])\n\t *     .printRectangle(10, 10, 200, 100)\n\t */\n\tpublic printLinearStrokeGradient(x0: number, y0: number, x1: number, y1: number, steps?: readonly GradientStop[]): this {\n\t\tconst gradient = this.createLinearGradient(x0, y0, x1, y1, steps);\n\t\treturn this.setStroke(gradient);\n\t}\n\n\t/**\n\t * Creates a radial gradient given by the coordinates of the two circles represented by the parameters.\n\t * @param x0 The x axis of the coordinate of the start circle.\n\t * @param y0 The y axis of the coordinate of the start circle.\n\t * @param r0 The radius of the start circle.\n\t * @param x1 The x axis of the coordinate of the end circle.\n\t * @param y1 The y axis of the coordinate of the end circle.\n\t * @param r1 The radius of the end circle.\n\t * @param steps The gradient steps.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/createRadialGradient\n\t */\n\tpublic createRadialGradient(\n\t\tx0: number,\n\t\ty0: number,\n\t\tr0: number,\n\t\tx1: number,\n\t\ty1: number,\n\t\tr1: number,\n\t\tsteps: readonly GradientStop[] = []\n\t): CanvasGradient {\n\t\tconst gradient = this.context.createRadialGradient(x0, y0, r0, x1, y1, r1);\n\t\tfor (const step of steps) {\n\t\t\tgradient.addColorStop(step.position, step.color);\n\t\t}\n\n\t\treturn gradient;\n\t}\n\n\t/**\n\t * Creates a radial gradient given by the coordinates of the two circles represented by the parameters. This\n\t * method is chainable and calls setColor after creating the gradient.\n\t * @param x0 The x axis of the coordinate of the start circle.\n\t * @param y0 The y axis of the coordinate of the start circle.\n\t * @param r0 The radius of the start circle.\n\t * @param x1 The x axis of the coordinate of the end circle.\n\t * @param y1 The y axis of the coordinate of the end circle.\n\t * @param r1 The radius of the end circle.\n\t * @param steps The gradient steps.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/createRadialGradient\n\t */\n\tpublic printRadialColorGradient(x0: number, y0: number, r0: number, x1: number, y1: number, r1: number, steps?: readonly GradientStop[]): this {\n\t\tconst gradient = this.createRadialGradient(x0, y0, r0, x1, y1, r1, steps);\n\t\treturn this.setColor(gradient);\n\t}\n\n\t/**\n\t * Creates a radial gradient given by the coordinates of the two circles represented by the parameters. This\n\t * method is chainable and calls setStroke after creating the gradient.\n\t * @param x0 The x axis of the coordinate of the start circle.\n\t * @param y0 The y axis of the coordinate of the start circle.\n\t * @param r0 The radius of the start circle.\n\t * @param x1 The x axis of the coordinate of the end circle.\n\t * @param y1 The y axis of the coordinate of the end circle.\n\t * @param r1 The radius of the end circle.\n\t * @param steps The gradient steps.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/createRadialGradient\n\t */\n\tpublic printRadialStrokeGradient(x0: number, y0: number, r0: number, x1: number, y1: number, r1: number, steps?: readonly GradientStop[]): this {\n\t\tconst gradient = this.createRadialGradient(x0, y0, r0, x1, y1, r1, steps);\n\t\treturn this.setStroke(gradient);\n\t}\n\n\t/**\n\t * Creates a radial gradient around a point with given coordinates.\n\t * @param startAngle The angle at which to begin the gradient, in radians. Angle measurements start vertically\n\t * above the centre and move around clockwise.\n\t * @param x The x-axis coordinate of the centre of the gradient.\n\t * @param y The y-axis coordinate of the centre of the gradient.\n\t * @param steps The gradient steps.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/createConicGradient\n\t */\n\tpublic createConicGradient(startAngle: number, x: number, y: number, steps: readonly GradientStop[] = []): CanvasGradient {\n\t\tconst gradient = this.context.createConicGradient(startAngle, x, y);\n\t\tfor (const step of steps) {\n\t\t\tgradient.addColorStop(step.position, step.color);\n\t\t}\n\n\t\treturn gradient;\n\t}\n\n\t/**\n\t * Creates a radial gradient given by the coordinates of the two circles represented by the parameters. This\n\t * method is chainable and calls setColor after creating the gradient.\n\t * @param startAngle The angle at which to begin the gradient, in radians. Angle measurements start vertically\n\t * above the centre and move around clockwise.\n\t * @param x The x-axis coordinate of the centre of the gradient.\n\t * @param y The y-axis coordinate of the centre of the gradient.\n\t * @param steps The gradient steps.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/createConicGradient\n\t */\n\tpublic printConicColorGradient(startAngle: number, x: number, y: number, steps?: readonly GradientStop[]): this {\n\t\tconst gradient = this.createConicGradient(startAngle, x, y, steps);\n\t\treturn this.setColor(gradient);\n\t}\n\n\t/**\n\t * Creates a radial gradient around a point with given coordinates. This method is chainable and calls setStroke\n\t * after creating the gradient.\n\t * @param startAngle The angle at which to begin the gradient, in radians. Angle measurements start vertically\n\t * above the centre and move around clockwise.\n\t * @param x The x-axis coordinate of the centre of the gradient.\n\t * @param y The y-axis coordinate of the centre of the gradient.\n\t * @param steps The gradient steps.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/createConicGradient\n\t */\n\tpublic printConicStrokeGradient(startAngle: number, x: number, y: number, steps?: readonly GradientStop[]): this {\n\t\tconst gradient = this.createConicGradient(startAngle, x, y, steps);\n\t\treturn this.setStroke(gradient);\n\t}\n\n\t/**\n\t * Adds an ellipse to the path which is centered at (X, Y) position with the radius radiusX and radiusY starting at\n\t * startAngle and ending at endAngle going in the given direction by anticlockwise (defaulting to clockwise).\n\t * @param x The x axis of the coordinate for the ellipse's center.\n\t * @param y The y axis of the coordinate for the ellipse's center.\n\t * @param radiusX The ellipse's major-axis radius.\n\t * @param radiusY The ellipse's minor-axis radius.\n\t * @param rotation The rotation for this ellipse, expressed in radians.\n\t * @param startAngle The starting point, measured from the x axis, from which it will be drawn, expressed in radians.\n\t * @param endAngle The end ellipse's angle to which it will be drawn, expressed in radians.\n\t * @param anticlockwise An optional Boolean which, if true, draws the ellipse anticlockwise (counter-clockwise), otherwise in a clockwise direction.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/ellipse\n\t */\n\tpublic createEllipsePath(\n\t\tx: number,\n\t\ty: number,\n\t\tradiusX: number,\n\t\tradiusY: number,\n\t\trotation: number,\n\t\tstartAngle: number,\n\t\tendAngle: number,\n\t\tanticlockwise?: boolean\n\t): this {\n\t\tthis.context.ellipse(x, y, radiusX, radiusY, rotation, startAngle, endAngle, anticlockwise);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Creates an ellipse clip which is centered at (X, Y) position with the radius radiusX and radiusY starting at\n\t * startAngle and ending at endAngle going in the given direction by anticlockwise (defaulting to clockwise).\n\t * @param x The x axis of the coordinate for the ellipse's center.\n\t * @param y The y axis of the coordinate for the ellipse's center.\n\t * @param radiusX The ellipse's major-axis radius.\n\t * @param radiusY The ellipse's minor-axis radius.\n\t * @param rotation The rotation for this ellipse, expressed in radians.\n\t * @param startAngle The starting point, measured from the x axis, from which it will be drawn, expressed in radians.\n\t * @param endAngle The end ellipse's angle to which it will be drawn, expressed in radians.\n\t * @param anticlockwise An optional Boolean which, if true, draws the ellipse anticlockwise (counter-clockwise), otherwise in a clockwise direction.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/ellipse\n\t */\n\tpublic createEllipseClip(\n\t\tx: number,\n\t\ty: number,\n\t\tradiusX: number,\n\t\tradiusY: number,\n\t\trotation: number,\n\t\tstartAngle: number,\n\t\tendAngle: number,\n\t\tanticlockwise?: boolean\n\t): this {\n\t\treturn this.createEllipsePath(x, y, radiusX, radiusY, rotation, startAngle, endAngle, anticlockwise).clip();\n\t}\n\n\t/**\n\t * Adds an arc to the path which is centered at (X, Y) position with radius r starting at startAngle and ending at\n\t * endAngle going in the given direction by anticlockwise (defaulting to clockwise).\n\t * @param x The X coordinate of the arc's center.\n\t * @param y The Y coordinate of the arc's center.\n\t * @param radius The arc's radius.\n\t * @param startAngle The angle at which the arc starts, measured clockwise from the positive x axis and expressed in radians.\n\t * @param endAngle The angle at which the arc ends, measured clockwise from the positive x axis and expressed in radians.\n\t * @param anticlockwise An optional Boolean which, if true, causes the arc to be drawn counter-clockwise between the two angles. By default it is drawn clockwise.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/arc\n\t */\n\tpublic arc(x: number, y: number, radius: number, startAngle: number, endAngle: number, anticlockwise?: boolean): this {\n\t\tthis.context.arc(x, y, radius, startAngle, endAngle, anticlockwise);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Adds an arc to the path with the given control points and radius, connected to the previous point by a straight line.\n\t * @param x1 The x axis of the coordinate for the first control point.\n\t * @param y1 The y axis of the coordinate for the first control point.\n\t * @param x2 The x axis of the coordinate for the second control point.\n\t * @param y2 The y axis of the coordinate for the second control point.\n\t * @param radius The arc's radius.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/arcTo\n\t */\n\tpublic arcTo(x1: number, y1: number, x2: number, y2: number, radius: number): this {\n\t\tthis.context.arcTo(x1, y1, x2, y2, radius);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Adds a quadratic Bézier curve to the path. It requires two points. The first point is a control point and the\n\t * second one is the end point. The starting point is the last point in the current path, which can be changed using\n\t * moveTo() before creating the quadratic Bézier curve.\n\t * @param cpx The x axis of the coordinate for the control point.\n\t * @param cpy The y axis of the coordinate for the control point.\n\t * @param x The x axis of the coordinate for the end point.\n\t * @param y The y axis of the coordinate for the end point.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/quadraticCurveTo\n\t */\n\tpublic quadraticCurveTo(cpx: number, cpy: number, x: number, y: number): this {\n\t\tthis.context.quadraticCurveTo(cpx, cpy, x, y);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Adds a cubic Bézier curve to the path. It requires three points. The first two points are control points and the\n\t * third one is the end point. The starting point is the last point in the current path, which can be changed using\n\t * moveTo() before creating the Bézier curve.\n\t * @param cp1x The x axis of the coordinate for the first control point.\n\t * @param cp1y The y axis of the coordinate for first control point.\n\t * @param cp2x The x axis of the coordinate for the second control point.\n\t * @param cp2y The y axis of the coordinate for the second control point.\n\t * @param x The x axis of the coordinate for the end point.\n\t * @param y The y axis of the coordinate for the end point.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/bezierCurveTo\n\t */\n\tpublic bezierCurveTo(cp1x: number, cp1y: number, cp2x: number, cp2y: number, x: number, y: number): this {\n\t\tthis.context.bezierCurveTo(cp1x, cp1y, cp2x, cp2y, x, y);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Connects the last point in the sub-path to the x, y coordinates with a straight line\n\t * @param x The x axis of the coordinate for the end of the line.\n\t * @param y The y axis of the coordinate for the end of the line.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/lineTo\n\t */\n\tpublic lineTo(x: number, y: number): this {\n\t\tthis.context.lineTo(x, y);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Moves the starting point of a new sub-path to the (X, Y) coordinates.\n\t * @param x The x axis of the point.\n\t * @param y The y axis of the point.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/moveTo\n\t */\n\tpublic moveTo(x: number, y: number): this {\n\t\tthis.context.moveTo(x, y);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Set the shadow's blur.\n\t * @param radius The shadow's blur radius to set.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/shadowBlur\n\t */\n\tpublic setShadowBlur(radius: number): this {\n\t\tthis.context.shadowBlur = radius;\n\t\treturn this;\n\t}\n\n\t/**\n\t * Set the shadow's color.\n\t * @param color A canvas' color resolvable to set as shadow's color.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/shadowColor\n\t */\n\tpublic setShadowColor(color: string): this {\n\t\tthis.context.shadowColor = color;\n\t\treturn this;\n\t}\n\n\t/**\n\t * Set the property that specifies the distance that the shadow will be offset in horizontal distance.\n\t * @param value The value in pixels for the distance.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/shadowOffsetX\n\t */\n\tpublic setShadowOffsetX(value: number): this {\n\t\tthis.context.shadowOffsetX = value;\n\t\treturn this;\n\t}\n\n\t/**\n\t * Set the property that specifies the distance that the shadow will be offset in vertical distance.\n\t * @param value The value in pixels for the distance.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/shadowOffsetY\n\t */\n\tpublic setShadowOffsetY(value: number): this {\n\t\tthis.context.shadowOffsetY = value;\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the miter limit ratio in space units. When getting, it returns the current value (10.0 by default). When\n\t * setting, zero, negative, Infinity and NaN values are ignored; otherwise the current value is set to the new value.\n\t * @param value A number specifying the miter limit ratio in space units. Zero, negative, Infinity and NaN values\n\t * are ignored.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/miterLimit\n\t */\n\tpublic setMiterLimit(value: number): this {\n\t\tthis.context.miterLimit = value;\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the type of compositing operation to apply when drawing new shapes, where type is a string identifying which\n\t * of the compositing or blending mode operations to use.\n\t * @param type The global composite operation mode.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/globalCompositeOperation\n\t */\n\tpublic setGlobalCompositeOperation(type: GlobalCompositeOperation): this {\n\t\tthis.context.globalCompositeOperation = type;\n\t\treturn this;\n\t}\n\n\t/**\n\t * Modify the alpha value that is applied to shapes and images before they are drawn into the canvas.\n\t * @param value The alpha value, from 0.0 (fully transparent) to 1.0 (fully opaque)\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/globalAlpha\n\t */\n\tpublic setGlobalAlpha(value: number): this {\n\t\tthis.context.globalAlpha = value;\n\t\treturn this;\n\t}\n\n\t/**\n\t * Modify whether or not image smoothing should be enabled.\n\t * @param value Whether or not image smoothing should be enabled.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/imageSmoothingEnabled\n\t */\n\tpublic setImageSmoothingEnabled(value: boolean): this {\n\t\tthis.context.imageSmoothingEnabled = value;\n\t\treturn this;\n\t}\n\n\t/**\n\t * Modify the smoothing quality value.\n\t * @param value The smoothing quality value.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/imageSmoothingEnabled\n\t */\n\tpublic setImageSmoothingQuality(value: ImageSmoothingQuality): this {\n\t\tthis.context.imageSmoothingQuality = value;\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the shadow blur and offsets to zero, then sets the shadow color to transparent. If shadows are not longer\n\t * used in a canvas and performance is critical, `.setShadowColor('transparent')` should be used instead, as of the\n\t * [note from Mozilla Developer Network](https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/shadowColor).\n\t * @example\n\t * new Canvas(500, 500)\n\t *     // Set a shadow color and blur\n\t *     .setShadowColor('rgba(23, 23, 23, 0.2)')\n\t *     .setShadowBlur(5)\n\t *     // Render the text with a blow effect\n\t *     .printText('Hello', 30, 50)\n\t *     // Reset the shadows\n\t *     .resetShadows()\n\t *     // Render the text without shadows\n\t *     .printText('World!', 30, 100);\n\t */\n\tpublic resetShadows(): this {\n\t\treturn this.setShadowBlur(0).setShadowOffsetX(0).setShadowOffsetY(0).setShadowColor('transparent');\n\t}\n\n\t/**\n\t * Clear a circle.\n\t * @param x The position x in the center of the clip's circle.\n\t * @param y The position y in the center of the clip's circle.\n\t * @param radius The radius for the clip.\n\t * @param start The degree in radians to start drawing the circle.\n\t * @param angle The degree in radians to finish drawing the circle, defaults to a full circle.\n\t * @param antiClockwise Whether or not the angle should be anti-clockwise.\n\t */\n\tpublic clearCircle(x: number, y: number, radius: number, start = 0, angle = Math.PI * 2, antiClockwise = false): this {\n\t\treturn this.createCircularClip(x, y, radius, start, angle, antiClockwise).clearRectangle(x - radius, y - radius, radius * 2, radius * 2);\n\t}\n\n\t/**\n\t * Clear an area.\n\t * @param dx The position x to start drawing the element.\n\t * @param dy The position y to start drawing the element.\n\t * @param width The width of the element.\n\t * @param height The height of the element.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/clearRect\n\t */\n\tpublic clearRectangle(dx = 0, dy = 0, width = this.width, height = this.height): this {\n\t\tthis.context.clearRect(dx, dy, width, height);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Reports whether or not the specified point is contained in the current path.\n\t * @param x The X coordinate of the point to check.\n\t * @param y The Y coordinate of the point to check.\n\t * @param fillRule The algorithm by which to determine if a point is inside a path or outside a path.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/isPointInPath\n\t */\n\tpublic isPointInPath(x: number, y: number, fillRule?: CanvasFillRule): boolean;\n\t/**\n\t * Reports whether or not the specified point is contained in the given path.\n\t * @param path The {@link Path2D} to check against.\n\t * @param x The X coordinate of the point to check.\n\t * @param y The Y coordinate of the point to check.\n\t * @param fillRule The algorithm by which to determine if a point is inside a path or outside a path.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/isPointInPath\n\t */\n\tpublic isPointInPath(path: Path2D, x: number, y: number, fillRule?: CanvasFillRule): boolean;\n\tpublic isPointInPath(...args: [any, any]): boolean {\n\t\treturn this.context.isPointInPath(...args);\n\t}\n\n\t/**\n\t * Reports whether or not the specified point is inside the area contained by the stroking of the current path.\n\t * @param x The X coordinate of the point to check.\n\t * @param y The Y coordinate of the point to check.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/isPointInStroke\n\t */\n\tpublic isPointInStroke(x: number, y: number): boolean;\n\t/**\n\t * Reports whether or not the specified point is inside the area contained by the stroking of the given path.\n\t * @param path The {@link Path2D} to check against.\n\t * @param x The X coordinate of the point to check.\n\t * @param y The Y coordinate of the point to check.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/isPointInStroke\n\t */\n\tpublic isPointInStroke(path: Path2D, x: number, y: number): boolean;\n\tpublic isPointInStroke(...args: [any, any]): boolean {\n\t\treturn this.context.isPointInStroke(...args);\n\t}\n\n\t/**\n\t * Process data with this as the context\n\t * @param fn A callback function\n\t * @param args Extra arguments to pass to the function\n\t */\n\tpublic process<Args extends readonly any[]>(fn: (this: this, canvas: this, ...args: Args) => unknown, ...args: Args): this {\n\t\tfn.call(this, this, ...args);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Wraps a text into a width-limited multi-line text.\n\t * @param text The text to wrap\n\t * @param wrapWidth The wrap width\n\t * @example\n\t * // Calculate the wrapped text and return it, which\n\t * // is useful for storage to avoid re-calculating the\n\t * // wrapped text\n\t * const wrappedText = new Canvas(500, 300)\n\t *     .setTextFont('48px Verdana')\n\t *     .wrapText('Hello World, this is a quite\\nlong text.', 300);\n\t * @example\n\t * // Wrap the text and add it\n\t * const buffer = new Canvas(500, 300)\n\t *     .setTextFont('48px Verdana')\n\t *     .process((canvas) => {\n\t *         const wrappedText = canvas.wrapText('Hello World, this is a quite\\nlong text.');\n\t *         return canvas\n\t *             .setTextAlign('center')\n\t *             .addMultilineText(wrappedText, 250, 50)\n\t *     })\n\t *     .png(); // Returns a Buffer\n\t */\n\tpublic wrapText(text: string, wrapWidth: number): string {\n\t\treturn textWrap(this, text, wrapWidth);\n\t}\n\n\t/**\n\t * Creates a new, blank {@link ImageData} object with the specified dimensions. All of the pixels in the new object are transparent black.\n\t * @param sw The width to give the new {@link ImageData} object. A negative value flips the rectangle around the vertical axis.\n\t * @param sh The height to give the new {@link ImageData} object. A negative value flips the rectangle around the horizontal axis.\n\t * @param settings The settings to be used.\n\t */\n\tpublic createImageData(sw: number, sh: number, settings?: ImageDataSettings): ImageData;\n\t/**\n\t * Creates a new, blank {@link ImageData} object with the dimensions of the specified object. All of the pixels in the new object are transparent black.\n\t * @param imageData An existing {@link ImageData} object from which to copy the width and height. The image itself is not copied.\n\t */\n\tpublic createImageData(imageData: ImageData): ImageData;\n\tpublic createImageData(...args: [any]): ImageData {\n\t\treturn this.context.createImageData(...args);\n\t}\n\n\t/**\n\t * Gets a JPEG buffer.\n\t * @param quality The quality to use, defaults to `92`.\n\t * @returns A JPEG buffer.\n\t * @see {@link jpegAsync} for the async version.\n\t */\n\tpublic jpeg(quality?: number): Buffer {\n\t\treturn this.canvas.encodeSync('jpeg', quality);\n\t}\n\n\t/**\n\t * Gets a JPEG buffer.\n\t * @param quality The quality to use, defaults to `92`.\n\t * @returns A JPEG buffer.\n\t * @see {@link jpeg} for the sync version.\n\t */\n\tpublic jpegAsync(quality?: number): Promise<Buffer> {\n\t\treturn this.canvas.encode('jpeg', quality);\n\t}\n\n\t/**\n\t * Gets a WebP buffer.\n\t * @param quality The quality to use, defaults to `80`.\n\t * @returns A WebP buffer.\n\t * @see {@link webpAsync} for the async version.\n\t */\n\tpublic webp(quality?: number): Buffer {\n\t\treturn this.canvas.encodeSync('webp', quality);\n\t}\n\n\t/**\n\t * Gets a WebP buffer.\n\t * @param quality The quality to use, defaults to `80`.\n\t * @returns A WebP buffer.\n\t * @see {@link webp} for the sync version.\n\t */\n\tpublic webpAsync(quality?: number): Promise<Buffer> {\n\t\treturn this.canvas.encode('webp', quality);\n\t}\n\n\t/**\n\t * Gets a PNG buffer.\n\t * @returns A PNG buffer.\n\t * @see {@link pngAsync} for the async version.\n\t */\n\tpublic png(): Buffer {\n\t\treturn this.canvas.encodeSync('png');\n\t}\n\n\t/**\n\t * Gets a PNG buffer.\n\t * @returns A PNG buffer.\n\t * @see {@link png} for the sync version.\n\t */\n\tpublic pngAsync(): Promise<Buffer> {\n\t\treturn this.canvas.encode('png');\n\t}\n\n\t/**\n\t * Gets an AVIF buffer.\n\t * @returns A AVIF buffer.\n\t * @see {@link avifAsync} for the async version.\n\t */\n\tpublic avif(cfg?: AvifConfig): Buffer {\n\t\treturn this.canvas.encodeSync('avif', cfg);\n\t}\n\n\t/**\n\t * Gets an AVIF buffer.\n\t * @returns A AVIF buffer.\n\t * @see {@link avif} for the sync version.\n\t */\n\tpublic avifAsync(cfg?: AvifConfig): Promise<Buffer> {\n\t\treturn this.canvas.encode('avif', cfg);\n\t}\n\n\tpublic toDataURL(mime?: 'image/png'): string;\n\tpublic toDataURL(mime: 'image/jpeg' | 'image/webp', quality?: number): string;\n\tpublic toDataURL(mime?: 'image/jpeg' | 'image/webp' | 'image/png', quality?: number): string;\n\tpublic toDataURL(mime?: 'image/avif', cfg?: AvifConfig): string;\n\tpublic toDataURL(...args: [any]): string {\n\t\treturn this.canvas.toDataURL(...args);\n\t}\n\n\tpublic toDataURLAsync(mime?: 'image/png'): Promise<string>;\n\tpublic toDataURLAsync(mime: 'image/jpeg' | 'image/webp', quality?: number): Promise<string>;\n\tpublic toDataURLAsync(mime?: 'image/jpeg' | 'image/webp' | 'image/png', quality?: number): Promise<string>;\n\tpublic toDataURLAsync(mime?: 'image/avif', cfg?: AvifConfig): Promise<string>;\n\tpublic toDataURLAsync(...args: [any]): Promise<string> {\n\t\treturn this.canvas.toDataURLAsync(...args);\n\t}\n\n\tprotected parseFont(font: string) {\n\t\tconst result = fontRegExp.exec(font);\n\t\tif (result === null) return [font] as const;\n\n\t\treturn [font.slice(0, result.index), Number(result[1]), font.slice(result.index + result[1].length)] as const;\n\t}\n\n\tprotected resolveCircularCoordinates(\n\t\timageOrBuffer: ImageResolvable,\n\t\tx: number,\n\t\ty: number,\n\t\tradius: number,\n\t\tfit: NonNullable<PrintCircularOptions['fit']>\n\t): ResolvedCircularCoordinates {\n\t\tconst { width: w, height: h } = imageOrBuffer;\n\t\tif (fit === 'none') {\n\t\t\treturn {\n\t\t\t\tpositionX: x - w / 2,\n\t\t\t\tpositionY: y - h / 2,\n\t\t\t\tsizeX: w,\n\t\t\t\tsizeY: h\n\t\t\t};\n\t\t}\n\n\t\tconst ratio = w / h;\n\t\tconst diameter = radius * 2;\n\n\t\tif (fit === 'fill' || ratio === 1) {\n\t\t\treturn {\n\t\t\t\tpositionX: x - radius,\n\t\t\t\tpositionY: y - radius,\n\t\t\t\tsizeX: diameter,\n\t\t\t\tsizeY: diameter\n\t\t\t};\n\t\t}\n\n\t\tif (fit === 'contain') {\n\t\t\treturn ratio > 1\n\t\t\t\t? {\n\t\t\t\t\t\tpositionX: x - radius,\n\t\t\t\t\t\tpositionY: y - radius / ratio,\n\t\t\t\t\t\tsizeX: diameter,\n\t\t\t\t\t\tsizeY: diameter / ratio\n\t\t\t\t  }\n\t\t\t\t: {\n\t\t\t\t\t\tpositionX: x - radius * ratio,\n\t\t\t\t\t\tpositionY: y - radius,\n\t\t\t\t\t\tsizeX: diameter * ratio,\n\t\t\t\t\t\tsizeY: diameter\n\t\t\t\t  };\n\t\t}\n\n\t\tif (ratio > 1) {\n\t\t\tconst sizeX = diameter * ratio;\n\t\t\tconst sizeY = diameter;\n\t\t\treturn {\n\t\t\t\tpositionX: x - sizeX / 2,\n\t\t\t\tpositionY: y - sizeY / 2,\n\t\t\t\tsizeX,\n\t\t\t\tsizeY\n\t\t\t};\n\t\t}\n\n\t\tconst sizeX = diameter;\n\t\tconst sizeY = diameter / ratio;\n\t\treturn {\n\t\t\tpositionX: x - sizeX / 2,\n\t\t\tpositionY: y - sizeY / 2,\n\t\t\tsizeX,\n\t\t\tsizeY\n\t\t};\n\t}\n}\n\ninterface ResolvedCircularCoordinates {\n\tpositionX: number;\n\tpositionY: number;\n\tsizeX: number;\n\tsizeY: number;\n}\n\nexport { loadImage, Path2D, GlobalFonts, NativeImage as Image };\n\nexport const resolveImage = deprecate(loadImage, 'resolveImage() is deprecated. Use loadImage() instead.');\n\nexport function loadFont(font: Buffer, alias?: string): boolean;\nexport function loadFont(path: string, alias?: string): boolean;\nexport function loadFont(fontOrPath: Buffer | string, alias?: string): boolean {\n\treturn typeof fontOrPath === 'string' ? GlobalFonts.registerFromPath(fontOrPath, alias) : GlobalFonts.register(fontOrPath, alias);\n}\n\nexport function loadFontsFromDirectory(path: string): number {\n\treturn GlobalFonts.loadFontsFromDir(path);\n}\n\nexport const registerFont = deprecate(loadFont, 'registerFont() is deprecated. Use loadFont() instead.');\n\n/**\n * @see https://developer.mozilla.org/en-US/docs/Web/CSS/font-variant-ligatures\n */\nexport type FontVariantLigatures =\n\t| 'common-ligatures'\n\t| 'no-common-ligatures'\n\t| 'discretionary-ligatures'\n\t| 'no-discretionary-ligatures'\n\t| 'historical-ligatures'\n\t| 'no-historical-ligatures'\n\t| 'contextual'\n\t| 'no-contextual';\n\n/**\n * @see https://developer.mozilla.org/en-US/docs/Web/CSS/font-variant-alternates\n */\nexport type FontVariantAlternates =\n\t| 'historical-forms'\n\t| `stylistic(${string})`\n\t| `styleset(${string})`\n\t| `character-variant(${string})`\n\t| `swash(${string})`\n\t| `ornaments(${string})`\n\t| `annotation()${string}'`;\n\n/**\n * @see https://developer.mozilla.org/en-US/docs/Web/CSS/font-variant-caps\n */\nexport type FontVariantCaps = 'small-caps' | 'all-small-caps' | 'petite-caps';\n\n/**\n * @see https://developer.mozilla.org/en-US/docs/Web/CSS/font-variant-numeric\n */\nexport type FontVariantNumeric =\n\t| 'lining-nums'\n\t| 'oldstyle-nums'\n\t| 'proportional-nums'\n\t| 'tabular-nums'\n\t| 'diagonal-fractions'\n\t| 'stacked-fractions'\n\t| 'ordinal'\n\t| 'slashed-zero';\n\n/**\n * @see https://developer.mozilla.org/en-US/docs/Web/CSS/font-variant-east-asian\n */\nexport type FontVariantEastAsian =\n\t| 'jis78'\n\t| 'jis83'\n\t| 'jis90'\n\t| 'jis04'\n\t| 'simplified'\n\t| 'traditional'\n\t| 'full-width'\n\t| 'proportional-width'\n\t| 'ruby';\n\nexport type FontVariantString = 'normal' | 'none' | string;\n\nexport type FontVariants = FontVariantLigatures | FontVariantAlternates | FontVariantCaps | FontVariantNumeric | FontVariantEastAsian;\n\ntype GetFontVariant<K extends FontVariants> = K extends FontVariantLigatures\n\t? FontVariantLigatures\n\t: K extends FontVariantAlternates\n\t? FontVariantAlternates\n\t: K extends FontVariantCaps\n\t? FontVariantCaps\n\t: K extends FontVariantNumeric\n\t? FontVariantNumeric\n\t: FontVariantEastAsian;\n\nexport function fontVariant<K1 extends FontVariantString>(k1: K1): K1;\nexport function fontVariant<K1 extends FontVariants, K2 extends Exclude<FontVariants, GetFontVariant<K1>>>(k1: K1, k2: K2): `${K1} ${K2}`;\nexport function fontVariant<\n\tK1 extends FontVariants,\n\tK2 extends Exclude<FontVariants, GetFontVariant<K1>>,\n\tK3 extends Exclude<FontVariants, GetFontVariant<K2>>\n>(k1: K1, k2: K2, k3: K3): `${K1} ${K2} ${K3}`;\nexport function fontVariant<\n\tK1 extends FontVariants,\n\tK2 extends Exclude<FontVariants, GetFontVariant<K1>>,\n\tK3 extends Exclude<FontVariants, GetFontVariant<K2>>,\n\tK4 extends Exclude<FontVariants, GetFontVariant<K3>>\n>(k1: K1, k2: K2, k3: K3, k4: K4): `${K1} ${K2} ${K3} ${K4}`;\nexport function fontVariant<\n\tK1 extends FontVariants,\n\tK2 extends Exclude<FontVariants, GetFontVariant<K1>>,\n\tK3 extends Exclude<FontVariants, GetFontVariant<K2>>,\n\tK4 extends Exclude<FontVariants, GetFontVariant<K3>>,\n\tK5 extends Exclude<FontVariants, GetFontVariant<K4>>\n>(k1: K1, k2: K2, k3: K3, k4: K4, k5: K5): `${K1} ${K2} ${K3} ${K4} ${K5}`;\nexport function fontVariant(...args: readonly FontVariantString[]): string {\n\treturn args.join(' ');\n}\n\n// Start Section: Filters\n/**\n * Invert an image\n * @param canvas The Canvas instance\n */\nexport const invert = (canvas: Canvas) =>\n\tcanvas.save().setGlobalCompositeOperation('difference').setColor('white').printRectangle(0, 0, canvas.width, canvas.height).restore();\n\n/**\n * Greyscale an image\n * @param canvas The Canvas instance\n */\nexport const greyscale = (canvas: Canvas) => {\n\tconst imageData = canvas.getImageData();\n\tconst { data } = imageData;\n\tfor (let i = 0; i < data.length; i += 4) {\n\t\tconst luminance = 0.2126 * data[i] + 0.7152 * data[i + 1] + 0.0722 * data[i + 2];\n\t\tdata[i] = luminance;\n\t\tdata[i + 1] = luminance;\n\t\tdata[i + 2] = luminance;\n\t}\n\n\treturn canvas.putImageData(imageData, 0, 0);\n};\nexport const grayscale = greyscale;\n\n/**\n * Invert then greyscale an image\n * @param canvas The Canvas instance\n */\nexport const invertGrayscale = (canvas: Canvas) => {\n\tconst imageData = canvas.getImageData();\n\tconst { data } = imageData;\n\tfor (let i = 0; i < data.length; i += 4) {\n\t\tconst luminance = 255 - (0.2126 * data[i] + 0.7152 * data[i + 1] + 0.0722 * data[i + 2]);\n\t\tdata[i] = luminance;\n\t\tdata[i + 1] = luminance;\n\t\tdata[i + 2] = luminance;\n\t}\n\n\treturn canvas.putImageData(imageData, 0, 0);\n};\nexport const invertGreyscale = invertGrayscale;\n\n/**\n * Give an image a sepia tone\n * @param canvas The Canvas instance\n */\nexport const sepia = (canvas: Canvas): Canvas => {\n\tconst imageData = canvas.getImageData();\n\tconst { data } = imageData;\n\tfor (let i = 0; i < data.length; i += 4) {\n\t\tconst r = data[i];\n\t\tconst g = data[i + 1];\n\t\tconst b = data[i + 2];\n\t\tdata[i] = r * 0.393 + g * 0.769 + b * 0.189;\n\t\tdata[i + 1] = r * 0.349 + g * 0.686 + b * 0.168;\n\t\tdata[i + 2] = r * 0.272 + g * 0.534 + b * 0.131;\n\t}\n\treturn canvas.putImageData(imageData, 0, 0);\n};\n\n/**\n * Turn an image into a silhouette\n * @param canvas The Canvas instance\n */\nexport const silhouette = (canvas: Canvas): Canvas => {\n\tconst imageData = canvas.getImageData();\n\tconst { data } = imageData;\n\tfor (let i = 0; i < data.length; i += 4) {\n\t\tdata[i] = 0;\n\t\tdata[i + 1] = 0;\n\t\tdata[i + 2] = 0;\n\t}\n\n\treturn canvas.putImageData(imageData, 0, 0);\n};\n\n/**\n * Apply a threshold to the image\n * @param canvas The Canvas instance\n * @param threshold The threshold to apply in a range of 0 to 255\n */\nexport const threshold = (canvas: Canvas, threshold: number): Canvas => {\n\tconst imageData = canvas.getImageData();\n\tconst { data } = imageData;\n\tfor (let i = 0; i < data.length; i += 4) {\n\t\tconst luminance = 0.2126 * data[i] + 0.7152 * data[i + 1] + 0.0722 * data[i + 2] >= threshold ? 255 : 0;\n\t\tdata[i] = luminance;\n\t\tdata[i + 1] = luminance;\n\t\tdata[i + 2] = luminance;\n\t}\n\n\treturn canvas.putImageData(imageData, 0, 0);\n};\n\n/**\n * Apply an inverted threshold to the image\n * @param canvas The Canvas instance\n * @param threshold The threshold to apply in a range of 0 to 255\n */\nexport const invertedThreshold = (canvas: Canvas, threshold: number): Canvas => {\n\tconst imageData = canvas.getImageData();\n\tconst { data } = imageData;\n\tfor (let i = 0; i < data.length; i += 4) {\n\t\tconst luminance = 0.2126 * data[i] + 0.7152 * data[i + 1] + 0.0722 * data[i + 2] >= threshold ? 0 : 255;\n\t\tdata[i] = luminance;\n\t\tdata[i + 1] = luminance;\n\t\tdata[i + 2] = luminance;\n\t}\n\n\treturn canvas.putImageData(imageData, 0, 0);\n};\n\n/**\n * Brighten an image\n * @param canvas The Canvas instance\n * @param brightness The brightness to apply in a range of 0 to 255\n */\nexport const brightness = (canvas: Canvas, brightness: number): Canvas => {\n\tconst imageData = canvas.getImageData();\n\tconst { data } = imageData;\n\tfor (let i = 0; i < data.length; i += 4) {\n\t\tdata[i] += brightness;\n\t\tdata[i + 1] += brightness;\n\t\tdata[i + 2] += brightness;\n\t}\n\n\treturn canvas.putImageData(imageData, 0, 0);\n};\n\n/**\n * Darken an image\n * @param canvas The Canvas instance\n * @param darkness The darkness to apply in a range of 0 to 255\n */\nexport const darkness = (canvas: Canvas, darkness: number): Canvas => {\n\tconst imageData = canvas.getImageData();\n\tconst { data } = imageData;\n\tfor (let i = 0; i < data.length; i += 4) {\n\t\tdata[i] -= darkness;\n\t\tdata[i + 1] -= darkness;\n\t\tdata[i + 2] -= darkness;\n\t}\n\n\treturn canvas.putImageData(imageData, 0, 0);\n};\nexport const myOldFriend = darkness;\n\n/**\n * Convolute a image. This filter needs a fix.\n * @param canvas The Canvas instance\n * @param weights The weights\n * @param opaque Whether or not pixels should try to be opaque\n * @see https://www.html5rocks.com/en/tutorials/canvas/imagefilters/\n */\nexport const convolute = (canvas: Canvas, weights: readonly number[], opaque = true): Canvas => {\n\tconst side = Math.round(Math.sqrt(weights.length));\n\tconst halfSide = Math.floor(side / 2);\n\n\tconst pixels = canvas.getImageData();\n\tconst src = pixels.data;\n\tconst sw = pixels.width;\n\tconst sh = pixels.height;\n\n\t// pad output by the convolution matrix\n\tconst w = sw;\n\tconst h = sh;\n\tconst output = canvas.getImageData();\n\tconst dst = output.data;\n\n\t// go through the destination image pixels\n\tconst alphaFac = opaque ? 1 : 0;\n\tfor (let y = 0; y < h; y++) {\n\t\tfor (let x = 0; x < w; x++) {\n\t\t\tconst sy = y;\n\t\t\tconst sx = x;\n\t\t\tconst dstOff = (y * w + x) * 4;\n\t\t\t// calculate the weighed sum of the source image pixels that\n\t\t\t// fall under the convolution matrix\n\t\t\tlet r = 0;\n\t\t\tlet g = 0;\n\t\t\tlet b = 0;\n\t\t\tlet a = 0;\n\t\t\tfor (let cy = 0; cy < side; cy++) {\n\t\t\t\tfor (let cx = 0; cx < side; cx++) {\n\t\t\t\t\tconst scy = sy + cy - halfSide;\n\t\t\t\t\tconst scx = sx + cx - halfSide;\n\t\t\t\t\tif (scy >= 0 && scy < sh && scx >= 0 && scx < sw) {\n\t\t\t\t\t\tconst srcOff = (scy * sw + scx) * 4;\n\t\t\t\t\t\tconst wt = weights[cy * side + cx];\n\t\t\t\t\t\tr += src[srcOff] * wt;\n\t\t\t\t\t\tg += src[srcOff + 1] * wt;\n\t\t\t\t\t\tb += src[srcOff + 2] * wt;\n\t\t\t\t\t\ta += src[srcOff + 3] * wt;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tdst[dstOff] = r;\n\t\t\tdst[dstOff + 1] = g;\n\t\t\tdst[dstOff + 2] = b;\n\t\t\tdst[dstOff + 3] = a + alphaFac * (255 - a);\n\t\t}\n\t}\n\n\treturn canvas.putImageData(output, 0, 0);\n};\n\n/**\n * The LaPlace matrix for edge\n * @internal\n */\nconst edgeLaPlaceMatrix = [0, -1, 0, -1, 4, -1, 0, -1, 0];\n\n/**\n * Display an image's edges\n * @param canvas The Canvas instance\n */\nexport const edge = (canvas: Canvas): Canvas => convolute(canvas, edgeLaPlaceMatrix, true);\n\n/**\n * The LaPlace matrix for sharpen\n * @internal\n */\nconst sharpenLaPlaceMatrix = [0, -1, 0, -1, 5, -1, 0, -1, 0];\n\n/**\n * Sharpen an image\n * @param canvas The Canvas instance\n * @param passes The amount of iterations to do\n */\nexport const sharpen = (canvas: Canvas, passes = 1): Canvas => {\n\tfor (let i = 0; i < passes; ++i) {\n\t\tconvolute(canvas, sharpenLaPlaceMatrix, true);\n\t}\n\n\treturn canvas;\n};\n\n/**\n * The LaPlace matrix for blur\n * @internal\n */\nconst blurLaPlaceMatrix = [1 / 9, 1 / 9, 1 / 9, 1 / 9, 1 / 9, 1 / 9, 1 / 9, 1 / 9, 1 / 9];\n\n/**\n * Blur an image\n * @param canvas The Canvas instance\n * @param passes The amount of iterations to do\n */\nexport const blur = (canvas: Canvas, passes = 1): Canvas => {\n\tfor (let i = 0; i < passes; ++i) {\n\t\tconvolute(canvas, blurLaPlaceMatrix, true);\n\t}\n\n\treturn canvas;\n};\n// End Section: Filters\n// Start Section: Util\nexport const fontRegExp = /([\\d.]+)(px|pt|pc|in|cm|mm|%|em|ex|ch|rem|q)/i;\nexport const getFontHeight = (() => {\n\tconst kCache = new Map<string, number>();\n\n\treturn (font: string): number => {\n\t\t// If it was already parsed, do not parse again\n\t\tconst previous = kCache.get(font);\n\t\tif (previous) return previous;\n\n\t\t// Test for required properties first, return null if the text is invalid\n\t\tconst sizeFamily = fontRegExp.exec(font);\n\t\tif (!sizeFamily) return 0;\n\n\t\tlet size = Number(sizeFamily[1]);\n\t\tconst unit = sizeFamily[2];\n\n\t\tswitch (unit) {\n\t\t\tcase 'pt':\n\t\t\t\tsize /= 0.75;\n\t\t\t\tbreak;\n\t\t\tcase 'pc':\n\t\t\t\tsize *= 16;\n\t\t\t\tbreak;\n\t\t\tcase 'in':\n\t\t\t\tsize *= 96;\n\t\t\t\tbreak;\n\t\t\tcase 'cm':\n\t\t\t\tsize *= 96.0 / 2.54;\n\t\t\t\tbreak;\n\t\t\tcase 'mm':\n\t\t\t\tsize *= 96.0 / 25.4;\n\t\t\t\tbreak;\n\t\t\tcase 'em':\n\t\t\tcase 'rem':\n\t\t\t\tsize *= 16 / 0.75;\n\t\t\t\tbreak;\n\t\t\tcase 'q':\n\t\t\t\tsize *= 96 / 25.4 / 4;\n\t\t\t\tbreak;\n\t\t}\n\n\t\tkCache.set(font, size);\n\t\treturn size;\n\t};\n})();\n\nexport const textWrap = (canvas: Canvas, text: string, wrapWidth: number): string => {\n\tconst result = [];\n\tconst buffer = [];\n\n\tconst spaceWidth = canvas.measureText(' ').width;\n\n\t// Run the loop for each line\n\tfor (const line of text.split(/\\r?\\n/)) {\n\t\tlet spaceLeft = wrapWidth;\n\n\t\t// Run the loop for each word\n\t\tfor (const word of line.split(' ')) {\n\t\t\tconst wordWidth = canvas.measureText(word).width;\n\t\t\t// eslint-disable-next-line @typescript-eslint/restrict-plus-operands\n\t\t\tconst wordWidthWithSpace = wordWidth + spaceWidth;\n\n\t\t\tif (wordWidthWithSpace > spaceLeft) {\n\t\t\t\tif (buffer.length) {\n\t\t\t\t\tresult.push(buffer.join(' '));\n\t\t\t\t\tbuffer.length = 0;\n\t\t\t\t}\n\t\t\t\tbuffer.push(word);\n\t\t\t\tspaceLeft = wrapWidth - wordWidth;\n\t\t\t} else {\n\t\t\t\tspaceLeft -= wordWidthWithSpace;\n\t\t\t\tbuffer.push(word);\n\t\t\t}\n\t\t}\n\n\t\tif (buffer.length) {\n\t\t\tresult.push(buffer.join(' '));\n\t\t\tbuffer.length = 0;\n\t\t}\n\t}\n\n\treturn result.join('\\n');\n};\n\n/**\n * The names of the filters that take a string argument.\n */\ntype LiteralFilters = 'url';\n\nexport type Percentage<T extends number = number> = `${T}%`;\n\n/**\n * The names of the filters that take a percentage argument.\n */\ntype PercentageFilters = 'brightness' | 'contrast' | 'grayscale' | 'invert' | 'opacity' | 'saturate' | 'sepia';\n\ntype RelativeLengthUnits = 'cap' | 'ch' | 'em' | 'ex' | 'ic' | 'lh' | 'rem' | 'rlh';\ntype RelativeUnits = RelativeLengthUnits | '%';\ntype ViewportPercentageUnits = 'vh' | 'vw' | 'vi' | 'vb' | 'vmin' | 'vmax';\ntype AbsoluteLengthUnits = 'px' | 'cm' | 'mm' | 'Q' | 'in' | 'pc' | 'pt';\ntype LengthUnits = RelativeUnits | ViewportPercentageUnits | AbsoluteLengthUnits;\nexport type Length<T extends number = number> = `${T}${LengthUnits}`;\n\n/**\n * The names of the filters that take a length argument.\n */\ntype LengthFilters = 'blur';\n\ntype AngleUnits = 'deg' | 'grad' | 'rad' | 'turn';\nexport type Angle<T extends number = number> = `${T}${AngleUnits}`;\n\n/**\n * The names of the filters that take an angle argument.\n */\ntype AngleFilters = 'hue-rotate';\n\nexport type Color = ColorKeyword | ColorHexadecimal | ColorRGB | ColorRGBA | ColorHSL | ColorHSLA;\n\ninterface Filter {\n\t<K extends LiteralFilters, V extends string>(name: K, url: V): `${K}(${V})`;\n\t<K extends PercentageFilters, V extends Percentage>(name: K, percentage: V): `${K}(${V})`;\n\t<K extends LengthFilters, V extends Length>(name: K, length: V): `${K}(${V})`;\n\t<K extends AngleFilters, V extends Angle>(name: K, angle: V): `${K}(${V})`;\n\t<Vx extends Length, Vy extends Length>(name: 'drop-shadow', x: Vx, y: Vy): `drop-shadow(${Vx} ${Vy})`;\n\t<Vx extends Length, Vy extends Length, Vb extends Length>(name: 'drop-shadow', x: Vx, y: Vy, blur: Vb): `drop-shadow(${Vx} ${Vy} ${Vb})`;\n\t<Vx extends Length, Vy extends Length, Vc extends Color>(name: 'drop-shadow', x: Vx, y: Vy, color: Vc): `drop-shadow(${Vx} ${Vy} ${Vc})`;\n\t<Vx extends Length, Vy extends Length, Vb extends Length, Vc extends Color>(\n\t\tname: 'drop-shadow',\n\t\tx: Vx,\n\t\ty: Vy,\n\t\tblur: Vb,\n\t\tcolor: Vc\n\t): `drop-shadow(${Vx} ${Vy} ${Vb} ${Vc})`;\n\t(value: 'none'): 'none';\n}\n\n// @ts-expect-error: Overload hell\nexport const filter: Filter = (name: string, ...args: readonly any[]) => `${name}(${args.join(' ')})` as const;\n\n/**\n * Represents a formatted hexadecimal value.\n */\nexport type ColorHexadecimal<T extends string = string> = `#${T}`;\n\n/**\n * Utility to format an hexadecimal string into a CSS hexadecimal string.\n * @param hex The hexadecimal code.\n * @example\n * hex('FFF'); // -> '#FFF'\n * hex('0F0F0F'); // -> '#0F0F0F'\n */\nexport const hex = <T extends string>(hex: T): ColorHexadecimal<T> => `#${hex}` as const;\n\n/**\n * Represents a formatted RGB value.\n */\nexport type ColorRGB<R extends number = number, G extends number = number, B extends number = number> = `rgb(${R}, ${G}, ${B})`;\n\n/**\n * Utility to format a RGB set of values into a string.\n * @param red The red value, must be a number between 0 and 255 inclusive.\n * @param green The green value, must be a number between 0 and 255 inclusive.\n * @param blue The blue value, must be a number between 0 and 255 inclusive.\n * @see https://en.wikipedia.org/wiki/RGB_color_model#Geometric_representation\n * @example\n * rgb(255, 150, 65); // -> 'rgb(255, 150, 65)'\n */\nexport const rgb = <R extends number, G extends number, B extends number>(red: R, green: G, blue: B): ColorRGB<R, G, B> =>\n\t`rgb(${red}, ${green}, ${blue})` as const;\n\n/**\n * Represents a formatted RGBA value.\n */\nexport type ColorRGBA<\n\tR extends number = number,\n\tG extends number = number,\n\tB extends number = number,\n\tA extends number = number\n> = `rgba(${R}, ${G}, ${B}, ${A})`;\n\n/**\n * Utility to format a RGBA set of values into a string.\n * @param red The red value, must be a number between 0 and 255 inclusive.\n * @param green The green value, must be a number between 0 and 255 inclusive.\n * @param blue The blue value, must be a number between 0 and 255 inclusive.\n * @param alpha The alpha value, must be a number between 0 and 1 inclusive.\n * @see https://en.wikipedia.org/wiki/RGB_color_model#Geometric_representation\n * @example\n * rgba(255, 150, 65, 0.3); // -> 'rgba(255, 150, 65, 0.3)'\n */\nexport const rgba = <R extends number, G extends number, B extends number, A extends number>(\n\tred: R,\n\tgreen: G,\n\tblue: B,\n\talpha: A\n): ColorRGBA<R, G, B, A> => `rgba(${red}, ${green}, ${blue}, ${alpha})` as const;\n\n/**\n * Represents a formatted HSL value.\n */\nexport type ColorHSL<H extends number = number, S extends number = number, L extends number = number> = `hsl(${H}, ${S}%, ${L}%)`;\n\n/**\n * Utility to format a HSL set of values into a string.\n * @param hue The hue, must be a number between 0 and 360 inclusive.\n * @param saturation The saturation, must be a number between 0 and 100 inclusive.\n * @param lightness The lightness, must be a number between 0 and 100 inclusive, 0 will make it black, 100 will make it white.\n * @see https://en.wikipedia.org/wiki/HSL_and_HSV\n * @example\n * hsl(120, 100, 40); // -> 'hsl(120, 100, 40)'\n */\nexport const hsl = <H extends number, S extends number, L extends number>(hue: H, saturation: S, lightness: L): ColorHSL<H, S, L> =>\n\t`hsl(${hue}, ${saturation}%, ${lightness}%)` as const;\n\n/**\n * Represents a formatted HSL value.\n */\nexport type ColorHSLA<\n\tH extends number = number,\n\tS extends number = number,\n\tL extends number = number,\n\tA extends number = number\n> = `hsla(${H}, ${S}%, ${L}%, ${A})`;\n\n/**\n * Utility to format a HSLA set of values into a string.\n * @param hue The hue, must be a number between 0 and 360 inclusive.\n * @param saturation The saturation, must be a number between 0 and 100 inclusive.\n * @param lightness The lightness, must be a number between 0 and 100 inclusive, 0 will make it black, 100 will make it white\n * @param alpha The alpha value, must be a number between 0 and 1 inclusive.\n * @see https://en.wikipedia.org/wiki/HSL_and_HSV\n * @example\n * hsla(120, 100, 40, 0.4); // -> 'hsla(120, 100, 40, 0.4)'\n */\nexport const hsla = <H extends number, S extends number, L extends number, A extends number>(\n\thue: H,\n\tsaturation: S,\n\tlightness: L,\n\talpha: A\n): ColorHSLA<H, S, L, A> => `hsla(${hue}, ${saturation}%, ${lightness}%, ${alpha})` as const;\n\n/**\n * Utility to type-safely use CSS colors.\n * @param color The CSS keyword color.\n * @example\n * color('silver'); // ✔\n * color('some-imaginary-number'); // ❌\n */\nexport const color = (color: ColorKeyword): ColorKeyword => color;\n\nexport type ColorKeyword = ColorKeywordLevel1 | ColorKeywordLevel2 | ColorKeywordLevel3 | ColorKeywordLevel4;\n\nexport type ColorKeywordLevel1 =\n\t| 'black'\n\t| 'silver'\n\t| 'gray'\n\t| 'white'\n\t| 'maroon'\n\t| 'red'\n\t| 'purple'\n\t| 'fuchsia'\n\t| 'green'\n\t| 'lime'\n\t| 'olive'\n\t| 'yellow'\n\t| 'navy'\n\t| 'blue'\n\t| 'teal'\n\t| 'aqua';\n\nexport type ColorKeywordLevel2 = 'orange';\n\nexport type ColorKeywordLevel3 =\n\t| 'aliceblue'\n\t| 'antiquewhite'\n\t| 'aquamarine'\n\t| 'azure'\n\t| 'beige'\n\t| 'bisque'\n\t| 'blanchedalmond'\n\t| 'blueviolet'\n\t| 'brown'\n\t| 'burlywood'\n\t| 'cadetblue'\n\t| 'chartreuse'\n\t| 'chocolate'\n\t| 'coral'\n\t| 'cornflowerblue'\n\t| 'cornsilk'\n\t| 'crimson'\n\t| 'cyan'\n\t| 'darkblue'\n\t| 'darkcyan'\n\t| 'darkgoldenrod'\n\t| 'darkgray'\n\t| 'darkgreen'\n\t| 'darkgrey'\n\t| 'darkkhaki'\n\t| 'darkmagenta'\n\t| 'darkolivegreen'\n\t| 'darkorange'\n\t| 'darkorchid'\n\t| 'darkred'\n\t| 'darksalmon'\n\t| 'darkseagreen'\n\t| 'darkslateblue'\n\t| 'darkslategray'\n\t| 'darkslategrey'\n\t| 'darkturquoise'\n\t| 'darkviolet'\n\t| 'deeppink'\n\t| 'deepskyblue'\n\t| 'dimgray'\n\t| 'dimgrey'\n\t| 'dodgerblue'\n\t| 'firebrick'\n\t| 'floralwhite'\n\t| 'forestgreen'\n\t| 'gainsboro'\n\t| 'ghostwhite'\n\t| 'gold'\n\t| 'goldenrod'\n\t| 'greenyellow'\n\t| 'grey'\n\t| 'honeydew'\n\t| 'hotpink'\n\t| 'indianred'\n\t| 'indigo'\n\t| 'ivory'\n\t| 'khaki'\n\t| 'lavender'\n\t| 'lavenderblush'\n\t| 'lawngreen'\n\t| 'lemonchiffon'\n\t| 'lightblue'\n\t| 'lightcoral'\n\t| 'lightcyan'\n\t| 'lightgoldenrodyellow'\n\t| 'lightgray'\n\t| 'lightgreen'\n\t| 'lightgrey'\n\t| 'lightpink'\n\t| 'lightsalmon'\n\t| 'lightseagreen'\n\t| 'lightskyblue'\n\t| 'lightslategray'\n\t| 'lightslategrey'\n\t| 'lightsteelblue'\n\t| 'lightyellow'\n\t| 'limegreen'\n\t| 'linen'\n\t| 'magenta'\n\t| 'mediumaquamarine'\n\t| 'mediumblue'\n\t| 'mediumorchid'\n\t| 'mediumpurple'\n\t| 'mediumseagreen'\n\t| 'mediumslateblue'\n\t| 'mediumspringgreen'\n\t| 'mediumturquoise'\n\t| 'mediumvioletred'\n\t| 'midnightblue'\n\t| 'mintcream'\n\t| 'mistyrose'\n\t| 'moccasin'\n\t| 'navajowhite'\n\t| 'oldlace'\n\t| 'olivedrab'\n\t| 'orangered'\n\t| 'orchid'\n\t| 'palegoldenrod'\n\t| 'palegreen'\n\t| 'paleturquoise'\n\t| 'palevioletred'\n\t| 'papayawhip'\n\t| 'peachpuff'\n\t| 'peru'\n\t| 'pink'\n\t| 'plum'\n\t| 'powderblue'\n\t| 'rosybrown'\n\t| 'royalblue'\n\t| 'saddlebrown'\n\t| 'salmon'\n\t| 'sandybrown'\n\t| 'seagreen'\n\t| 'seashell'\n\t| 'sienna'\n\t| 'skyblue'\n\t| 'slateblue'\n\t| 'slategray'\n\t| 'slategrey'\n\t| 'snow'\n\t| 'springgreen'\n\t| 'steelblue'\n\t| 'tan'\n\t| 'thistle'\n\t| 'tomato'\n\t| 'turquoise'\n\t| 'violet'\n\t| 'wheat'\n\t| 'whitesmoke'\n\t| 'yellowgreen';\n\nexport type ColorKeywordLevel4 = 'rebeccapurple';\n// End Section: Util\n"], "names": ["<PERSON><PERSON>", "constructor", "width", "height", "contextAttributes", "this", "canvas", "createCanvas", "context", "getContext", "value", "direction", "font", "globalAlpha", "imageSmoothingEnabled", "imageSmoothingQuality", "getContextAttributes", "transform", "getTransform", "textFontHeight", "getFontHeight", "lineDash", "getLineDash", "changeCanvasSize", "changeCanvasWidth", "changeCanvasHeight", "save", "restore", "rotate", "angle", "scale", "x", "y", "translate", "clip", "args", "setTransform", "resetTransform", "resetFilters", "setFilter", "getImageData", "putImageData", "fill", "printText", "text", "rest", "fillText", "printResponsiveText", "max<PERSON><PERSON><PERSON>", "tail", "lead", "parseFont", "measureText", "newHeight", "setTextFont", "printMultilineText", "lines", "split", "length", "linePositionY", "line", "Math", "floor", "printWrappedText", "wrapWidth", "wrappedText", "textWrap", "stroke", "printStrokeRectangle", "strokeRect", "printStrokeText", "strokeText", "setTextSize", "size", "result", "setStroke", "color", "strokeStyle", "setLineWidth", "lineWidth", "setStrokeWidth", "setLineDashOffset", "lineDashOffset", "setLineJoin", "lineJoin", "setLineCap", "lineCap", "setLineDash", "segments", "printImage", "image", "resolvable", "drawImage", "printCircularImage", "imageOrBuffer", "radius", "fit", "positionX", "positionY", "sizeX", "sizeY", "resolveCircularCoordinates", "createCircularClip", "PI", "printRoundedImage", "createRoundedClip", "printCircle", "createCircularPath", "printRectangle", "fillRect", "printRoundedRectangle", "createRoundedPath", "dx", "dy", "start", "antiClockwise", "beginPath", "arc", "createRectanglePath", "rect", "createRectangleClip", "radiusObject", "tl", "min", "tr", "br", "bl", "moveTo", "lineTo", "quadraticCurveTo", "closePath", "setColor", "fillStyle", "setTextAlign", "align", "textAlign", "setTextBaseline", "baseline", "textBaseline", "filter", "createPattern", "repetition", "printPattern", "createLinearGradient", "x0", "y0", "x1", "y1", "steps", "gradient", "step", "addColorStop", "position", "printLinearColorGradient", "printLinearStrokeGradient", "createRadialGradient", "r0", "r1", "printRadialColorGradient", "printRadialStrokeGradient", "createConicGradient", "startAngle", "printConicColorGradient", "printConicStrokeGradient", "createEllipsePath", "radiusX", "radiusY", "rotation", "endAngle", "anticlockwise", "ellipse", "createEllipseClip", "arcTo", "x2", "y2", "cpx", "cpy", "bezierCurveTo", "cp1x", "cp1y", "cp2x", "cp2y", "setShadowBlur", "<PERSON><PERSON><PERSON><PERSON>", "setShadowColor", "shadowColor", "setShadowOffsetX", "shadowOffsetX", "setShadowOffsetY", "shadowOffsetY", "setMiterLimit", "miterLimit", "setGlobalCompositeOperation", "type", "globalCompositeOperation", "setGlobalAlpha", "setImageSmoothingEnabled", "setImageSmoothingQuality", "resetShadows", "clearCircle", "clearRectangle", "clearRect", "isPointInPath", "isPointInStroke", "process", "fn", "call", "wrapText", "createImageData", "jpeg", "quality", "encodeSync", "jpegAsync", "encode", "webp", "webpAsync", "png", "pngAsync", "avif", "cfg", "avifAsync", "toDataURL", "toDataURLAsync", "fontRegExp", "exec", "slice", "index", "Number", "w", "h", "ratio", "diameter", "resolveImage", "deprecate", "loadImage", "loadFont", "fontOrPath", "alias", "GlobalFonts", "registerFromPath", "register", "loadFontsFromDirectory", "path", "loadFontsFromDir", "registerFont", "fontVariant", "join", "invert", "greyscale", "imageData", "data", "i", "luminance", "grayscale", "invertGrayscale", "invertGreyscale", "sepia", "r", "g", "b", "silhouette", "threshold", "invertedThreshold", "brightness", "darkness", "myOldFriend", "convolute", "weights", "opaque", "side", "round", "sqrt", "halfSide", "pixels", "src", "sw", "sh", "output", "dst", "alphaFac", "sy", "sx", "dstOff", "a", "cy", "cx", "scy", "scx", "srcOff", "wt", "edgeLaPlaceMatrix", "edge", "sharpenLaPlaceMatrix", "sharpen", "passes", "blurLaPlaceMatrix", "blur", "kCache", "Map", "previous", "get", "sizeFamily", "set", "buffer", "spaceWidth", "spaceLeft", "word", "wordWidth", "wordWidthWithSpace", "push", "name", "hex", "rgb", "red", "green", "blue", "rgba", "alpha", "hsl", "hue", "saturation", "lightness", "hsla"], "mappings": "2LAkEaA,EAiBZC,YAAmBC,EAAeC,EAAgBC,GACjDC,KAAKC,OAASC,EAAaL,EAAOC,GAClCE,KAAKG,QAAUH,KAAKC,OAAOG,WAAW,KAAML,EAC5C,CAKUF,YACV,OAAOG,KAAKC,OAAOJ,KACnB,CAEUA,UAAMQ,GAChBL,KAAKC,OAAOJ,MAAQQ,CACpB,CAKUP,aACV,OAAOE,KAAKC,OAAOH,MACnB,CAEUA,WAAOO,GACjBL,KAAKC,OAAOH,OAASO,CACrB,CAMUC,gBACV,OAAON,KAAKG,QAAQG,SACpB,CAOUC,WACV,OAAOP,KAAKG,QAAQI,IACpB,CAMUC,kBACV,OAAOR,KAAKG,QAAQK,WACpB,CAMUC,4BACV,OAAOT,KAAKG,QAAQM,qBACpB,CAMUC,4BACV,OAAOV,KAAKG,QAAQO,qBACpB,CAOUX,wBACV,OAAOC,KAAKG,QAAQQ,sBACpB,CAMUC,gBACV,OAAOZ,KAAKG,QAAQU,cACpB,CAKUC,qBACV,OAAOC,EAAcf,KAAKG,QAAQI,KAClC,CAgBUS,eACV,OAAOhB,KAAKG,QAAQc,aACpB,CAOMC,iBAAiBrB,EAAeC,GACtC,OAAOE,KAAKmB,kBAAkBtB,GAAOuB,mBAAmBtB,EACxD,CAMMqB,kBAAkBtB,GAExB,OADAG,KAAKH,MAAQA,EACNG,IACP,CAMMoB,mBAAmBtB,GAEzB,OADAE,KAAKF,OAASA,EACPE,IACP,CAMMqB,OAEN,OADArB,KAAKG,QAAQkB,OACNrB,IACP,CAOMsB,UAEN,OADAtB,KAAKG,QAAQmB,UACNtB,IACP,CAQMuB,OAAOC,GAEb,OADAxB,KAAKG,QAAQoB,OAAOC,GACbxB,IACP,CAQMyB,MAAMC,EAAWC,GAEvB,OADA3B,KAAKG,QAAQsB,MAAMC,EAAGC,GACf3B,IACP,CAQM4B,UAAUF,EAAWC,GAE3B,OADA3B,KAAKG,QAAQyB,UAAUF,EAAGC,GACnB3B,IACP,CAeM6B,QAAQC,GAEd,OADA9B,KAAKG,QAAQ0B,QAAQC,GACd9B,IACP,CAqBM+B,gBAAgBD,GAEtB,OADA9B,KAAKG,QAAQ4B,gBAAgBD,GACtB9B,IACP,CAMMgC,iBAEN,OADAhC,KAAKG,QAAQ6B,iBACNhC,IACP,CAKMiC,eACN,OAAOjC,KAAKkC,UAAU,OACtB,CAkBMC,aAAaT,EAAYC,EAAY9B,EAAgBC,GAC3D,OAAOE,KAAKG,QAAQgC,aAAaT,QAAAA,EAAK,EAAGC,QAAAA,EAAK,EAAG9B,QAAAA,EAASG,KAAKH,MAAOC,QAAAA,EAAUE,KAAKF,OACrF,CA0BMsC,gBAAgBN,GAEtB,OADA9B,KAAKG,QAAQiC,gBAAgBN,GACtB9B,IACP,CAeMqC,QAAQP,GAEd,OADA9B,KAAKG,QAAQkC,QAAQP,GACd9B,IACP,CAYMsC,UAAUC,EAAcb,EAAWC,KAAca,GAEvD,OADAxC,KAAKG,QAAQsC,SAASF,EAAMb,EAAGC,KAAMa,GAC9BxC,IACP,CAcM0C,oBAAoBH,EAAcb,EAAWC,EAAWgB,GAC9D,MAAOC,EAAM9C,EAAQ+C,GAAQ7C,KAAK8C,UAAU9C,KAAKG,QAAQI,MACzD,GAAsB,iBAAXT,EAAqB,OAAOE,KAAKsC,UAAUC,EAAMb,EAAGC,GAG/D,MAAM9B,MAAEA,GAAUG,KAAK+C,YAAYR,GACnC,GAAI1C,GAAS8C,EAAU,OAAO3C,KAAKsC,UAAUC,EAAMb,EAAGC,GAGtD,MAAMqB,EAAaL,EAAW9C,EAASC,EACvC,OAAOE,KAAKqB,OAAO4B,YAAY,GAAGL,IAAOI,IAAYH,KAAQP,UAAUC,EAAMb,EAAGC,GAAGL,SACnF,CAaM4B,mBAAmBX,EAAcb,EAAWC,GAClD,MAAMwB,EAAQZ,EAAKa,MAAM,SAGzB,GAAID,EAAME,QAAU,EAAG,OAAOrD,KAAKsC,UAAUC,EAAMb,EAAGC,GAEtD,MAAM7B,EAASE,KAAKc,eAEpB,IAAIwC,EAAgB3B,EACpB,IAAK,MAAM4B,KAAQJ,EAClBnD,KAAKsC,UAAUiB,EAAM7B,EAAG8B,KAAKC,MAAMH,IACnCA,GAAiBxD,EAGlB,OAAOE,IACP,CAcM0D,iBAAiBnB,EAAcb,EAAWC,EAAWgC,GAC3D,MAAMC,EAAcC,EAAS7D,KAAMuC,EAAMoB,GACzC,OAAO3D,KAAKkD,mBAAmBU,EAAalC,EAAGC,EAC/C,CAMMmC,SAEN,OADA9D,KAAKG,QAAQ2D,SACN9D,IACP,CAWM+D,qBAAqBrC,EAAWC,EAAW9B,EAAeC,GAEhE,OADAE,KAAKG,QAAQ6D,WAAWtC,EAAGC,EAAG9B,EAAOC,GAC9BE,IACP,CAWMiE,gBAAgB1B,EAAcb,EAAWC,EAAWgB,GAE1D,OADA3C,KAAKG,QAAQ+D,WAAW3B,EAAMb,EAAGC,EAAGgB,GAC7B3C,IACP,CA4BM+C,YAAYR,GAClB,OAAOvC,KAAKG,QAAQ4C,YAAYR,EAChC,CAMM4B,YAAYC,GAClB,MAAMC,EAASrE,KAAK8C,UAAU9C,KAAKG,QAAQI,MAC3C,OAAyB,IAAlB8D,EAAOhB,OAAerD,KAAOA,KAAKiD,YAAY,GAAGoB,EAAO,KAAKD,IAAOC,EAAO,KAClF,CAOMC,UAAUC,GAEhB,OADAvE,KAAKG,QAAQqE,YAAcD,EACpBvE,IACP,CAOMyE,aAAa5E,GAEnB,OADAG,KAAKG,QAAQuE,UAAY7E,EAClBG,IACP,CAEM2E,eAAe9E,GACrB,OAAOG,KAAKyE,aAAa5E,EACzB,CAOM+E,kBAAkBvE,GAExB,OADAL,KAAKG,QAAQ0E,eAAiBxE,EACvBL,IACP,CASM8E,YAAYzE,GAElB,OADAL,KAAKG,QAAQ4E,SAAW1E,EACjBL,IACP,CAQMgF,WAAW3E,GAEjB,OADAL,KAAKG,QAAQ8E,QAAU5E,EAChBL,IACP,CAWMkF,YAAYC,GAElB,OADAnF,KAAKG,QAAQ+E,YAAYC,GAClBnF,IACP,CAkCMoF,WAAWC,KAA2BvD,GAlmB9C,IAAuBwD,EAomBrB,OADAtF,KAAKG,QAAQoF,WAnmBQD,EAmmBgBD,aAlmBT1F,EAAS2F,EAAWrF,OAASqF,KAkmBTxD,GACzC9B,IACP,CAYMwF,mBACNC,EACA/D,EACAC,EACA+D,GACAC,IAAEA,EAAM,QAAiC,IAEzC,MAAMC,UAAEA,EAASC,UAAEA,EAASC,MAAEA,EAAKC,MAAEA,GAAU/F,KAAKgG,2BAA2BP,EAAe/D,EAAGC,EAAG+D,EAAQC,GAC5G,OAAO3F,KAAKqB,OACV4E,mBAAmBvE,EAAGC,EAAG+D,EAAQ,EAAa,EAAVlC,KAAK0C,IAAQ,GACjDd,WAAWK,EAAeG,EAAWC,EAAWC,EAAOC,GACvDzE,SACF,CAoBM6E,kBACNV,EACA/D,EACAC,EACA9B,EACAC,EACA4F,GAEA,OAAO1F,KAAKqB,OAAO+E,kBAAkB1E,EAAGC,EAAG9B,EAAOC,EAAQ4F,GAAQN,WAAWK,EAAe/D,EAAGC,EAAG9B,EAAOC,GAAQwB,SACjH,CAQM+E,YAAY3E,EAAWC,EAAW+D,GACxC,OAAO1F,KAAKqB,OAAOiF,mBAAmB5E,EAAGC,EAAG+D,GAAQrD,OAAOf,SAC3D,CAUMiF,eAAe7E,EAAWC,EAAW9B,EAAeC,GAE1D,OADAE,KAAKG,QAAQqG,SAAS9E,EAAGC,EAAG9B,EAAOC,GAC5BE,IACP,CAqCMyG,sBAAsB/E,EAAWC,EAAW9B,EAAeC,EAAgB4F,GACjF,OAAO1F,KAAKqB,OAAOqF,kBAAkBhF,EAAGC,EAAG9B,EAAOC,EAAQ4F,GAAQrD,OAAOf,SACzE,CAWMgF,mBAAmBK,EAAYC,EAAYlB,EAAgBmB,EAAQ,EAAGrF,EAAkB,EAAVgC,KAAK0C,GAAQY,GAAgB,GAGjH,OAFA9G,KAAKG,QAAQ4G,YACb/G,KAAKG,QAAQ6G,IAAIL,EAAIC,EAAIlB,EAAQmB,EAAOrF,EAAOsF,GACxC9G,IACP,CAYMiG,mBAAmBU,EAAYC,EAAYlB,EAAgBmB,EAAgBrF,EAAgBsF,GACjG,OAAO9G,KAAKsG,mBAAmBK,EAAIC,EAAIlB,EAAQmB,EAAOrF,EAAOsF,GAAejF,MAC5E,CASMoF,oBAAoBvF,EAAWC,EAAW9B,EAAeC,GAE/D,OADAE,KAAKG,QAAQ+G,KAAKxF,EAAGC,EAAG9B,EAAOC,GACxBE,IACP,CASMmH,oBAAoBzF,EAAWC,EAAW9B,EAAeC,GAC/D,OAAOE,KAAKiH,oBAAoBvF,EAAGC,EAAG9B,EAAOC,GAAQ+B,MACrD,CAUM6E,kBAAkBhF,EAAWC,EAAW9B,EAAeC,EAAgB4F,GAC7E,GAAI7F,EAAQ,GAAKC,EAAS,EAAG,CAC5B,IAAIsH,EACkB,iBAAX1B,EAEV0B,EAAe,CAAEC,GADjB3B,EAASlC,KAAK8D,IAAI5B,EAAQ7F,EAAQ,EAAGC,EAAS,GACjByH,GAAI7B,EAAQ8B,GAAI9B,EAAQ+B,GAAI/B,IAEzD0B,EAAe1B,EACfA,EAASlC,KAAK8D,IAAI,EAAGzH,EAAQ,EAAGC,EAAS,IAE1C,MAAMuH,GAAEA,EAAK3B,EAAM6B,GAAEA,EAAK7B,EAAM8B,GAAEA,EAAK9B,EAAM+B,GAAEA,EAAK/B,GAAW0B,EAC/DpH,KAAKG,QAAQ4G,YACb/G,KAAKG,QAAQuH,OAAOhG,EAAI2F,EAAI1F,GAC5B3B,KAAKG,QAAQwH,OAAOjG,EAAI7B,EAAQ0H,EAAI5F,GACpC3B,KAAKG,QAAQyH,iBAAiBlG,EAAI7B,EAAO8B,EAAGD,EAAI7B,EAAO8B,EAAI4F,GAC3DvH,KAAKG,QAAQwH,OAAOjG,EAAI7B,EAAO8B,EAAI7B,EAAS0H,GAC5CxH,KAAKG,QAAQyH,iBAAiBlG,EAAI7B,EAAO8B,EAAI7B,EAAQ4B,EAAI7B,EAAQ2H,EAAI7F,EAAI7B,GACzEE,KAAKG,QAAQwH,OAAOjG,EAAI+F,EAAI9F,EAAI7B,GAChCE,KAAKG,QAAQyH,iBAAiBlG,EAAGC,EAAI7B,EAAQ4B,EAAGC,EAAI7B,EAAS2H,GAC7DzH,KAAKG,QAAQwH,OAAOjG,EAAGC,EAAI0F,GAC3BrH,KAAKG,QAAQyH,iBAAiBlG,EAAGC,EAAGD,EAAI2F,EAAI1F,GAC5C3B,KAAKG,QAAQ0H,WACb,CACD,OAAO7H,IACP,CAwCMoG,kBAAkB1E,EAAWC,EAAW9B,EAAeC,EAAgB4F,GAC7E,OAAO1F,KAAK0G,kBAAkBhF,EAAGC,EAAG9B,EAAOC,EAAQ4F,GAAQ7D,MAC3D,CAOMiG,SAASvD,GAEf,OADAvE,KAAKG,QAAQ4H,UAAYxD,EAClBvE,IACP,CAOMiD,YAAY1C,GAElB,OADAP,KAAKG,QAAQI,KAAOA,EACbP,IACP,CAOMgI,aAAaC,GAEnB,OADAjI,KAAKG,QAAQ+H,UAAYD,EAClBjI,IACP,CAOMmI,gBAAgBC,GAEtB,OADApI,KAAKG,QAAQkI,aAAeD,EACrBpI,IACP,CAOMkC,UAAUoG,GAEhB,OADAtI,KAAKG,QAAQmI,OAASA,EACftI,IACP,CAMM+G,YAEN,OADA/G,KAAKG,QAAQ4G,YACN/G,IACP,CAOM6H,YAEN,OADA7H,KAAKG,QAAQ0H,YACN7H,IACP,CASMuI,cAAclD,EAA0BmD,GAC9C,OAAOxI,KAAKG,QAAQoI,eAl6BGjD,EAk6B2BD,aAj6BzB1F,EAAe2F,EAAWnD,eAC7CmD,EAg6BoDkD,GAl6B5D,IAAyBlD,CAm6BvB,CASMmD,aAAapD,EAA0BmD,GAC7C,OAAOxI,KAAK8H,SAAS9H,KAAKuI,cAAclD,EAAOmD,GAC/C,CAYME,qBAAqBC,EAAYC,EAAYC,EAAYC,EAAYC,EAAiC,IAC5G,MAAMC,EAAWhJ,KAAKG,QAAQuI,qBAAqBC,EAAIC,EAAIC,EAAIC,GAC/D,IAAK,MAAMG,KAAQF,EAClBC,EAASE,aAAaD,EAAKE,SAAUF,EAAK1E,OAG3C,OAAOyE,CACP,CAqBMI,yBAAyBT,EAAYC,EAAYC,EAAYC,EAAYC,GAC/E,MAAMC,EAAWhJ,KAAK0I,qBAAqBC,EAAIC,EAAIC,EAAIC,EAAIC,GAC3D,OAAO/I,KAAK8H,SAASkB,EACrB,CAqBMK,0BAA0BV,EAAYC,EAAYC,EAAYC,EAAYC,GAChF,MAAMC,EAAWhJ,KAAK0I,qBAAqBC,EAAIC,EAAIC,EAAIC,EAAIC,GAC3D,OAAO/I,KAAKsE,UAAU0E,EACtB,CAaMM,qBACNX,EACAC,EACAW,EACAV,EACAC,EACAU,EACAT,EAAiC,IAEjC,MAAMC,EAAWhJ,KAAKG,QAAQmJ,qBAAqBX,EAAIC,EAAIW,EAAIV,EAAIC,EAAIU,GACvE,IAAK,MAAMP,KAAQF,EAClBC,EAASE,aAAaD,EAAKE,SAAUF,EAAK1E,OAG3C,OAAOyE,CACP,CAcMS,yBAAyBd,EAAYC,EAAYW,EAAYV,EAAYC,EAAYU,EAAYT,GACvG,MAAMC,EAAWhJ,KAAKsJ,qBAAqBX,EAAIC,EAAIW,EAAIV,EAAIC,EAAIU,EAAIT,GACnE,OAAO/I,KAAK8H,SAASkB,EACrB,CAcMU,0BAA0Bf,EAAYC,EAAYW,EAAYV,EAAYC,EAAYU,EAAYT,GACxG,MAAMC,EAAWhJ,KAAKsJ,qBAAqBX,EAAIC,EAAIW,EAAIV,EAAIC,EAAIU,EAAIT,GACnE,OAAO/I,KAAKsE,UAAU0E,EACtB,CAWMW,oBAAoBC,EAAoBlI,EAAWC,EAAWoH,EAAiC,IACrG,MAAMC,EAAWhJ,KAAKG,QAAQwJ,oBAAoBC,EAAYlI,EAAGC,GACjE,IAAK,MAAMsH,KAAQF,EAClBC,EAASE,aAAaD,EAAKE,SAAUF,EAAK1E,OAG3C,OAAOyE,CACP,CAYMa,wBAAwBD,EAAoBlI,EAAWC,EAAWoH,GACxE,MAAMC,EAAWhJ,KAAK2J,oBAAoBC,EAAYlI,EAAGC,EAAGoH,GAC5D,OAAO/I,KAAK8H,SAASkB,EACrB,CAYMc,yBAAyBF,EAAoBlI,EAAWC,EAAWoH,GACzE,MAAMC,EAAWhJ,KAAK2J,oBAAoBC,EAAYlI,EAAGC,EAAGoH,GAC5D,OAAO/I,KAAKsE,UAAU0E,EACtB,CAeMe,kBACNrI,EACAC,EACAqI,EACAC,EACAC,EACAN,EACAO,EACAC,GAGA,OADApK,KAAKG,QAAQkK,QAAQ3I,EAAGC,EAAGqI,EAASC,EAASC,EAAUN,EAAYO,EAAUC,GACtEpK,IACP,CAeMsK,kBACN5I,EACAC,EACAqI,EACAC,EACAC,EACAN,EACAO,EACAC,GAEA,OAAOpK,KAAK+J,kBAAkBrI,EAAGC,EAAGqI,EAASC,EAASC,EAAUN,EAAYO,EAAUC,GAAevI,MACrG,CAaMmF,IAAItF,EAAWC,EAAW+D,EAAgBkE,EAAoBO,EAAkBC,GAEtF,OADApK,KAAKG,QAAQ6G,IAAItF,EAAGC,EAAG+D,EAAQkE,EAAYO,EAAUC,GAC9CpK,IACP,CAWMuK,MAAM1B,EAAYC,EAAY0B,EAAYC,EAAY/E,GAE5D,OADA1F,KAAKG,QAAQoK,MAAM1B,EAAIC,EAAI0B,EAAIC,EAAI/E,GAC5B1F,IACP,CAYM4H,iBAAiB8C,EAAaC,EAAajJ,EAAWC,GAE5D,OADA3B,KAAKG,QAAQyH,iBAAiB8C,EAAKC,EAAKjJ,EAAGC,GACpC3B,IACP,CAcM4K,cAAcC,EAAcC,EAAcC,EAAcC,EAActJ,EAAWC,GAEvF,OADA3B,KAAKG,QAAQyK,cAAcC,EAAMC,EAAMC,EAAMC,EAAMtJ,EAAGC,GAC/C3B,IACP,CAQM2H,OAAOjG,EAAWC,GAExB,OADA3B,KAAKG,QAAQwH,OAAOjG,EAAGC,GAChB3B,IACP,CAQM0H,OAAOhG,EAAWC,GAExB,OADA3B,KAAKG,QAAQuH,OAAOhG,EAAGC,GAChB3B,IACP,CAOMiL,cAAcvF,GAEpB,OADA1F,KAAKG,QAAQ+K,WAAaxF,EACnB1F,IACP,CAOMmL,eAAe5G,GAErB,OADAvE,KAAKG,QAAQiL,YAAc7G,EACpBvE,IACP,CAOMqL,iBAAiBhL,GAEvB,OADAL,KAAKG,QAAQmL,cAAgBjL,EACtBL,IACP,CAOMuL,iBAAiBlL,GAEvB,OADAL,KAAKG,QAAQqL,cAAgBnL,EACtBL,IACP,CASMyL,cAAcpL,GAEpB,OADAL,KAAKG,QAAQuL,WAAarL,EACnBL,IACP,CAQM2L,4BAA4BC,GAElC,OADA5L,KAAKG,QAAQ0L,yBAA2BD,EACjC5L,IACP,CAOM8L,eAAezL,GAErB,OADAL,KAAKG,QAAQK,YAAcH,EACpBL,IACP,CAOM+L,yBAAyB1L,GAE/B,OADAL,KAAKG,QAAQM,sBAAwBJ,EAC9BL,IACP,CAOMgM,yBAAyB3L,GAE/B,OADAL,KAAKG,QAAQO,sBAAwBL,EAC9BL,IACP,CAkBMiM,eACN,OAAOjM,KAAKiL,cAAc,GAAGI,iBAAiB,GAAGE,iBAAiB,GAAGJ,eAAe,cACpF,CAWMe,YAAYxK,EAAWC,EAAW+D,EAAgBmB,EAAQ,EAAGrF,EAAkB,EAAVgC,KAAK0C,GAAQY,GAAgB,GACxG,OAAO9G,KAAKiG,mBAAmBvE,EAAGC,EAAG+D,EAAQmB,EAAOrF,EAAOsF,GAAeqF,eAAezK,EAAIgE,EAAQ/D,EAAI+D,EAAiB,EAATA,EAAqB,EAATA,EAC7H,CAUMyG,eAAexF,EAAK,EAAGC,EAAK,EAAG/G,EAAQG,KAAKH,MAAOC,EAASE,KAAKF,QAEvE,OADAE,KAAKG,QAAQiM,UAAUzF,EAAIC,EAAI/G,EAAOC,GAC/BE,IACP,CAmBMqM,iBAAiBvK,GACvB,OAAO9B,KAAKG,QAAQkM,iBAAiBvK,EACrC,CAiBMwK,mBAAmBxK,GACzB,OAAO9B,KAAKG,QAAQmM,mBAAmBxK,EACvC,CAOMyK,QAAqCC,KAA6D1K,GAExG,OADA0K,EAAGC,KAAKzM,KAAMA,QAAS8B,GAChB9B,IACP,CAyBM0M,SAASnK,EAAcoB,GAC7B,OAAOE,EAAS7D,KAAMuC,EAAMoB,EAC5B,CAcMgJ,mBAAmB7K,GACzB,OAAO9B,KAAKG,QAAQwM,mBAAmB7K,EACvC,CAQM8K,KAAKC,GACX,OAAO7M,KAAKC,OAAO6M,WAAW,OAAQD,EACtC,CAQME,UAAUF,GAChB,OAAO7M,KAAKC,OAAO+M,OAAO,OAAQH,EAClC,CAQMI,KAAKJ,GACX,OAAO7M,KAAKC,OAAO6M,WAAW,OAAQD,EACtC,CAQMK,UAAUL,GAChB,OAAO7M,KAAKC,OAAO+M,OAAO,OAAQH,EAClC,CAOMM,MACN,OAAOnN,KAAKC,OAAO6M,WAAW,MAC9B,CAOMM,WACN,OAAOpN,KAAKC,OAAO+M,OAAO,MAC1B,CAOMK,KAAKC,GACX,OAAOtN,KAAKC,OAAO6M,WAAW,OAAQQ,EACtC,CAOMC,UAAUD,GAChB,OAAOtN,KAAKC,OAAO+M,OAAO,OAAQM,EAClC,CAMME,aAAa1L,GACnB,OAAO9B,KAAKC,OAAOuN,aAAa1L,EAChC,CAMM2L,kBAAkB3L,GACxB,OAAO9B,KAAKC,OAAOwN,kBAAkB3L,EACrC,CAESgB,UAAUvC,GACnB,MAAM8D,EAASqJ,EAAWC,KAAKpN,GAC/B,OAAe,OAAX8D,EAAwB,CAAC9D,GAEtB,CAACA,EAAKqN,MAAM,EAAGvJ,EAAOwJ,OAAQC,OAAOzJ,EAAO,IAAK9D,EAAKqN,MAAMvJ,EAAOwJ,MAAQxJ,EAAO,GAAGhB,QAC5F,CAES2C,2BACTP,EACA/D,EACAC,EACA+D,EACAC,GAEA,MAAQ9F,MAAOkO,EAAGjO,OAAQkO,GAAMvI,EAChC,GAAY,SAARE,EACH,MAAO,CACNC,UAAWlE,EAAIqM,EAAI,EACnBlI,UAAWlE,EAAIqM,EAAI,EACnBlI,MAAOiI,EACPhI,MAAOiI,GAIT,MAAMC,EAAQF,EAAIC,EACZE,EAAoB,EAATxI,EAEjB,GAAY,SAARC,GAA4B,IAAVsI,EACrB,MAAO,CACNrI,UAAWlE,EAAIgE,EACfG,UAAWlE,EAAI+D,EACfI,MAAOoI,EACPnI,MAAOmI,GAIT,GAAY,YAARvI,EACH,OAAOsI,EAAQ,EACZ,CACArI,UAAWlE,EAAIgE,EACfG,UAAWlE,EAAI+D,EAASuI,EACxBnI,MAAOoI,EACPnI,MAAOmI,EAAWD,GAElB,CACArI,UAAWlE,EAAIgE,EAASuI,EACxBpI,UAAWlE,EAAI+D,EACfI,MAAOoI,EAAWD,EAClBlI,MAAOmI,GAIX,GAAID,EAAQ,EAAG,CACd,MAAMnI,EAAQoI,EAAWD,EAEzB,MAAO,CACNrI,UAAWlE,EAAIoE,EAAQ,EACvBD,UAAWlE,EAHEuM,EAGU,EACvBpI,QACAC,MALamI,EAOd,CAED,MACMnI,EAAQmI,EAAWD,EACzB,MAAO,CACNrI,UAAWlE,EAHEwM,EAGU,EACvBrI,UAAWlE,EAAIoE,EAAQ,EACvBD,MALaoI,EAMbnI,QAED,EAYW,MAAAoI,EAAeC,EAAUC,EAAW,0DAIjC,SAAAC,EAASC,EAA6BC,GACrD,MAA6B,iBAAfD,EAA0BE,EAAYC,iBAAiBH,EAAYC,GAASC,EAAYE,SAASJ,EAAYC,EAC5H,CAEM,SAAUI,EAAuBC,GACtC,OAAOJ,EAAYK,iBAAiBD,EACrC,CAEa,MAAAE,EAAeX,EAAUE,EAAU,yDA6FhC,SAAAU,KAAelN,GAC9B,OAAOA,EAAKmN,KAAK,IAClB,CAOa,MAAAC,EAAUjP,GACtBA,EAAOoB,OAAOsK,4BAA4B,cAAc7D,SAAS,SAASvB,eAAe,EAAG,EAAGtG,EAAOJ,MAAOI,EAAOH,QAAQwB,UAMhH6N,EAAalP,IACzB,MAAMmP,EAAYnP,EAAOkC,gBACnBkN,KAAEA,GAASD,EACjB,IAAK,IAAIE,EAAI,EAAGA,EAAID,EAAKhM,OAAQiM,GAAK,EAAG,CACxC,MAAMC,EAAY,MAASF,EAAKC,GAAK,MAASD,EAAKC,EAAI,GAAK,MAASD,EAAKC,EAAI,GAC9ED,EAAKC,GAAKC,EACVF,EAAKC,EAAI,GAAKC,EACdF,EAAKC,EAAI,GAAKC,CACd,CAED,OAAOtP,EAAOmC,aAAagN,EAAW,EAAG,EAAE,EAE/BI,EAAYL,EAMZM,EAAmBxP,IAC/B,MAAMmP,EAAYnP,EAAOkC,gBACnBkN,KAAEA,GAASD,EACjB,IAAK,IAAIE,EAAI,EAAGA,EAAID,EAAKhM,OAAQiM,GAAK,EAAG,CACxC,MAAMC,EAAY,KAAO,MAASF,EAAKC,GAAK,MAASD,EAAKC,EAAI,GAAK,MAASD,EAAKC,EAAI,IACrFD,EAAKC,GAAKC,EACVF,EAAKC,EAAI,GAAKC,EACdF,EAAKC,EAAI,GAAKC,CACd,CAED,OAAOtP,EAAOmC,aAAagN,EAAW,EAAG,EAAE,EAE/BM,EAAkBD,EAMlBE,EAAS1P,IACrB,MAAMmP,EAAYnP,EAAOkC,gBACnBkN,KAAEA,GAASD,EACjB,IAAK,IAAIE,EAAI,EAAGA,EAAID,EAAKhM,OAAQiM,GAAK,EAAG,CACxC,MAAMM,EAAIP,EAAKC,GACTO,EAAIR,EAAKC,EAAI,GACbQ,EAAIT,EAAKC,EAAI,GACnBD,EAAKC,GAAS,KAAJM,EAAgB,KAAJC,EAAgB,KAAJC,EAClCT,EAAKC,EAAI,GAAS,KAAJM,EAAgB,KAAJC,EAAgB,KAAJC,EACtCT,EAAKC,EAAI,GAAS,KAAJM,EAAgB,KAAJC,EAAgB,KAAJC,CACtC,CACD,OAAO7P,EAAOmC,aAAagN,EAAW,EAAG,EAAE,EAO/BW,EAAc9P,IAC1B,MAAMmP,EAAYnP,EAAOkC,gBACnBkN,KAAEA,GAASD,EACjB,IAAK,IAAIE,EAAI,EAAGA,EAAID,EAAKhM,OAAQiM,GAAK,EACrCD,EAAKC,GAAK,EACVD,EAAKC,EAAI,GAAK,EACdD,EAAKC,EAAI,GAAK,EAGf,OAAOrP,EAAOmC,aAAagN,EAAW,EAAG,EAAE,EAQ/BY,EAAY,CAAC/P,EAAgB+P,KACzC,MAAMZ,EAAYnP,EAAOkC,gBACnBkN,KAAEA,GAASD,EACjB,IAAK,IAAIE,EAAI,EAAGA,EAAID,EAAKhM,OAAQiM,GAAK,EAAG,CACxC,MAAMC,EAAY,MAASF,EAAKC,GAAK,MAASD,EAAKC,EAAI,GAAK,MAASD,EAAKC,EAAI,IAAMU,EAAY,IAAM,EACtGX,EAAKC,GAAKC,EACVF,EAAKC,EAAI,GAAKC,EACdF,EAAKC,EAAI,GAAKC,CACd,CAED,OAAOtP,EAAOmC,aAAagN,EAAW,EAAG,EAAE,EAQ/Ba,EAAoB,CAAChQ,EAAgB+P,KACjD,MAAMZ,EAAYnP,EAAOkC,gBACnBkN,KAAEA,GAASD,EACjB,IAAK,IAAIE,EAAI,EAAGA,EAAID,EAAKhM,OAAQiM,GAAK,EAAG,CACxC,MAAMC,EAAY,MAASF,EAAKC,GAAK,MAASD,EAAKC,EAAI,GAAK,MAASD,EAAKC,EAAI,IAAMU,EAAY,EAAI,IACpGX,EAAKC,GAAKC,EACVF,EAAKC,EAAI,GAAKC,EACdF,EAAKC,EAAI,GAAKC,CACd,CAED,OAAOtP,EAAOmC,aAAagN,EAAW,EAAG,EAAE,EAQ/Bc,EAAa,CAACjQ,EAAgBiQ,KAC1C,MAAMd,EAAYnP,EAAOkC,gBACnBkN,KAAEA,GAASD,EACjB,IAAK,IAAIE,EAAI,EAAGA,EAAID,EAAKhM,OAAQiM,GAAK,EACrCD,EAAKC,IAAMY,EACXb,EAAKC,EAAI,IAAMY,EACfb,EAAKC,EAAI,IAAMY,EAGhB,OAAOjQ,EAAOmC,aAAagN,EAAW,EAAG,EAAE,EAQ/Be,EAAW,CAAClQ,EAAgBkQ,KACxC,MAAMf,EAAYnP,EAAOkC,gBACnBkN,KAAEA,GAASD,EACjB,IAAK,IAAIE,EAAI,EAAGA,EAAID,EAAKhM,OAAQiM,GAAK,EACrCD,EAAKC,IAAMa,EACXd,EAAKC,EAAI,IAAMa,EACfd,EAAKC,EAAI,IAAMa,EAGhB,OAAOlQ,EAAOmC,aAAagN,EAAW,EAAG,EAAE,EAE/BgB,EAAcD,EASdE,EAAY,CAACpQ,EAAgBqQ,EAA4BC,GAAS,KAC9E,MAAMC,EAAOhN,KAAKiN,MAAMjN,KAAKkN,KAAKJ,EAAQjN,SACpCsN,EAAWnN,KAAKC,MAAM+M,EAAO,GAE7BI,EAAS3Q,EAAOkC,eAChB0O,EAAMD,EAAOvB,KACbyB,EAAKF,EAAO/Q,MACZkR,EAAKH,EAAO9Q,OAGZiO,EAAI+C,EACJ9C,EAAI+C,EACJC,EAAS/Q,EAAOkC,eAChB8O,EAAMD,EAAO3B,KAGb6B,EAAWX,EAAS,EAAI,EAC9B,IAAK,IAAI5O,EAAI,EAAGA,EAAIqM,EAAGrM,IACtB,IAAK,IAAID,EAAI,EAAGA,EAAIqM,EAAGrM,IAAK,CAC3B,MAAMyP,EAAKxP,EACLyP,EAAK1P,EACL2P,EAAuB,GAAb1P,EAAIoM,EAAIrM,GAGxB,IAAIkO,EAAI,EACJC,EAAI,EACJC,EAAI,EACJwB,EAAI,EACR,IAAK,IAAIC,EAAK,EAAGA,EAAKf,EAAMe,IAC3B,IAAK,IAAIC,EAAK,EAAGA,EAAKhB,EAAMgB,IAAM,CACjC,MAAMC,EAAMN,EAAKI,EAAKZ,EAChBe,EAAMN,EAAKI,EAAKb,EACtB,GAAIc,GAAO,GAAKA,EAAMV,GAAMW,GAAO,GAAKA,EAAMZ,EAAI,CACjD,MAAMa,EAA4B,GAAlBF,EAAMX,EAAKY,GACrBE,EAAKtB,EAAQiB,EAAKf,EAAOgB,GAC/B5B,GAAKiB,EAAIc,GAAUC,EACnB/B,GAAKgB,EAAIc,EAAS,GAAKC,EACvB9B,GAAKe,EAAIc,EAAS,GAAKC,EACvBN,GAAKT,EAAIc,EAAS,GAAKC,CACvB,CACD,CAEFX,EAAII,GAAUzB,EACdqB,EAAII,EAAS,GAAKxB,EAClBoB,EAAII,EAAS,GAAKvB,EAClBmB,EAAII,EAAS,GAAKC,EAAIJ,GAAY,IAAMI,EACxC,CAGF,OAAOrR,EAAOmC,aAAa4O,EAAQ,EAAG,EAAE,EAOnCa,EAAoB,CAAC,GAAI,EAAG,GAAI,EAAG,GAAI,EAAG,GAAI,EAAG,GAM1CC,EAAQ7R,GAA2BoQ,EAAUpQ,EAAQ4R,GAAmB,GAM/EE,EAAuB,CAAC,GAAI,EAAG,GAAI,EAAG,GAAI,EAAG,GAAI,EAAG,GAO7CC,EAAU,CAAC/R,EAAgBgS,EAAS,KAChD,IAAK,IAAI3C,EAAI,EAAGA,EAAI2C,IAAU3C,EAC7Be,EAAUpQ,EAAQ8R,GAAsB,GAGzC,OAAO9R,CAAM,EAORiS,EAAoB,CAAC,EAAI,EAAG,EAAI,EAAG,EAAI,EAAG,EAAI,EAAG,EAAI,EAAG,EAAI,EAAG,EAAI,EAAG,EAAI,EAAG,EAAI,GAO1EC,EAAO,CAAClS,EAAgBgS,EAAS,KAC7C,IAAK,IAAI3C,EAAI,EAAGA,EAAI2C,IAAU3C,EAC7Be,EAAUpQ,EAAQiS,GAAmB,GAGtC,OAAOjS,CAAM,EAIDyN,EAAa,gDACb3M,EAAgB,MAC5B,MAAMqR,EAAS,IAAIC,IAEnB,OAAQ9R,IAEP,MAAM+R,EAAWF,EAAOG,IAAIhS,GAC5B,GAAI+R,EAAU,OAAOA,EAGrB,MAAME,EAAa9E,EAAWC,KAAKpN,GACnC,IAAKiS,EAAY,OAAO,EAExB,IAAIpO,EAAO0J,OAAO0E,EAAW,IAG7B,OAFaA,EAAW,IAGvB,IAAK,KACJpO,GAAQ,IACR,MACD,IAAK,KACJA,GAAQ,GACR,MACD,IAAK,KACJA,GAAQ,GACR,MACD,IAAK,KACJA,GAAQ,GAAO,KACf,MACD,IAAK,KACJA,GAAQ,GAAO,KACf,MACD,IAAK,KACL,IAAK,MACJA,GAAQ,GAAK,IACb,MACD,IAAK,IACJA,GAAQ,GAAK,KAAO,EAKtB,OADAgO,EAAOK,IAAIlS,EAAM6D,GACVA,CAAI,CAEZ,EA3C4B,GA6ChBP,EAAW,CAAC5D,EAAgBsC,EAAcoB,KACtD,MAAMU,EAAS,GACTqO,EAAS,GAETC,EAAa1S,EAAO8C,YAAY,KAAKlD,MAG3C,IAAK,MAAM0D,KAAQhB,EAAKa,MAAM,SAAU,CACvC,IAAIwP,EAAYjP,EAGhB,IAAK,MAAMkP,KAAQtP,EAAKH,MAAM,KAAM,CACnC,MAAM0P,EAAY7S,EAAO8C,YAAY8P,GAAMhT,MAErCkT,EAAqBD,EAAYH,EAEnCI,EAAqBH,GACpBF,EAAOrP,SACVgB,EAAO2O,KAAKN,EAAOzD,KAAK,MACxByD,EAAOrP,OAAS,GAEjBqP,EAAOM,KAAKH,GACZD,EAAYjP,EAAYmP,IAExBF,GAAaG,EACbL,EAAOM,KAAKH,GAEb,CAEGH,EAAOrP,SACVgB,EAAO2O,KAAKN,EAAOzD,KAAK,MACxByD,EAAOrP,OAAS,EAEjB,CAED,OAAOgB,EAAO4K,KAAK,KAAK,EAwDZ3G,EAAiB,CAAC2K,KAAiBnR,IAAyB,GAAGmR,KAAQnR,EAAKmN,KAAK,QAcjFiE,EAAyBA,GAAgC,IAAIA,IAgB7DC,EAAM,CAAuDC,EAAQC,EAAUC,IAC3F,OAAOF,MAAQC,MAAUC,KAsBbC,EAAO,CACnBH,EACAC,EACAC,EACAE,IAC2B,QAAQJ,MAAQC,MAAUC,MAASE,KAgBlDC,EAAM,CAAuDC,EAAQC,EAAeC,IAChG,OAAOF,MAAQC,OAAgBC,MAsBnBC,EAAO,CACnBH,EACAC,EACAC,EACAJ,IAC2B,QAAQE,MAAQC,OAAgBC,OAAeJ,KAS9DjP,EAASA,GAAsCA"}