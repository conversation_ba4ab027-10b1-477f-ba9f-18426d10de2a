import{deprecate as t}from"node:util";import{loadImage as e,registerFont as r,createCanvas as n}from"canvas";export{Image,registerFont as loadFont,loadImage}from"canvas";class i{constructor(t,e,r){this.canvas=n(t,e,r),this.context=this.canvas.getContext("2d")}addPage(...t){return this.context.addPage(...t),this}get width(){return this.canvas.width}set width(t){this.canvas.width=t}get height(){return this.canvas.height}set height(t){this.canvas.height=t}get font(){return this.context.font}get globalAlpha(){return this.context.globalAlpha}get antialias(){return this.context.antialias}get textDrawingMode(){return this.context.textDrawingMode}get patternQuality(){return this.context.patternQuality}get transform(){return this.context.getTransform()}get textFontHeight(){return $(this.context.font)}get lineDash(){return this.context.getLineDash()}changeCanvasSize(t,e){return this.changeCanvasWidth(t).changeCanvasHeight(e)}changeCanvasWidth(t){return this.width=t,this}changeCanvasHeight(t){return this.height=t,this}save(){return this.context.save(),this}restore(){return this.context.restore(),this}rotate(t){return this.context.rotate(t),this}scale(t,e){return this.context.scale(t,e),this}translate(t,e){return this.context.translate(t,e),this}clip(...t){return this.context.clip(...t),this}setTransform(...t){return this.context.setTransform(...t),this}resetTransform(){return this.context.resetTransform(),this}getImageData(t,e,r,n){return this.context.getImageData(null!=t?t:0,null!=e?e:0,null!=r?r:this.width,null!=n?n:this.height)}putImageData(...t){return this.context.putImageData(...t),this}fill(...t){return this.context.fill(...t),this}printText(t,e,r,...n){return this.context.fillText(t,e,r,...n),this}printResponsiveText(t,e,r,n){const[i,s,a]=this.parseFont(this.context.font);if("number"!=typeof s)return this.printText(t,e,r);const{width:o}=this.measureText(t);if(o<=n)return this.printText(t,e,r);const h=n/o*s;return this.save().setTextFont(`${i}${h}${a}`).printText(t,e,r).restore()}printMultilineText(t,e,r){const n=t.split(/\r?\n/);if(n.length<=1)return this.printText(t,e,r);const i=this.textFontHeight;let s=r;for(const t of n)this.printText(t,e,Math.floor(s)),s+=i;return this}printWrappedText(t,e,r,n){const i=k(this,t,n);return this.printMultilineText(i,e,r)}stroke(){return this.context.stroke(),this}printStrokeRectangle(t,e,r,n){return this.context.strokeRect(t,e,r,n),this}printStrokeText(t,e,r,n){return this.context.strokeText(t,e,r,n),this}measureText(t){return this.context.measureText(t)}setTextSize(t){const e=this.parseFont(this.context.font);return 1===e.length?this:this.setTextFont(`${e[0]}${t}${e[2]}`)}setStroke(t){return this.context.strokeStyle=t,this}setLineWidth(t){return this.context.lineWidth=t,this}setStrokeWidth(t){return this.setLineWidth(t)}setLineDashOffset(t){return this.context.lineDashOffset=t,this}setLineJoin(t){return this.context.lineJoin=t,this}setLineCap(t){return this.context.lineCap=t,this}setLineDash(t){return this.context.setLineDash(t),this}printImage(t,...e){var r;return this.context.drawImage((r=t)instanceof i?r.canvas:r,...e),this}printCircularImage(t,e,r,n,{fit:i="fill"}={}){const{positionX:s,positionY:a,sizeX:o,sizeY:h}=this.resolveCircularCoordinates(t,e,r,n,i);return this.save().createCircularClip(e,r,n,0,2*Math.PI,!1).printImage(t,s,a,o,h).restore()}printRoundedImage(t,e,r,n,i,s){return this.save().createRoundedClip(e,r,n,i,s).printImage(t,e,r,n,i).restore()}printCircle(t,e,r){return this.save().createCircularPath(t,e,r).fill().restore()}printRectangle(t,e,r,n){return this.context.fillRect(t,e,r,n),this}printRoundedRectangle(t,e,r,n,i){return this.save().createRoundedPath(t,e,r,n,i).fill().restore()}createCircularPath(t,e,r,n=0,i=2*Math.PI,s=!1){return this.context.beginPath(),this.context.arc(t,e,r,n,i,s),this}createCircularClip(t,e,r,n,i,s){return this.createCircularPath(t,e,r,n,i,s).clip()}createRectanglePath(t,e,r,n){return this.context.rect(t,e,r,n),this}createRectangleClip(t,e,r,n){return this.createRectanglePath(t,e,r,n).clip()}createRoundedPath(t,e,r,n,i){if(r>0&&n>0){let s;"number"==typeof i?s={tl:i=Math.min(i,r/2,n/2),tr:i,br:i,bl:i}:(s=i,i=Math.min(5,r/2,n/2));const{tl:a=i,tr:o=i,br:h=i,bl:c=i}=s;this.context.beginPath(),this.context.moveTo(t+a,e),this.context.lineTo(t+r-o,e),this.context.quadraticCurveTo(t+r,e,t+r,e+o),this.context.lineTo(t+r,e+n-h),this.context.quadraticCurveTo(t+r,e+n,t+r-h,e+n),this.context.lineTo(t+c,e+n),this.context.quadraticCurveTo(t,e+n,t,e+n-c),this.context.lineTo(t,e+a),this.context.quadraticCurveTo(t,e,t+a,e),this.context.closePath()}return this}createRoundedClip(t,e,r,n,i){return this.createRoundedPath(t,e,r,n,i).clip()}setColor(t){return this.context.fillStyle=t,this}setTextFont(t){return this.context.font=t,this}setTextAlign(t){return this.context.textAlign=t,this}setTextBaseline(t){return this.context.textBaseline=t,this}beginPath(){return this.context.beginPath(),this}closePath(){return this.context.closePath(),this}createPattern(t,e){return this.context.createPattern((r=t)instanceof i?r.canvas:r,e);var r}printPattern(t,e){return this.setColor(this.createPattern(t,e))}createLinearGradient(t,e,r,n,i=[]){const s=this.context.createLinearGradient(t,e,r,n);for(const t of i)s.addColorStop(t.position,t.color);return s}printLinearColorGradient(t,e,r,n,i){const s=this.createLinearGradient(t,e,r,n,i);return this.setColor(s)}printLinearStrokeGradient(t,e,r,n,i){const s=this.createLinearGradient(t,e,r,n,i);return this.setStroke(s)}createRadialGradient(t,e,r,n,i,s,a=[]){const o=this.context.createRadialGradient(t,e,r,n,i,s);for(const t of a)o.addColorStop(t.position,t.color);return o}printRadialColorGradient(t,e,r,n,i,s,a){const o=this.createRadialGradient(t,e,r,n,i,s,a);return this.setColor(o)}printRadialStrokeGradient(t,e,r,n,i,s,a){const o=this.createRadialGradient(t,e,r,n,i,s,a);return this.setStroke(o)}createEllipsePath(t,e,r,n,i,s,a,o){return this.context.ellipse(t,e,r,n,i,s,a,o),this}createEllipseClip(t,e,r,n,i,s,a,o){return this.createEllipsePath(t,e,r,n,i,s,a,o).clip()}arc(t,e,r,n,i,s){return this.context.arc(t,e,r,n,i,s),this}arcTo(t,e,r,n,i){return this.context.arcTo(t,e,r,n,i),this}quadraticCurveTo(t,e,r,n){return this.context.quadraticCurveTo(t,e,r,n),this}bezierCurveTo(t,e,r,n,i,s){return this.context.bezierCurveTo(t,e,r,n,i,s),this}lineTo(t,e){return this.context.lineTo(t,e),this}moveTo(t,e){return this.context.moveTo(t,e),this}setShadowBlur(t){return this.context.shadowBlur=t,this}setShadowColor(t){return this.context.shadowColor=t,this}setShadowOffsetX(t){return this.context.shadowOffsetX=t,this}setShadowOffsetY(t){return this.context.shadowOffsetY=t,this}setMiterLimit(t){return this.context.miterLimit=t,this}setGlobalCompositeOperation(t){return this.context.globalCompositeOperation=t,this}setGlobalAlpha(t){return this.context.globalAlpha=t,this}setImageSmoothingEnabled(t){return this.context.imageSmoothingEnabled=t,this}setAntialiasMode(t){return this.context.antialias=t,this}setTextDrawingMode(t){return this.context.textDrawingMode=t,this}setPatternQuality(t){return this.context.patternQuality=t,this}resetShadows(){return this.setShadowBlur(0).setShadowOffsetX(0).setShadowOffsetY(0).setShadowColor("transparent")}clearCircle(t,e,r,n=0,i=2*Math.PI,s=!1){return this.createCircularClip(t,e,r,n,i,s).clearRectangle(t-r,e-r,2*r,2*r)}clearRectangle(t=0,e=0,r=this.width,n=this.height){return this.context.clearRect(t,e,r,n),this}isPointInPath(...t){return this.context.isPointInPath(...t)}process(t,...e){return t.call(this,this,...e),this}wrapText(t,e){return k(this,t,e)}createImageData(...t){return this.context.createImageData(...t)}jpeg(t){return this.toBuffer("image/jpeg",t)}jpegAsync(t){return this.toBufferAsync("image/jpeg",t)}jpegStream(t){return this.canvas.createJPEGStream(t)}png(t){return this.toBuffer("image/png",t)}pngAsync(t){return this.toBufferAsync("image/png",t)}pngStream(t){return this.canvas.createPNGStream(t)}pdf(t){return this.toBuffer("application/pdf",t)}pdfAsync(t){return this.toBufferAsync("application/pdf",t)}pdfStream(t){return this.canvas.createPDFStream(t)}toBuffer(...t){return this.canvas.toBuffer(...t)}toBufferAsync(...t){return new Promise(((e,r)=>this.canvas.toBuffer(((t,n)=>{t?r(t):e(n)}),...t)))}toDataURL(...t){return this.canvas.toDataURL(...t)}parseFont(t){const e=R.exec(t);return null===e?[t]:[t.slice(0,e.index),Number(e[1]),t.slice(e.index+e[1].length)]}resolveCircularCoordinates(t,e,r,n,i){const{width:s,height:a}=t;if("none"===i)return{positionX:e-s/2,positionY:r-a/2,sizeX:s,sizeY:a};const o=s/a,h=2*n;if("fill"===i||1===o)return{positionX:e-n,positionY:r-n,sizeX:h,sizeY:h};if("contain"===i)return o>1?{positionX:e-n,positionY:r-n/o,sizeX:h,sizeY:h/o}:{positionX:e-n*o,positionY:r-n,sizeX:h*o,sizeY:h};if(o>1){const t=h*o;return{positionX:e-t/2,positionY:r-h/2,sizeX:t,sizeY:h}}const c=h/o;return{positionX:e-h/2,positionY:r-c/2,sizeX:h,sizeY:c}}}const s=t(e,"resolveImage() is deprecated. Use loadImage() instead."),a=t(r,"registerFont() is deprecated. Use loadFont() instead.");function o(...t){return t.join(" ")}const h=t=>t.save().setGlobalCompositeOperation("difference").setColor("white").printRectangle(0,0,t.width,t.height).restore(),c=t=>{const e=t.getImageData(),{data:r}=e;for(let t=0;t<r.length;t+=4){const e=.2126*r[t]+.7152*r[t+1]+.0722*r[t+2];r[t]=e,r[t+1]=e,r[t+2]=e}return t.putImageData(e,0,0)},l=c,u=t=>{const e=t.getImageData(),{data:r}=e;for(let t=0;t<r.length;t+=4){const e=255-(.2126*r[t]+.7152*r[t+1]+.0722*r[t+2]);r[t]=e,r[t+1]=e,r[t+2]=e}return t.putImageData(e,0,0)},g=u,p=t=>{const e=t.getImageData(),{data:r}=e;for(let t=0;t<r.length;t+=4){const e=r[t],n=r[t+1],i=r[t+2];r[t]=.393*e+.769*n+.189*i,r[t+1]=.349*e+.686*n+.168*i,r[t+2]=.272*e+.534*n+.131*i}return t.putImageData(e,0,0)},x=t=>{const e=t.getImageData(),{data:r}=e;for(let t=0;t<r.length;t+=4)r[t]=0,r[t+1]=0,r[t+2]=0;return t.putImageData(e,0,0)},d=(t,e)=>{const r=t.getImageData(),{data:n}=r;for(let t=0;t<n.length;t+=4){const r=.2126*n[t]+.7152*n[t+1]+.0722*n[t+2]>=e?255:0;n[t]=r,n[t+1]=r,n[t+2]=r}return t.putImageData(r,0,0)},f=(t,e)=>{const r=t.getImageData(),{data:n}=r;for(let t=0;t<n.length;t+=4){const r=.2126*n[t]+.7152*n[t+1]+.0722*n[t+2]>=e?0:255;n[t]=r,n[t+1]=r,n[t+2]=r}return t.putImageData(r,0,0)},m=(t,e)=>{const r=t.getImageData(),{data:n}=r;for(let t=0;t<n.length;t+=4)n[t]+=e,n[t+1]+=e,n[t+2]+=e;return t.putImageData(r,0,0)},C=(t,e)=>{const r=t.getImageData(),{data:n}=r;for(let t=0;t<n.length;t+=4)n[t]-=e,n[t+1]-=e,n[t+2]-=e;return t.putImageData(r,0,0)},T=C,v=(t,e,r=!0)=>{const n=Math.round(Math.sqrt(e.length)),i=Math.floor(n/2),s=t.getImageData(),a=s.data,o=s.width,h=s.height,c=o,l=h,u=t.getImageData(),g=u.data,p=r?1:0;for(let t=0;t<l;t++)for(let r=0;r<c;r++){const s=t,l=r,u=4*(t*c+r);let x=0,d=0,f=0,m=0;for(let t=0;t<n;t++)for(let r=0;r<n;r++){const c=s+t-i,u=l+r-i;if(c>=0&&c<h&&u>=0&&u<o){const i=4*(c*o+u),s=e[t*n+r];x+=a[i]*s,d+=a[i+1]*s,f+=a[i+2]*s,m+=a[i+3]*s}}g[u]=x,g[u+1]=d,g[u+2]=f,g[u+3]=m+p*(255-m)}return t.putImageData(u,0,0)},I=[0,-1,0,-1,4,-1,0,-1,0],D=t=>v(t,I,!0),w=[0,-1,0,-1,5,-1,0,-1,0],P=(t,e=1)=>{for(let r=0;r<e;++r)v(t,w,!0);return t},b=[1/9,1/9,1/9,1/9,1/9,1/9,1/9,1/9,1/9],S=(t,e=1)=>{for(let r=0;r<e;++r)v(t,b,!0);return t},R=/([\d.]+)(px|pt|pc|in|cm|mm|%|em|ex|ch|rem|q)/i,$=(()=>{const t=new Map;return e=>{const r=t.get(e);if(r)return r;const n=R.exec(e);if(!n)return 0;let i=Number(n[1]);switch(n[2]){case"pt":i/=.75;break;case"pc":i*=16;break;case"in":i*=96;break;case"cm":i*=96/2.54;break;case"mm":i*=96/25.4;break;case"em":case"rem":i*=16/.75;break;case"q":i*=96/25.4/4}return t.set(e,i),i}})(),k=(t,e,r)=>{const n=[],i=[],s=t.measureText(" ").width;for(const a of e.split(/\r?\n/)){let e=r;for(const o of a.split(" ")){const a=t.measureText(o).width,h=a+s;h>e?(i.length&&(n.push(i.join(" ")),i.length=0),i.push(o),e=r-a):(e-=h,i.push(o))}i.length&&(n.push(i.join(" ")),i.length=0)}return n.join("\n")},z=(t,...e)=>`${t}(${e.join(" ")})`,L=t=>`#${t}`,M=(t,e,r)=>`rgb(${t}, ${e}, ${r})`,G=(t,e,r,n)=>`rgba(${t}, ${e}, ${r}, ${n})`,X=(t,e,r)=>`hsl(${t}, ${e}%, ${r}%)`,Y=(t,e,r,n)=>`hsla(${t}, ${e}%, ${r}%, ${n})`,y=t=>t;export{i as Canvas,S as blur,m as brightness,y as color,v as convolute,C as darkness,D as edge,z as filter,R as fontRegExp,o as fontVariant,$ as getFontHeight,l as grayscale,c as greyscale,L as hex,X as hsl,Y as hsla,h as invert,u as invertGrayscale,g as invertGreyscale,f as invertedThreshold,T as myOldFriend,a as registerFont,s as resolveImage,M as rgb,G as rgba,p as sepia,P as sharpen,x as silhouette,k as textWrap,d as threshold};
//# sourceMappingURL=cairo.mjs.map
