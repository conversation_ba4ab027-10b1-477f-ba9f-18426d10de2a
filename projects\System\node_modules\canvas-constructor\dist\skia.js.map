{"version": 3, "file": "skia.js", "sources": ["../src/skia.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/unified-signatures */\n\nimport { deprecate } from 'node:util';\n\nimport {\n\tCanvas as NativeCanvas,\n\tFontLibrary,\n\tImage as NativeImage,\n\tloadImage,\n\tPath2D,\n\ttype CanvasRenderingContext2D as NativeCanvasRenderingContext2D,\n\ttype Font,\n\ttype RenderOptions\n} from 'skia-canvas';\n\nexport interface BeveledRadiusOptions {\n\t/**\n\t * Top left corner.\n\t */\n\ttl: number;\n\n\t/**\n\t * Top right corner.\n\t */\n\ttr: number;\n\n\t/**\n\t * Bottom right corner.\n\t */\n\tbr: number;\n\n\t/**\n\t * Bottom left corner.\n\t */\n\tbl: number;\n}\n\nexport interface GradientStop {\n\tposition: number;\n\tcolor: string;\n}\n\nexport interface PrintCircularOptions {\n\t/**\n\t * The fit options, this is similar to CSS's object-fit.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/CSS/object-fit\n\t */\n\tfit?: 'fill' | 'contain' | 'cover' | 'none';\n}\n\nexport type ImageResolvable = Canvas | NativeImage | NativeCanvas;\nfunction _resolveImage(resolvable: ImageResolvable) {\n\treturn resolvable instanceof Canvas ? resolvable.canvas : resolvable;\n}\n\nexport type PatternResolvable = Canvas | NativeImage | NativeImage;\nfunction _resolvePattern(resolvable: PatternResolvable) {\n\tif (resolvable instanceof Canvas) return resolvable.canvas;\n\treturn resolvable;\n}\n\nexport type PatternRepeat = 'repeat' | 'repeat-x' | 'repeat-y' | 'no-repeat' | null;\nexport type Transform = ReturnType<NativeCanvasRenderingContext2D['getTransform']>;\n\nexport type RenderImageFormat = `${`${'image/' | ''}${'png' | 'jpg'}` | 'image/svg+xml' | 'svg' | `${'application/' | ''}pdf`}${`@${number}x` | ''}`;\nexport interface SaveAsOptions extends RenderOptions {\n\t/**\n\t * The image format to use.\n\t */\n\tformat?: RenderImageFormat;\n}\n\nexport class Canvas {\n\t/**\n\t * The constructed Canvas.\n\t */\n\tpublic canvas: NativeCanvas;\n\n\t/**\n\t * The 2D context for the Canvas.\n\t */\n\tpublic context: NativeCanvasRenderingContext2D;\n\n\tpublic constructor(width: number, height: number);\n\tpublic constructor(canvas: NativeCanvas, context?: NativeCanvasRenderingContext2D);\n\tpublic constructor(width: number | NativeCanvas, height?: number | NativeCanvasRenderingContext2D) {\n\t\tif (typeof width === 'number') {\n\t\t\tthis.canvas = new NativeCanvas(width, height as number);\n\t\t\tthis.context = this.canvas.getContext('2d');\n\t\t} else {\n\t\t\tthis.canvas = width;\n\t\t\tthis.context = (height ?? this.canvas.getContext('2d')) as NativeCanvasRenderingContext2D;\n\t\t}\n\t}\n\n\t/**\n\t * Creates a new page for the canvas, which can be retrieved later with {@link pages}.\n\t * @param width The width of the new page.\n\t * @param height The height of the new page.\n\t * @returns A new instance of {@link NativeCanvas} with the new context.\n\t * @note This is a `skia-canvas` extension.\n\t */\n\tpublic addPage(width: number, height: number): Canvas {\n\t\treturn new Canvas(this.canvas, this.canvas.newPage(width, height));\n\t}\n\n\t/**\n\t * The image width of this canvas\n\t */\n\tpublic get width(): number {\n\t\treturn this.canvas.width;\n\t}\n\n\tpublic set width(value: number) {\n\t\tthis.canvas.width = value;\n\t}\n\n\t/**\n\t * The image height of this canvas\n\t */\n\tpublic get height(): number {\n\t\treturn this.canvas.height;\n\t}\n\n\tpublic set height(value: number) {\n\t\tthis.canvas.height = value;\n\t}\n\n\t/**\n\t * The current text direction used to draw text.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/direction\n\t */\n\tpublic get direction(): CanvasDirection {\n\t\treturn this.context.direction;\n\t}\n\n\t/**\n\t * The current text style to use when drawing text. This string uses the same syntax as the\n\t * [CSS font](https://developer.mozilla.org/en-US/docs/Web/CSS/font) specifier.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/font\n\t */\n\tpublic get font(): string {\n\t\treturn this.context.font;\n\t}\n\n\t/**\n\t * The alpha (transparency) value that is applied to shapes and images before they are drawn onto the canvas.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/globalAlpha\n\t */\n\tpublic get globalAlpha(): number {\n\t\treturn this.context.globalAlpha;\n\t}\n\n\t/**\n\t * Whether scaled images are smoothed (true, default) or not (false).\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/imageSmoothingEnabled\n\t */\n\tpublic get imageSmoothingEnabled(): boolean {\n\t\treturn this.context.imageSmoothingEnabled;\n\t}\n\n\t/**\n\t * The quality of image smoothing.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/imageSmoothingQuality\n\t */\n\tpublic get imageSmoothingQuality(): ImageSmoothingQuality {\n\t\treturn this.context.imageSmoothingQuality;\n\t}\n\n\t/**\n\t * Returns the pages created with {@link addPage}.\n\t * @note This is a `skia-canvas` extension.\n\t */\n\tpublic get pages(): NativeCanvasRenderingContext2D[] {\n\t\treturn this.canvas.pages;\n\t}\n\n\t/**\n\t * Returns the canvas's current CSS3 [font-variant](https://developer.mozilla.org/en-US/docs/Web/CSS/font-variant).\n\t * @note This is a `skia-canvas` extension.\n\t * @see {@link setFontVariant}\n\t */\n\tpublic get fontVariant(): FontVariantString {\n\t\treturn this.context.fontVariant;\n\t}\n\n\t/**\n\t * Returns the text tracking property.\n\t * @note This is a `skia-canvas` extension.\n\t * @see {@link setTextTracking}\n\t */\n\tpublic get textTracking(): number {\n\t\treturn this.context.textTracking;\n\t}\n\n\t/**\n\t * Returns whether or not text-wrap is enabled.\n\t * @note This is a `skia-canvas` extension.\n\t * @see {@link setTextWrap}\n\t */\n\tpublic get textWrap(): boolean {\n\t\treturn this.context.textWrap;\n\t}\n\n\t/**\n\t * Returns the current transformation matrix being applied to the context.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/getTransform\n\t */\n\tpublic get transform(): Transform {\n\t\treturn this.context.getTransform();\n\t}\n\n\t/**\n\t * The font height\n\t */\n\tpublic get textFontHeight(): number {\n\t\treturn getFontHeight(this.context.font);\n\t}\n\n\t/**\n\t * A list of numbers that specifies distances to alternately draw a line and a gap (in coordinate space units).\n\t * If the number, when setting the elements, was odd, the elements of the array get copied and concatenated. For\n\t * example, setting the line dash to [5, 15, 25] will result in getting back [5, 15, 25, 5, 15, 25].\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/getLineDash\n\t * @example\n\t * new Canvas(400, 300)\n\t *     .beginPath()\n\t *     .setLineDash([5, 15])\n\t *     .moveTo(0, 50)\n\t *     .lineTo(400, 50)\n\t *     .stroke()\n\t *     .png();\n\t */\n\tpublic get lineDash(): number[] {\n\t\treturn this.context.getLineDash();\n\t}\n\n\t/**\n\t * Change the current canvas' size.\n\t * @param width The new width for the canvas.\n\t * @param height The new height for the canvas.\n\t */\n\tpublic changeCanvasSize(width: number, height: number): this {\n\t\treturn this.changeCanvasWidth(width).changeCanvasHeight(height);\n\t}\n\n\t/**\n\t * Change the current canvas' width.\n\t * @param width The new width for the canvas.\n\t */\n\tpublic changeCanvasWidth(width: number): this {\n\t\tthis.width = width;\n\t\treturn this;\n\t}\n\n\t/**\n\t * Change the current canvas' height.\n\t * @param height The new height for the canvas.\n\t */\n\tpublic changeCanvasHeight(height: number): this {\n\t\tthis.height = height;\n\t\treturn this;\n\t}\n\n\t/**\n\t * Save the entire state of the canvas by pushing the current state onto a stack.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/save\n\t */\n\tpublic save(): this {\n\t\tthis.context.save();\n\t\treturn this;\n\t}\n\n\t/**\n\t * Restores the most recently saved canvas by popping the top entry in the drawing state stack. If there is no saved\n\t * state, this method does nothing.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/restore\n\t */\n\tpublic restore(): this {\n\t\tthis.context.restore();\n\t\treturn this;\n\t}\n\n\t/**\n\t * Adds a rotation to the transformation matrix. The angle argument represents a clockwise rotation angle and is\n\t * expressed in radians.\n\t * @param angle The angle to rotate clockwise in radians.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/rotate\n\t */\n\tpublic rotate(angle: number): this {\n\t\tthis.context.rotate(angle);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Adds a scaling transformation to the canvas units by X horizontally and by y vertically.\n\t * @param x Scaling factor in the horizontal direction.\n\t * @param y Scaling factor in the vertical direction.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/scale\n\t */\n\tpublic scale(x: number, y: number): this {\n\t\tthis.context.scale(x, y);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Adds a translation transformation by moving the canvas and its origin X horizontally and y vertically on the grid.\n\t * @param x Distance to move in the horizontal direction.\n\t * @param y Distance to move in the vertical direction.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/translate\n\t */\n\tpublic translate(x: number, y: number): this {\n\t\tthis.context.translate(x, y);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Turns the path currently being built into the current clipping path.\n\t * @param fillRule The algorithm by which to determine if a point is inside a path or outside a path.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/clip\n\t */\n\tpublic clip(fillRule?: CanvasFillRule): this;\n\t/**\n\t * Turns the path currently being built into the current clipping path.\n\t * @param path The path to use.\n\t * @param fillRule The algorithm by which to determine if a point is inside a path or outside a path.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/clip\n\t */\n\tpublic clip(path: Path2D, fillRule?: CanvasFillRule): this;\n\tpublic clip(...args: []): this {\n\t\tthis.context.clip(...args);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Resets (overrides) the current transformation to the identity matrix and then invokes a transformation described\n\t * by the arguments of this method.\n\t * @param a Horizontal scaling.\n\t * @param b Horizontal skewing.\n\t * @param c Vertical skewing.\n\t * @param d Vertical scaling.\n\t * @param e Horizontal moving.\n\t * @param f Vertical moving.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/setTransform\n\t */\n\tpublic setTransform(a: number, b: number, c: number, d: number, e: number, f: number): this;\n\t/**\n\t * Resets (overrides) the current transformation to the identity matrix and then invokes a transformation described\n\t * by the arguments of this method.\n\t * @param matrix The new transform matrix.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/setTransform\n\t */\n\tpublic setTransform(transform?: DOMMatrix): this;\n\tpublic setTransform(...args: readonly any[]): this {\n\t\tthis.context.setTransform(...args);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Resets the transformation.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/resetTransform\n\t */\n\tpublic resetTransform(): this {\n\t\tthis.context.resetTransform();\n\t\treturn this;\n\t}\n\n\t/**\n\t * Resets the filters.\n\t */\n\tpublic resetFilters(): this {\n\t\treturn this.setFilter('none');\n\t}\n\n\t/**\n\t * Returns an ImageData object representing the underlying pixel data for the area of the canvas\n\t * denoted by the entire Canvas. This method is not affected by the canvas transformation matrix.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/getImageData\n\t */\n\tpublic getImageData(): ImageData;\n\t/**\n\t * Returns an ImageData object representing the underlying pixel data for the area of the canvas denoted by the rectangle which starts at (sx, sy)\n\t * and has an sw width and sh height. This method is not affected by the canvas transformation matrix.\n\t * @param x The X coordinate of the upper left corner of the rectangle from which the ImageData will be extracted.\n\t * @param y The Y coordinate of the upper left corner of the rectangle from which the ImageData will be extracted.\n\t * @param width The width of the rectangle from which the ImageData will be extracted.\n\t * @param height The height of the rectangle from which the ImageData will be extracted.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/getImageData\n\t */\n\tpublic getImageData(x: number, y: number, width: number, height: number): ImageData;\n\tpublic getImageData(x?: number, y?: number, width?: number, height?: number): ImageData {\n\t\treturn this.context.getImageData(x ?? 0, y ?? 0, width ?? this.width, height ?? this.height);\n\t}\n\n\t/**\n\t * The CanvasRenderingContext2D.putImageData() method of the Canvas 2D API paints data from the given ImageData object onto the bitmap.\n\t * This method is not affected by the canvas transformation matrix.\n\t * @param imagedata An ImageData object containing the array of pixel values.\n\t * @param dx Horizontal position (x-coordinate) at which to place the image data in the destination canvas.\n\t * @param dy Vertical position (y-coordinate) at which to place the image data in the destination canvas.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/putImageData\n\t */\n\tpublic putImageData(imagedata: ImageData, dx: number, dy: number): this;\n\t/**\n\t * The CanvasRenderingContext2D.putImageData() method of the Canvas 2D API paints data from the given ImageData object onto the bitmap.\n\t * Only the pixels from that rectangle are painted.\n\t * This method is not affected by the canvas transformation matrix.\n\t * @param imagedata An ImageData object containing the array of pixel values.\n\t * @param x Horizontal position (x-coordinate) at which to place the image data in the destination canvas.\n\t * @param y Vertical position (y-coordinate) at which to place the image data in the destination canvas.\n\t * @param dirtyX Horizontal position (x-coordinate). The X coordinate of the top left hand corner of your Image data. Defaults to 0.\n\t * @param dirtyY Vertical position (y-coordinate). The Y coordinate of the top left hand corner of your Image data. Defaults to 0.\n\t * @param dirtyWidth Width of the rectangle to be painted. Defaults to the width of the image data.\n\t * @param dirtyHeight Height of the rectangle to be painted. Defaults to the height of the image data.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/putImageData\n\t */\n\tpublic putImageData(imagedata: ImageData, x: number, y: number, dirtyX: number, dirtyY: number, dirtyWidth: number, dirtyHeight: number): this;\n\n\tpublic putImageData(...args: [any, any, any]): this {\n\t\tthis.context.putImageData(...args);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Fills the current or given path with the current fill style using the non-zero or even-odd winding rule.\n\t * @param fillRule The algorithm by which to determine if a point is inside a path or outside a path.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/fill\n\t */\n\tpublic fill(fillRule?: CanvasFillRule): this;\n\t/**\n\t * Fills the current or given path with the current fill style using the non-zero or even-odd winding rule.\n\t * @param path The path to fill.\n\t * @param fillRule The algorithm by which to determine if a point is inside a path or outside a path.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/fill\n\t */\n\tpublic fill(path: Path2D, fillRule?: CanvasFillRule): this;\n\tpublic fill(...args: [any]): this {\n\t\tthis.context.fill(...args);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Add a text.\n\t * @param text The text to write.\n\t * @param x The position x to start drawing the element.\n\t * @param y The position y to start drawing the element.\n\t * @param maxWidth The maximum width to draw. If specified, and the string is computed to be wider than this width,\n\t * the font is adjusted to use a more horizontally condensed font.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/fillText\n\t */\n\tpublic printText(text: string, x: number, y: number, maxWidth?: number): this;\n\tpublic printText(text: string, x: number, y: number, ...rest: readonly any[]): this {\n\t\tthis.context.fillText(text, x, y, ...rest);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Add responsive text\n\t * @param text The text to write.\n\t * @param x The position x to start drawing the element.\n\t * @param y The position y to start drawing the element.\n\t * @param maxWidth The max length in pixels for the text.\n\t * @example\n\t * new Canvas(400, 300)\n\t *     .setTextFont('40px Tahoma')\n\t *     .printResponsiveText('Hello World', 30, 30, 50)\n\t *     .png();\n\t */\n\tpublic printResponsiveText(text: string, x: number, y: number, maxWidth: number): this {\n\t\tconst [tail, height, lead] = this.parseFont(this.context.font);\n\t\tif (typeof height !== 'number') return this.printText(text, x, y);\n\n\t\t// Measure the width of the text. If it fits `maxWidth`, draw the text directly:\n\t\tconst { width } = this.measureText(text);\n\t\tif (width <= maxWidth) return this.printText(text, x, y);\n\n\t\t// Otherwise save state, set the font with a size that fits, draw the text, and restore:\n\t\tconst newHeight = (maxWidth / width) * height;\n\t\treturn this.save().setTextFont(`${tail}${newHeight}${lead}`).printText(text, x, y).restore();\n\t}\n\n\t/**\n\t * Add text with line breaks (node-canvas and web canvas compatible)\n\t * @param text The text to write.\n\t * @param x The position x to start drawing the element.\n\t * @param y The position y to start drawing the element.\n\t * @example\n\t * new Canvas(400, 300)\n\t *     .setTextFont('25px Tahoma')\n\t *     .printMultilineText('This is a really\\nlong text!', 139, 360)\n\t *     .png();\n\t */\n\tpublic printMultilineText(text: string, x: number, y: number): this {\n\t\tconst lines = text.split(/\\r?\\n/);\n\n\t\t// If there are no new lines, return using printText\n\t\tif (lines.length <= 1) return this.printText(text, x, y);\n\n\t\tconst height = this.textFontHeight;\n\n\t\tlet linePositionY = y;\n\t\tfor (const line of lines) {\n\t\t\tthis.printText(line, x, Math.floor(linePositionY));\n\t\t\tlinePositionY += height;\n\t\t}\n\n\t\treturn this;\n\t}\n\n\t/**\n\t * Wrap the text in multiple lines and write it\n\t * @param text The text to wrap and write.\n\t * @param x The position x to start drawing the element.\n\t * @param y The position y to start drawing the element.\n\t * @param wrapWidth The width in pixels of the line wrap\n\t * @example\n\t * new Canvas(400, 300)\n\t *     .setTextFont('25px Tahoma')\n\t *     .printWrappedText('This is a really long text!', 139, 360)\n\t *     .png();\n\t */\n\tpublic printWrappedText(text: string, x: number, y: number, wrapWidth: number): this {\n\t\tconst wrappedText = textWrap(this, text, wrapWidth);\n\t\treturn this.printMultilineText(wrappedText, x, y);\n\t}\n\n\t/**\n\t * Strokes the current or given path with the current stroke style using the non-zero winding rule.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/stroke\n\t */\n\tpublic stroke(): this {\n\t\tthis.context.stroke();\n\t\treturn this;\n\t}\n\n\t/**\n\t * Paints a rectangle which has a starting point at (X, Y) and has a w width and an h height onto the canvas, using\n\t * the current stroke style.\n\t * @param x The x axis of the coordinate for the rectangle starting point.\n\t * @param y The y axis of the coordinate for the rectangle starting point.\n\t * @param width The rectangle's width.\n\t * @param height The rectangle's height.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/strokeRect\n\t */\n\tpublic printStrokeRectangle(x: number, y: number, width: number, height: number): this {\n\t\tthis.context.strokeRect(x, y, width, height);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Add stroked text.\n\t * @param text The text to write.\n\t * @param x The position x to start drawing the element.\n\t * @param y The position y to start drawing the element.\n\t * @param maxWidth The maximum width to draw. If specified, and the string is computed to be wider than this width,\n\t * the font is adjusted to use a more horizontally condensed font.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/strokeText\n\t */\n\tpublic printStrokeText(text: string, x: number, y: number, maxWidth?: number): this {\n\t\tthis.context.strokeText(text, x, y, maxWidth);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Measure a text's width given a string.\n\t * @param text The text to measure.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/measureText\n\t * @example\n\t * const size = new Canvas(500, 400)\n\t *     .setTextFont('40px Tahoma')\n\t *     .measureText('Hello World!'); // Returns a number\n\t *\n\t * const newSize = size.width < 500 ? 40 : (500 / size.width) * 40;\n\t *\n\t * new Canvas(500, 400)\n\t *     .setTextFont(`${newSize}px Tahoma`)\n\t *     .printText('Hello World!', 30, 50)\n\t *     .png(); // Returns a Buffer\n\t * @example\n\t * new Canvas(500, 400)\n\t *     .setTextFont('40px Tahoma')\n\t *     .process((canvas) => {\n\t *         const size = canvas.measureText('Hello World!');\n\t *         const newSize = size.width < 500 ? 40 : (500 / size.width) * 40;\n\t *         this.setTextFont(`${newSize}px Tahoma`);\n\t *     })\n\t *     .printText('Hello World!', 30, 50)\n\t *     .png(); // Returns a Buffer\n\t */\n\tpublic measureText(text: string): TextMetrics {\n\t\treturn this.context.measureText(text);\n\t}\n\n\t/**\n\t * Set the new font size, unlike setTextFont, this only requires the number.\n\t * @param size The new size to set\n\t */\n\tpublic setTextSize(size: number): this {\n\t\tconst result = this.parseFont(this.context.font);\n\t\treturn result.length === 1 ? this : this.setTextFont(`${result[0]}${size}${result[2]}`);\n\t}\n\n\t/**\n\t * Specifies the color or style to use for the lines around shapes.\n\t * @param color A canvas' color resolvable.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/strokeStyle\n\t */\n\tpublic setStroke(color: string | CanvasGradient | CanvasPattern): this {\n\t\tthis.context.strokeStyle = color;\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the thickness of lines in space units.\n\t * @param width A number specifying the line width in space units.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/lineWidth\n\t */\n\tpublic setLineWidth(width: number): this {\n\t\tthis.context.lineWidth = width;\n\t\treturn this;\n\t}\n\n\tpublic setStrokeWidth(width: number): this {\n\t\treturn this.setLineWidth(width);\n\t}\n\n\t/**\n\t * Sets the line dash pattern offset or \"phase\" to achieve a \"marching ants\" effect\n\t * @param value A float specifying the amount of the offset. Initially 0.0.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/lineDashOffset\n\t */\n\tpublic setLineDashOffset(value: number): this {\n\t\tthis.context.lineDashOffset = value;\n\t\treturn this;\n\t}\n\n\t/**\n\t * Determines how two connecting segments (of lines, arcs or curves) with non-zero lengths in a shape are joined\n\t * together (degenerate segments with zero lengths, whose specified endpoints and control points are exactly at the\n\t * same position, are skipped).\n\t * @param value The line join type.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/lineJoin\n\t */\n\tpublic setLineJoin(value: CanvasLineJoin): this {\n\t\tthis.context.lineJoin = value;\n\t\treturn this;\n\t}\n\n\t/**\n\t * Determines how the end points of every line are drawn. There are three possible values for this property and\n\t * those are: butt, round and square. By default this property is set to butt.\n\t * @param value The line join type.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/lineCap\n\t */\n\tpublic setLineCap(value: CanvasLineCap): this {\n\t\tthis.context.lineCap = value;\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the line dash pattern used when stroking lines, using an array of values which specify alternating lengths\n\t * of lines and gaps which describe the pattern.\n\t * @param segments An Array of numbers which specify distances to alternately draw a line and a gap (in coordinate\n\t * space units). If the number of elements in the array is odd, the elements of the array get copied and\n\t * concatenated. For example, [5, 15, 25] will become [5, 15, 25, 5, 15, 25]. If the array is empty, the line dash\n\t * list is cleared and line strokes return to being solid.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/setLineDash\n\t */\n\tpublic setLineDash(segments: number[]): this {\n\t\tthis.context.setLineDash(segments);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Add an image at a position (x, y) with the source image's width and height.\n\t * @param image The image.\n\t * @param dx The x-axis coordinate in the destination canvas at which to place the top-left corner of the source `image`.\n\t * @param dy The y-axis coordinate in the destination canvas at which to place the top-left corner of the source `image`.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/drawImage\n\t */\n\tpublic printImage(image: ImageResolvable, dx: number, dy: number): this;\n\t/**\n\t * Add an image at a position (x, y) with a given width and height.\n\t * @param image The image.\n\t * @param dx The x-axis coordinate in the destination canvas at which to place the top-left corner of the source `image`.\n\t * @param dy The y-axis coordinate in the destination canvas at which to place the top-left corner of the source `image`.\n\t * @param dw The width to draw the `image` in the destination canvas. This allows scaling of the drawn image.\n\t * @param dh The height to draw the `image` in the destination canvas. This allows scaling of the drawn image.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/drawImage\n\t */\n\tpublic printImage(image: ImageResolvable, dx: number, dy: number, dw: number, dh: number): this;\n\t/**\n\t * Add an image at a position (x, y) with a given width and height, from a specific source rectangle.\n\t * @param image The image.\n\t * @param sx The x-axis coordinate of the top left corner of the sub-rectangle of the source `image` to draw into the destination context.\n\t * @param sy The y-axis coordinate of the top left corner of the sub-rectangle of the source `image` to draw into the destination context.\n\t * @param sw The width of the sub-rectangle of the source `image` to draw into the destination context.\n\t * @param sh The height of the sub-rectangle of the source `image` to draw into the destination context.\n\t * @param dx The x-axis coordinate in the destination canvas at which to place the top-left corner of the source `image`.\n\t * @param dy The y-axis coordinate in the destination canvas at which to place the top-left corner of the source `image`.\n\t * @param dw The width to draw the `image` in the destination canvas. This allows scaling of the drawn image.\n\t * @param dh The height to draw the `image` in the destination canvas. This allows scaling of the drawn image.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/drawImage\n\t */\n\tpublic printImage(image: ImageResolvable, sx: number, sy: number, sw: number, sh: number, dx: number, dy: number, dw: number, dh: number): this;\n\tpublic printImage(image: ImageResolvable, ...args: [number, number]) {\n\t\tthis.context.drawImage(_resolveImage(image), ...args);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Add a round image.\n\t * @param imageOrBuffer The image.\n\t * @param x The X coordinate in the destination canvas at which to place the top-left corner of the source image.\n\t * @param y The Y coordinate in the destination canvas at which to place the top-left corner of the source image.\n\t * @param width The width to draw the image in the destination canvas. This allows scaling of the drawn image. If not specified, the image is not scaled in width when drawn.\n\t * @param height The height to draw the image in the destination canvas. This allows scaling of the drawn image. If not specified, the image is not scaled in height when drawn.\n\t * @param radius The radius for the circle\n\t */\n\tpublic printCircularImage(imageOrBuffer: ImageResolvable, x: number, y: number, radius: number, options?: PrintCircularOptions): this;\n\tpublic printCircularImage(\n\t\timageOrBuffer: ImageResolvable,\n\t\tx: number,\n\t\ty: number,\n\t\tradius: number,\n\t\t{ fit = 'fill' }: PrintCircularOptions = {}\n\t): this {\n\t\tconst { positionX, positionY, sizeX, sizeY } = this.resolveCircularCoordinates(imageOrBuffer, x, y, radius, fit);\n\t\treturn this.save()\n\t\t\t.createCircularClip(x, y, radius, 0, Math.PI * 2, false)\n\t\t\t.printImage(imageOrBuffer, positionX, positionY, sizeX, sizeY)\n\t\t\t.restore();\n\t}\n\n\t/**\n\t * Add a beveled image.\n\t * @param imageOrBuffer The image.\n\t * @param x The position x to start drawing the element.\n\t * @param y The position y to start drawing the element.\n\t * @param width The width of the element.\n\t * @param height The height of the element.\n\t * @param radius The radius for the new image.\n\t */\n\tpublic printRoundedImage(\n\t\timageOrBuffer: ImageResolvable,\n\t\tx: number,\n\t\ty: number,\n\t\twidth: number,\n\t\theight: number,\n\t\tradius: BeveledRadiusOptions | number\n\t): this;\n\n\tpublic printRoundedImage(\n\t\timageOrBuffer: ImageResolvable,\n\t\tx: number,\n\t\ty: number,\n\t\twidth: number,\n\t\theight: number,\n\t\tradius: BeveledRadiusOptions | number\n\t): this {\n\t\treturn this.save().createRoundedClip(x, y, width, height, radius).printImage(imageOrBuffer, x, y, width, height).restore();\n\t}\n\n\t/**\n\t * Add a circle or semi circle.\n\t * @param x The position x in the center of the circle.\n\t * @param y The position y in the center of the circle.\n\t * @param radius The radius for the clip.\n\t */\n\tpublic printCircle(x: number, y: number, radius: number): this {\n\t\treturn this.save().createCircularPath(x, y, radius).fill().restore();\n\t}\n\n\t/**\n\t * Add a rectangle.\n\t * @param x The position x to start drawing the element.\n\t * @param y The position y to start drawing the element.\n\t * @param width The width of the element.\n\t * @param height The height of the element.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/fillRect\n\t */\n\tpublic printRectangle(x: number, y: number, width: number, height: number): this {\n\t\tthis.context.fillRect(x, y, width, height);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Add a beveled rectangle.\n\t * @param x The position x to start drawing the element.\n\t * @param y The position y to start drawing the element.\n\t * @param width  The width of the element.\n\t * @param height The height of the element.\n\t * @param radius The radius for the bevels.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/fillRect\n\t * @example\n\t * // Radius argument\n\t * new Canvas(200, 200)\n\t *     .printRoundedRectangle(0, 0, 200, 50, 35)\n\t *     .png();\n\t *\n\t * @example\n\t * // Configured bevels\n\t * new Canvas(200, 200)\n\t *     .printRoundedRectangle(0, 0, 200, 50, {\n\t *         // Top left border\n\t *         tl: 15,\n\t *         // Top right border\n\t *         tr: 20,\n\t *         // Bottom left border\n\t *         bl: 5,\n\t *         // Bottom right border\n\t *         br: 10\n\t *     })\n\t *     .png();\n\t *\n\t * @example\n\t * // Top bevels only\n\t * new Canvas(200, 200)\n\t *     .printRoundedRectangle(0, 0, 200, 50, { tl: 20, tr: 20, bl: 0, br: 0 })\n\t *     .png();\n\t */\n\tpublic printRoundedRectangle(x: number, y: number, width: number, height: number, radius: number | BeveledRadiusOptions): this {\n\t\treturn this.save().createRoundedPath(x, y, width, height, radius).fill().restore();\n\t}\n\n\t/**\n\t * Create a round path.\n\t * @param dx The position x in the center of the clip's circle.\n\t * @param dy The position y in the center of the clip's circle.\n\t * @param radius The radius for the clip.\n\t * @param start The degree in radians to start drawing the circle.\n\t * @param angle The degree in radians to finish drawing the circle, defaults to a full circle.\n\t * @param antiClockwise Whether the path should be anti-clockwise.\n\t */\n\tpublic createCircularPath(dx: number, dy: number, radius: number, start = 0, angle = Math.PI * 2, antiClockwise = false): this {\n\t\tthis.context.beginPath();\n\t\tthis.context.arc(dx, dy, radius, start, angle, antiClockwise);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Create a round clip.\n\t * @param dx The position x in the center of the clip's circle.\n\t * @param dy The position y in the center of the clip's circle.\n\t * @param radius The radius for the clip.\n\t * @param start The degree in radians to start drawing the circle.\n\t * @param angle The degree in radians to finish drawing the circle, defaults to a full circle.\n\t * @param antiClockwise Whether the path should be anti-clockwise.\n\t * @see createRoundPath\n\t */\n\tpublic createCircularClip(dx: number, dy: number, radius: number, start?: number, angle?: number, antiClockwise?: boolean): this {\n\t\treturn this.createCircularPath(dx, dy, radius, start, angle, antiClockwise).clip();\n\t}\n\n\t/**\n\t * Create a rectangle path.\n\t * @param x The position x in the left corner.\n\t * @param y The position y in the upper corner.\n\t * @param width The width of the rectangle.\n\t * @param height The height of the rectangle.\n\t */\n\tpublic createRectanglePath(x: number, y: number, width: number, height: number): this {\n\t\tthis.context.rect(x, y, width, height);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Create a rectangle clip.\n\t * @param x The position x in the left corner.\n\t * @param y The position y in the upper corner.\n\t * @param width The width of the rectangle.\n\t * @param height The height of the rectangle.\n\t */\n\tpublic createRectangleClip(x: number, y: number, width: number, height: number): this {\n\t\treturn this.createRectanglePath(x, y, width, height).clip();\n\t}\n\n\t/**\n\t * Create a beveled path.\n\t * @param x The position x to start drawing clip.\n\t * @param y The position y to start drawing clip.\n\t * @param width The width of clip.\n\t * @param height The height of clip.\n\t * @param radius The radius for clip's rounded borders.\n\t */\n\tpublic createRoundedPath(x: number, y: number, width: number, height: number, radius: number | BeveledRadiusOptions): this {\n\t\tif (width > 0 && height > 0) {\n\t\t\tlet radiusObject: BeveledRadiusOptions | undefined = undefined;\n\t\t\tif (typeof radius === 'number') {\n\t\t\t\tradius = Math.min(radius, width / 2, height / 2);\n\t\t\t\tradiusObject = { tl: radius, tr: radius, br: radius, bl: radius };\n\t\t\t} else {\n\t\t\t\tradiusObject = radius;\n\t\t\t\tradius = Math.min(5, width / 2, height / 2);\n\t\t\t}\n\t\t\tconst { tl = radius, tr = radius, br = radius, bl = radius } = radiusObject;\n\t\t\tthis.context.beginPath();\n\t\t\tthis.context.moveTo(x + tl, y);\n\t\t\tthis.context.lineTo(x + width - tr, y);\n\t\t\tthis.context.quadraticCurveTo(x + width, y, x + width, y + tr);\n\t\t\tthis.context.lineTo(x + width, y + height - br);\n\t\t\tthis.context.quadraticCurveTo(x + width, y + height, x + width - br, y + height);\n\t\t\tthis.context.lineTo(x + bl, y + height);\n\t\t\tthis.context.quadraticCurveTo(x, y + height, x, y + height - bl);\n\t\t\tthis.context.lineTo(x, y + tl);\n\t\t\tthis.context.quadraticCurveTo(x, y, x + tl, y);\n\t\t\tthis.context.closePath();\n\t\t}\n\t\treturn this;\n\t}\n\n\t/**\n\t * Create a beveled clip.\n\t * @param x The position x to start drawing clip.\n\t * @param y The position y to start drawing clip.\n\t * @param width The width of clip.\n\t * @param height The height of clip.\n\t * @param radius The radius for clip's rounded borders.\n\t * @example\n\t * // Radius argument, fill the content\n\t * new Canvas(200, 200)\n\t *     .createRoundedClip(0, 0, 200, 50, 35)\n\t *     .fill()\n\t *     .png();\n\t *\n\t * @example\n\t * // Configured bevels\n\t * new Canvas(200, 200)\n\t *     .createRoundedClip(0, 0, 200, 50, {\n\t *         // Top left border\n\t *         tl: 15,\n\t *         // Top right border\n\t *         tr: 20,\n\t *         // Bottom left border\n\t *         bl: 5,\n\t *         // Bottom right border\n\t *         br: 10\n\t *     })\n\t *     // Add an image with the shape of the beveled clip using different borders\n\t *     .printImage(buffer, 0, 0, 200, 50)\n\t *     .png();\n\t *\n\t * @example\n\t * // Top bevels only\n\t * new Canvas(200, 200)\n\t *     .createRoundedClip(0, 0, 200, 50, { tl: 20, tr: 20, bl: 0, br: 0 })\n\t *     .printImage(buffer, 0, 0, 200, 50)\n\t *     .png();\n\t */\n\tpublic createRoundedClip(x: number, y: number, width: number, height: number, radius: number | BeveledRadiusOptions): this {\n\t\treturn this.createRoundedPath(x, y, width, height, radius).clip();\n\t}\n\n\t/**\n\t * Set a color for the canvas' context.\n\t * @param color A canvas' color resolvable.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/fillStyle\n\t */\n\tpublic setColor(color: string | CanvasGradient | CanvasPattern): this {\n\t\tthis.context.fillStyle = color;\n\t\treturn this;\n\t}\n\n\t/**\n\t * Change the font.\n\t * @param font The font's name to set.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/font\n\t */\n\tpublic setTextFont(font: string): this {\n\t\tthis.context.font = font;\n\t\treturn this;\n\t}\n\n\t/**\n\t * Change the font alignment.\n\t * @param align The font's alignment to set.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/textAlign\n\t */\n\tpublic setTextAlign(align: CanvasTextAlign): this {\n\t\tthis.context.textAlign = align;\n\t\treturn this;\n\t}\n\n\t/**\n\t * Change the font's baseline.\n\t * @param baseline The font's baseline to set.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/textBaseline\n\t */\n\tpublic setTextBaseline(baseline: CanvasTextBaseline): this {\n\t\tthis.context.textBaseline = baseline;\n\t\treturn this;\n\t}\n\n\t/**\n\t * Change the canvas's filters.\n\t * @param filter The filter to set.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/filter\n\t */\n\tpublic setFilter(filter: string): this {\n\t\tthis.context.filter = filter;\n\t\treturn this;\n\t}\n\n\t/**\n\t * Starts a new path by emptying the list of sub-paths.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/beginPath\n\t */\n\tpublic beginPath(): this {\n\t\tthis.context.beginPath();\n\t\treturn this;\n\t}\n\n\t/**\n\t * Causes the point of the pen to move back to the start of the current sub-path.\n\t * If the shape has already been closed or has only one point, this function does nothing.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/closePath\n\t */\n\tpublic closePath(): this {\n\t\tthis.context.closePath();\n\t\treturn this;\n\t}\n\n\t/**\n\t * Creates a pattern using the specified image. It repeats the source in the directions specified by the repetition\n\t * argument, and returns it.\n\t * @param image A Canvas Image to be used as the image to repeat.\n\t * @param repetition The repeat mode.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/createPattern\n\t */\n\tpublic createPattern(image: PatternResolvable, repetition: PatternRepeat): CanvasPattern {\n\t\treturn this.context.createPattern(_resolvePattern(image), repetition)!;\n\t}\n\n\t/**\n\t * Creates a pattern using the specified image. It repeats the source in the directions specified by the repetition\n\t * argument, and prints it.\n\t * @param image A Canvas Image to be used as the image to repeat.\n\t * @param repetition The repeat mode.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/createPattern\n\t */\n\tpublic printPattern(image: PatternResolvable, repetition: PatternRepeat): this {\n\t\treturn this.setColor(this.createPattern(image, repetition));\n\t}\n\n\t/**\n\t * Creates a gradient along the line given by the coordinates represented by the parameters.\n\t * The coordinates are global, the second point does not rely on the position of the first and vice versa.\n\t * @param x0 The x axis of the coordinate of the start point.\n\t * @param y0 The y axis of the coordinate of the start point.\n\t * @param x1 The x axis of the coordinate of the end point.\n\t * @param y1 The y axis of the coordinate of the end point.\n\t * @param steps The gradient steps.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/createLinearGradient\n\t */\n\tpublic createLinearGradient(x0: number, y0: number, x1: number, y1: number, steps: readonly GradientStop[] = []): CanvasGradient {\n\t\tconst gradient = this.context.createLinearGradient(x0, y0, x1, y1);\n\t\tfor (const step of steps) {\n\t\t\tgradient.addColorStop(step.position, step.color);\n\t\t}\n\n\t\treturn gradient;\n\t}\n\n\t/**\n\t * Creates a gradient along the line given by the coordinates represented by the parameters.\n\t * The coordinates are global, the second point does not rely on the position of the first and vice versa. This\n\t * method is chainable and calls setColor after creating the gradient.\n\t * @param x0 The x axis of the coordinate of the start point.\n\t * @param y0 The y axis of the coordinate of the start point.\n\t * @param x1 The x axis of the coordinate of the end point.\n\t * @param y1 The y axis of the coordinate of the end point.\n\t * @param steps The gradient steps.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/createLinearGradient\n\t * @example\n\t * new Canvas(200, 200)\n\t *     .printLinearColorGradient(0, 0, 200, 50, [\n\t *         { position: 0, color: 'white' },\n\t *         { position: 0.25, color: 'red' },\n\t *         { position: 0.5, color: 'blue' }\n\t *     ])\n\t *     .printRectangle(10, 10, 200, 100)\n\t */\n\tpublic printLinearColorGradient(x0: number, y0: number, x1: number, y1: number, steps?: readonly GradientStop[]): this {\n\t\tconst gradient = this.createLinearGradient(x0, y0, x1, y1, steps);\n\t\treturn this.setColor(gradient);\n\t}\n\n\t/**\n\t * Creates a gradient along the line given by the coordinates represented by the parameters.\n\t * The coordinates are global, the second point does not rely on the position of the first and vice versa. This\n\t * method is chainable and calls setStroke after creating the gradient.\n\t * @param x0 The x axis of the coordinate of the start point.\n\t * @param y0 The y axis of the coordinate of the start point.\n\t * @param x1 The x axis of the coordinate of the end point.\n\t * @param y1 The y axis of the coordinate of the end point.\n\t * @param steps The gradient steps.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/createLinearGradient\n\t * @example\n\t * new Canvas(200, 200)\n\t *     .printLinearStrokeGradient(0, 0, 200, 50, [\n\t *         { position: 0, color: 'white' },\n\t *         { position: 0.25, color: 'red' },\n\t *         { position: 0.5, color: 'blue' }\n\t *     ])\n\t *     .printRectangle(10, 10, 200, 100)\n\t */\n\tpublic printLinearStrokeGradient(x0: number, y0: number, x1: number, y1: number, steps?: readonly GradientStop[]): this {\n\t\tconst gradient = this.createLinearGradient(x0, y0, x1, y1, steps);\n\t\treturn this.setStroke(gradient);\n\t}\n\n\t/**\n\t * Creates a radial gradient given by the coordinates of the two circles represented by the parameters.\n\t * @param x0 The x axis of the coordinate of the start circle.\n\t * @param y0 The y axis of the coordinate of the start circle.\n\t * @param r0 The radius of the start circle.\n\t * @param x1 The x axis of the coordinate of the end circle.\n\t * @param y1 The y axis of the coordinate of the end circle.\n\t * @param r1 The radius of the end circle.\n\t * @param steps The gradient steps.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/createRadialGradient\n\t */\n\tpublic createRadialGradient(\n\t\tx0: number,\n\t\ty0: number,\n\t\tr0: number,\n\t\tx1: number,\n\t\ty1: number,\n\t\tr1: number,\n\t\tsteps: readonly GradientStop[] = []\n\t): CanvasGradient {\n\t\tconst gradient = this.context.createRadialGradient(x0, y0, r0, x1, y1, r1);\n\t\tfor (const step of steps) {\n\t\t\tgradient.addColorStop(step.position, step.color);\n\t\t}\n\n\t\treturn gradient;\n\t}\n\n\t/**\n\t * Creates a radial gradient given by the coordinates of the two circles represented by the parameters. This\n\t * method is chainable and calls setColor after creating the gradient.\n\t * @param x0 The x axis of the coordinate of the start circle.\n\t * @param y0 The y axis of the coordinate of the start circle.\n\t * @param r0 The radius of the start circle.\n\t * @param x1 The x axis of the coordinate of the end circle.\n\t * @param y1 The y axis of the coordinate of the end circle.\n\t * @param r1 The radius of the end circle.\n\t * @param steps The gradient steps.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/createRadialGradient\n\t */\n\tpublic printRadialColorGradient(x0: number, y0: number, r0: number, x1: number, y1: number, r1: number, steps?: readonly GradientStop[]): this {\n\t\tconst gradient = this.createRadialGradient(x0, y0, r0, x1, y1, r1, steps);\n\t\treturn this.setColor(gradient);\n\t}\n\n\t/**\n\t * Creates a radial gradient given by the coordinates of the two circles represented by the parameters. This\n\t * method is chainable and calls setStroke after creating the gradient.\n\t * @param x0 The x axis of the coordinate of the start circle.\n\t * @param y0 The y axis of the coordinate of the start circle.\n\t * @param r0 The radius of the start circle.\n\t * @param x1 The x axis of the coordinate of the end circle.\n\t * @param y1 The y axis of the coordinate of the end circle.\n\t * @param r1 The radius of the end circle.\n\t * @param steps The gradient steps.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/createRadialGradient\n\t */\n\tpublic printRadialStrokeGradient(x0: number, y0: number, r0: number, x1: number, y1: number, r1: number, steps?: readonly GradientStop[]): this {\n\t\tconst gradient = this.createRadialGradient(x0, y0, r0, x1, y1, r1, steps);\n\t\treturn this.setStroke(gradient);\n\t}\n\n\t/**\n\t * Creates a radial gradient around a point with given coordinates.\n\t * @param startAngle The angle at which to begin the gradient, in radians. Angle measurements start vertically\n\t * above the centre and move around clockwise.\n\t * @param x The x-axis coordinate of the centre of the gradient.\n\t * @param y The y-axis coordinate of the centre of the gradient.\n\t * @param steps The gradient steps.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/createConicGradient\n\t */\n\tpublic createConicGradient(startAngle: number, x: number, y: number, steps: readonly GradientStop[] = []): CanvasGradient {\n\t\tconst gradient = this.context.createConicGradient(startAngle, x, y);\n\t\tfor (const step of steps) {\n\t\t\tgradient.addColorStop(step.position, step.color);\n\t\t}\n\n\t\treturn gradient;\n\t}\n\n\t/**\n\t * Creates a radial gradient given by the coordinates of the two circles represented by the parameters. This\n\t * method is chainable and calls setColor after creating the gradient.\n\t * @param startAngle The angle at which to begin the gradient, in radians. Angle measurements start vertically\n\t * above the centre and move around clockwise.\n\t * @param x The x-axis coordinate of the centre of the gradient.\n\t * @param y The y-axis coordinate of the centre of the gradient.\n\t * @param steps The gradient steps.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/createConicGradient\n\t */\n\tpublic printConicColorGradient(startAngle: number, x: number, y: number, steps?: readonly GradientStop[]): this {\n\t\tconst gradient = this.createConicGradient(startAngle, x, y, steps);\n\t\treturn this.setColor(gradient);\n\t}\n\n\t/**\n\t * Creates a radial gradient around a point with given coordinates. This method is chainable and calls setStroke\n\t * after creating the gradient.\n\t * @param startAngle The angle at which to begin the gradient, in radians. Angle measurements start vertically\n\t * above the centre and move around clockwise.\n\t * @param x The x-axis coordinate of the centre of the gradient.\n\t * @param y The y-axis coordinate of the centre of the gradient.\n\t * @param steps The gradient steps.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/createConicGradient\n\t */\n\tpublic printConicStrokeGradient(startAngle: number, x: number, y: number, steps?: readonly GradientStop[]): this {\n\t\tconst gradient = this.createConicGradient(startAngle, x, y, steps);\n\t\treturn this.setStroke(gradient);\n\t}\n\n\t/**\n\t * Adds an ellipse to the path which is centered at (X, Y) position with the radius radiusX and radiusY starting at\n\t * startAngle and ending at endAngle going in the given direction by anticlockwise (defaulting to clockwise).\n\t * @param x The x axis of the coordinate for the ellipse's center.\n\t * @param y The y axis of the coordinate for the ellipse's center.\n\t * @param radiusX The ellipse's major-axis radius.\n\t * @param radiusY The ellipse's minor-axis radius.\n\t * @param rotation The rotation for this ellipse, expressed in radians.\n\t * @param startAngle The starting point, measured from the x axis, from which it will be drawn, expressed in radians.\n\t * @param endAngle The end ellipse's angle to which it will be drawn, expressed in radians.\n\t * @param anticlockwise An optional Boolean which, if true, draws the ellipse anticlockwise (counter-clockwise), otherwise in a clockwise direction.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/ellipse\n\t */\n\tpublic createEllipsePath(\n\t\tx: number,\n\t\ty: number,\n\t\tradiusX: number,\n\t\tradiusY: number,\n\t\trotation: number,\n\t\tstartAngle: number,\n\t\tendAngle: number,\n\t\tanticlockwise?: boolean\n\t): this {\n\t\tthis.context.ellipse(x, y, radiusX, radiusY, rotation, startAngle, endAngle, anticlockwise);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Creates an ellipse clip which is centered at (X, Y) position with the radius radiusX and radiusY starting at\n\t * startAngle and ending at endAngle going in the given direction by anticlockwise (defaulting to clockwise).\n\t * @param x The x axis of the coordinate for the ellipse's center.\n\t * @param y The y axis of the coordinate for the ellipse's center.\n\t * @param radiusX The ellipse's major-axis radius.\n\t * @param radiusY The ellipse's minor-axis radius.\n\t * @param rotation The rotation for this ellipse, expressed in radians.\n\t * @param startAngle The starting point, measured from the x axis, from which it will be drawn, expressed in radians.\n\t * @param endAngle The end ellipse's angle to which it will be drawn, expressed in radians.\n\t * @param anticlockwise An optional Boolean which, if true, draws the ellipse anticlockwise (counter-clockwise), otherwise in a clockwise direction.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/ellipse\n\t */\n\tpublic createEllipseClip(\n\t\tx: number,\n\t\ty: number,\n\t\tradiusX: number,\n\t\tradiusY: number,\n\t\trotation: number,\n\t\tstartAngle: number,\n\t\tendAngle: number,\n\t\tanticlockwise?: boolean\n\t): this {\n\t\treturn this.createEllipsePath(x, y, radiusX, radiusY, rotation, startAngle, endAngle, anticlockwise).clip();\n\t}\n\n\t/**\n\t * Adds an arc to the path which is centered at (X, Y) position with radius r starting at startAngle and ending at\n\t * endAngle going in the given direction by anticlockwise (defaulting to clockwise).\n\t * @param x The X coordinate of the arc's center.\n\t * @param y The Y coordinate of the arc's center.\n\t * @param radius The arc's radius.\n\t * @param startAngle The angle at which the arc starts, measured clockwise from the positive x axis and expressed in radians.\n\t * @param endAngle The angle at which the arc ends, measured clockwise from the positive x axis and expressed in radians.\n\t * @param anticlockwise An optional Boolean which, if true, causes the arc to be drawn counter-clockwise between the two angles. By default it is drawn clockwise.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/arc\n\t */\n\tpublic arc(x: number, y: number, radius: number, startAngle: number, endAngle: number, anticlockwise?: boolean): this {\n\t\tthis.context.arc(x, y, radius, startAngle, endAngle, anticlockwise);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Adds an arc to the path with the given control points and radius, connected to the previous point by a straight line.\n\t * @param x1 The x axis of the coordinate for the first control point.\n\t * @param y1 The y axis of the coordinate for the first control point.\n\t * @param x2 The x axis of the coordinate for the second control point.\n\t * @param y2 The y axis of the coordinate for the second control point.\n\t * @param radius The arc's radius.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/arcTo\n\t */\n\tpublic arcTo(x1: number, y1: number, x2: number, y2: number, radius: number): this {\n\t\tthis.context.arcTo(x1, y1, x2, y2, radius);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Adds a quadratic Bézier curve to the path. It requires two points. The first point is a control point and the\n\t * second one is the end point. The starting point is the last point in the current path, which can be changed using\n\t * moveTo() before creating the quadratic Bézier curve.\n\t * @param cpx The x axis of the coordinate for the control point.\n\t * @param cpy The y axis of the coordinate for the control point.\n\t * @param x The x axis of the coordinate for the end point.\n\t * @param y The y axis of the coordinate for the end point.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/quadraticCurveTo\n\t */\n\tpublic quadraticCurveTo(cpx: number, cpy: number, x: number, y: number): this {\n\t\tthis.context.quadraticCurveTo(cpx, cpy, x, y);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Adds a cubic Bézier curve to the path. It requires three points. The first two points are control points and the\n\t * third one is the end point. The starting point is the last point in the current path, which can be changed using\n\t * moveTo() before creating the Bézier curve.\n\t * @param cp1x The x axis of the coordinate for the first control point.\n\t * @param cp1y The y axis of the coordinate for first control point.\n\t * @param cp2x The x axis of the coordinate for the second control point.\n\t * @param cp2y The y axis of the coordinate for the second control point.\n\t * @param x The x axis of the coordinate for the end point.\n\t * @param y The y axis of the coordinate for the end point.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/bezierCurveTo\n\t */\n\tpublic bezierCurveTo(cp1x: number, cp1y: number, cp2x: number, cp2y: number, x: number, y: number): this {\n\t\tthis.context.bezierCurveTo(cp1x, cp1y, cp2x, cp2y, x, y);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Connects the last point in the sub-path to the x, y coordinates with a straight line\n\t * @param x The x axis of the coordinate for the end of the line.\n\t * @param y The y axis of the coordinate for the end of the line.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/lineTo\n\t */\n\tpublic lineTo(x: number, y: number): this {\n\t\tthis.context.lineTo(x, y);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Moves the starting point of a new sub-path to the (X, Y) coordinates.\n\t * @param x The x axis of the point.\n\t * @param y The y axis of the point.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/moveTo\n\t */\n\tpublic moveTo(x: number, y: number): this {\n\t\tthis.context.moveTo(x, y);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Set the shadow's blur.\n\t * @param radius The shadow's blur radius to set.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/shadowBlur\n\t */\n\tpublic setShadowBlur(radius: number): this {\n\t\tthis.context.shadowBlur = radius;\n\t\treturn this;\n\t}\n\n\t/**\n\t * Set the shadow's color.\n\t * @param color A canvas' color resolvable to set as shadow's color.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/shadowColor\n\t */\n\tpublic setShadowColor(color: string): this {\n\t\tthis.context.shadowColor = color;\n\t\treturn this;\n\t}\n\n\t/**\n\t * Set the property that specifies the distance that the shadow will be offset in horizontal distance.\n\t * @param value The value in pixels for the distance.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/shadowOffsetX\n\t */\n\tpublic setShadowOffsetX(value: number): this {\n\t\tthis.context.shadowOffsetX = value;\n\t\treturn this;\n\t}\n\n\t/**\n\t * Set the property that specifies the distance that the shadow will be offset in vertical distance.\n\t * @param value The value in pixels for the distance.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/shadowOffsetY\n\t */\n\tpublic setShadowOffsetY(value: number): this {\n\t\tthis.context.shadowOffsetY = value;\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the miter limit ratio in space units. When getting, it returns the current value (10.0 by default). When\n\t * setting, zero, negative, Infinity and NaN values are ignored; otherwise the current value is set to the new value.\n\t * @param value A number specifying the miter limit ratio in space units. Zero, negative, Infinity and NaN values\n\t * are ignored.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/miterLimit\n\t */\n\tpublic setMiterLimit(value: number): this {\n\t\tthis.context.miterLimit = value;\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the type of compositing operation to apply when drawing new shapes, where type is a string identifying which\n\t * of the compositing or blending mode operations to use.\n\t * @param type The global composite operation mode.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/globalCompositeOperation\n\t */\n\tpublic setGlobalCompositeOperation(type: GlobalCompositeOperation): this {\n\t\tthis.context.globalCompositeOperation = type;\n\t\treturn this;\n\t}\n\n\t/**\n\t * Modify the alpha value that is applied to shapes and images before they are drawn into the canvas.\n\t * @param value The alpha value, from 0.0 (fully transparent) to 1.0 (fully opaque)\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/globalAlpha\n\t */\n\tpublic setGlobalAlpha(value: number): this {\n\t\tthis.context.globalAlpha = value;\n\t\treturn this;\n\t}\n\n\t/**\n\t * Modify whether or not image smoothing should be enabled.\n\t * @param value Whether or not image smoothing should be enabled.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/imageSmoothingEnabled\n\t */\n\tpublic setImageSmoothingEnabled(value: boolean): this {\n\t\tthis.context.imageSmoothingEnabled = value;\n\t\treturn this;\n\t}\n\n\t/**\n\t * Modify the smoothing quality value.\n\t * @param value The smoothing quality value.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/imageSmoothingEnabled\n\t */\n\tpublic setImageSmoothingQuality(value: ImageSmoothingQuality): this {\n\t\tthis.context.imageSmoothingQuality = value;\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the canvas's CSS3 [font-variant](https://developer.mozilla.org/en-US/docs/Web/CSS/font-variant).\n\t * @param fontVariant The CSS3 [font-variant](https://developer.mozilla.org/en-US/docs/Web/CSS/font-variant) value\n\t * to be set.\n\t * @note The font-variant does not persist between font changes. Additionally, you can use {@link fontVariant}.\n\t * @note This is a `skia-canvas` extension.\n\t */\n\tpublic setFontVariant(fontVariant: FontVariantString): this {\n\t\tthis.context.fontVariant = fontVariant;\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the text tracking property in the canvas.\n\t * @param textTracking An integer representing the amount of space to add/remove in terms of 1/1000’s of an ‘em’\n\t * (a.k.a. the current font size). Positive numbers will space out the text (e.g., `100` is a good value for\n\t * setting all-caps) while negative values will pull the letters closer together (this is only rarely a good\n\t * idea).\n\t * @note The tracking value defaults to `0` and settings will persist across font changes.\n\t * @note This is a `skia-canvas` extension.\n\t */\n\tpublic setTextTracking(textTracking: number): this {\n\t\tthis.context.textTracking = textTracking;\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets whether or not Skia's text-wrap system should be enabled. Setting to `true` has the following effects:\n\t *\n\t * - {@link printText} will honor newlines as opposed to converting them to spaces.\n\t * - {@link printText}'s width argument will be interpreted as column width and will word-wrap long lines.\n\t * - {@link printStrokeText}'s width argument will be interpreted as column width and will word-wrap long lines.\n\t *\n\t * However, when set to `false` (default), the text-drawing methods will never choose a more-condensed weight or\n\t * otherwise attempt to squeeze your entire string into the measure specified by `width`. Instead the text will be\n\t * typeset up through the last word that fits and the rest will be omitted.\n\t * @param value Whether text wrap should be enabled or not.\n\t * @note This is a `skia-canvas` extension.\n\t */\n\tpublic setTextWrap(value: boolean): this {\n\t\tthis.context.textWrap = value;\n\t\treturn this;\n\t}\n\n\t/**\n\t * Sets the shadow blur and offsets to zero, then sets the shadow color to transparent. If shadows are not longer\n\t * used in a canvas and performance is critical, `.setShadowColor('transparent')` should be used instead, as of the\n\t * [note from Mozilla Developer Network](https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/shadowColor).\n\t * @example\n\t * new Canvas(500, 500)\n\t *     // Set a shadow color and blur\n\t *     .setShadowColor('rgba(23, 23, 23, 0.2)')\n\t *     .setShadowBlur(5)\n\t *     // Render the text with a blow effect\n\t *     .printText('Hello', 30, 50)\n\t *     // Reset the shadows\n\t *     .resetShadows()\n\t *     // Render the text without shadows\n\t *     .printText('World!', 30, 100);\n\t */\n\tpublic resetShadows(): this {\n\t\treturn this.setShadowBlur(0).setShadowOffsetX(0).setShadowOffsetY(0).setShadowColor('transparent');\n\t}\n\n\t/**\n\t * Clear a circle.\n\t * @param x The position x in the center of the clip's circle.\n\t * @param y The position y in the center of the clip's circle.\n\t * @param radius The radius for the clip.\n\t * @param start The degree in radians to start drawing the circle.\n\t * @param angle The degree in radians to finish drawing the circle, defaults to a full circle.\n\t * @param antiClockwise Whether or not the angle should be anti-clockwise.\n\t */\n\tpublic clearCircle(x: number, y: number, radius: number, start = 0, angle = Math.PI * 2, antiClockwise = false): this {\n\t\treturn this.createCircularClip(x, y, radius, start, angle, antiClockwise).clearRectangle(x - radius, y - radius, radius * 2, radius * 2);\n\t}\n\n\t/**\n\t * Clear an area.\n\t * @param dx The position x to start drawing the element.\n\t * @param dy The position y to start drawing the element.\n\t * @param width The width of the element.\n\t * @param height The height of the element.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/clearRect\n\t */\n\tpublic clearRectangle(dx = 0, dy = 0, width = this.width, height = this.height): this {\n\t\tthis.context.clearRect(dx, dy, width, height);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Reports whether or not the specified point is contained in the current path.\n\t * @param x The X coordinate of the point to check.\n\t * @param y The Y coordinate of the point to check.\n\t * @param fillRule The algorithm by which to determine if a point is inside a path or outside a path.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/isPointInPath\n\t */\n\tpublic isPointInPath(x: number, y: number, fillRule?: CanvasFillRule): boolean;\n\t/**\n\t * Reports whether or not the specified point is contained in the given path.\n\t * @param path The {@link Path2D} to check against.\n\t * @param x The X coordinate of the point to check.\n\t * @param y The Y coordinate of the point to check.\n\t * @param fillRule The algorithm by which to determine if a point is inside a path or outside a path.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/isPointInPath\n\t */\n\tpublic isPointInPath(path: Path2D, x: number, y: number, fillRule?: CanvasFillRule): boolean;\n\tpublic isPointInPath(...args: [any, any]): boolean {\n\t\treturn this.context.isPointInPath(...args);\n\t}\n\n\t/**\n\t * Reports whether or not the specified point is inside the area contained by the stroking of the current path.\n\t * @param x The X coordinate of the point to check.\n\t * @param y The Y coordinate of the point to check.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/isPointInStroke\n\t */\n\tpublic isPointInStroke(x: number, y: number): boolean;\n\t/**\n\t * Reports whether or not the specified point is inside the area contained by the stroking of the given path.\n\t * @param path The {@link Path2D} to check against.\n\t * @param x The X coordinate of the point to check.\n\t * @param y The Y coordinate of the point to check.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/isPointInStroke\n\t */\n\tpublic isPointInStroke(path: Path2D, x: number, y: number): boolean;\n\tpublic isPointInStroke(...args: [any, any]): boolean {\n\t\treturn this.context.isPointInStroke(...args);\n\t}\n\n\t/**\n\t * Process data with this as the context\n\t * @param fn A callback function\n\t * @param args Extra arguments to pass to the function\n\t */\n\tpublic process<Args extends readonly any[]>(fn: (this: this, canvas: this, ...args: Args) => unknown, ...args: Args): this {\n\t\tfn.call(this, this, ...args);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Wraps a text into a width-limited multi-line text.\n\t * @param text The text to wrap\n\t * @param wrapWidth The wrap width\n\t * @example\n\t * // Calculate the wrapped text and return it, which\n\t * // is useful for storage to avoid re-calculating the\n\t * // wrapped text\n\t * const wrappedText = new Canvas(500, 300)\n\t *     .setTextFont('48px Verdana')\n\t *     .wrapText('Hello World, this is a quite\\nlong text.', 300);\n\t * @example\n\t * // Wrap the text and add it\n\t * const buffer = new Canvas(500, 300)\n\t *     .setTextFont('48px Verdana')\n\t *     .process((canvas) => {\n\t *         const wrappedText = canvas.wrapText('Hello World, this is a quite\\nlong text.');\n\t *         return canvas\n\t *             .setTextAlign('center')\n\t *             .addMultilineText(wrappedText, 250, 50)\n\t *     })\n\t *     .png(); // Returns a Buffer\n\t */\n\tpublic wrapText(text: string, wrapWidth: number): string {\n\t\treturn textWrap(this, text, wrapWidth);\n\t}\n\n\t/**\n\t * Creates a new, blank {@link ImageData} object with the specified dimensions. All of the pixels in the new object are transparent black.\n\t * @param sw The width to give the new {@link ImageData} object. A negative value flips the rectangle around the vertical axis.\n\t * @param sh The height to give the new {@link ImageData} object. A negative value flips the rectangle around the horizontal axis.\n\t * @param settings The settings to be used.\n\t */\n\tpublic createImageData(sw: number, sh: number, settings?: ImageDataSettings): ImageData;\n\t/**\n\t * Creates a new, blank {@link ImageData} object with the dimensions of the specified object. All of the pixels in the new object are transparent black.\n\t * @param imageData An existing {@link ImageData} object from which to copy the width and height. The image itself is not copied.\n\t */\n\tpublic createImageData(imageData: ImageData): ImageData;\n\tpublic createImageData(...args: [any]): ImageData {\n\t\treturn this.context.createImageData(...args);\n\t}\n\n\t/**\n\t * Gets a JPEG buffer.\n\t * @param options The render options.\n\t * @returns A JPEG buffer.\n\t * @see {@link jpegAsync} for the async version.\n\t */\n\tpublic jpeg(options?: RenderOptions): Buffer {\n\t\treturn this.canvas.toBufferSync('jpeg', options);\n\t}\n\n\t/**\n\t * Gets a JPEG buffer.\n\t * @param options The render options.\n\t * @returns A JPEG buffer.\n\t */\n\tpublic jpegAsync(options?: RenderOptions): Promise<Buffer> {\n\t\treturn this.canvas.toBuffer('jpeg', options);\n\t}\n\n\t/**\n\t * Gets a PNG buffer.\n\t * @param options The render options.\n\t * @returns A PNG buffer.\n\t * @see {@link pngAsync} for the async version.\n\t */\n\tpublic png(options?: RenderOptions): Buffer {\n\t\treturn this.canvas.toBufferSync('png', options);\n\t}\n\n\t/**\n\t * Gets a PNG buffer.\n\t * @param options The render options.\n\t * @returns A PNG buffer.\n\t * @see {@link png} for the sync version.\n\t */\n\tpublic pngAsync(options?: RenderOptions): Promise<Buffer> {\n\t\treturn this.canvas.toBuffer('png', options);\n\t}\n\n\t/**\n\t * Gets a PDF buffer.\n\t * @param options The render options.\n\t * @returns A PDF buffer.\n\t * @see {@link pdfAsync} for the async version.\n\t */\n\tpublic pdf(options?: RenderOptions): Buffer {\n\t\treturn this.canvas.toBufferSync('pdf', options);\n\t}\n\n\t/**\n\t * Gets a PDF buffer.\n\t * @param options The render options.\n\t * @returns A PDF buffer.\n\t */\n\tpublic pdfAsync(options?: RenderOptions): Promise<Buffer> {\n\t\treturn this.canvas.toBuffer('pdf', options);\n\t}\n\n\t/**\n\t * Gets a SVG buffer.\n\t * @param options The render options.\n\t * @returns A SVG buffer.\n\t * @see {@link svgAsync} for the async version.\n\t */\n\tpublic svg(options?: RenderOptions): Buffer {\n\t\treturn this.canvas.toBufferSync('svg', options);\n\t}\n\n\t/**\n\t * Gets a SVG buffer.\n\t * @param options The render options.\n\t * @returns A SVG buffer.\n\t */\n\tpublic svgAsync(options?: RenderOptions): Promise<Buffer> {\n\t\treturn this.canvas.toBuffer('svg', options);\n\t}\n\n\t/**\n\t * Renders the canvas into a buffer with the specified format.\n\t * @param format The format to use for the image. An `@` suffix can be added to the format string to specify a\n\t * pixel-density (for instance, \"jpg@2x\").\n\t * @param options The render options.\n\t */\n\tpublic toBuffer(format: RenderImageFormat, options?: RenderOptions): Buffer;\n\tpublic toBuffer(...args: [any]): Buffer {\n\t\treturn this.canvas.toBufferSync(...args);\n\t}\n\n\t/**\n\t * Renders the canvas into a buffer with the specified format.\n\t * @param format The format to use for the image. An `@` suffix can be added to the format string to specify a\n\t * pixel-density (for instance, \"jpg@2x\").\n\t * @param options The render options.\n\t */\n\tpublic toBufferAsync(format: RenderImageFormat, options?: RenderOptions): Promise<Buffer>;\n\tpublic toBufferAsync(...args: [any]): Promise<Buffer> {\n\t\treturn this.canvas.toBuffer(...args);\n\t}\n\n\t/**\n\t * Render the canvas into a data URL with the specified format.\n\t * @param format The image format the data URL must have.\n\t * @param options The render options.\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/HTMLCanvasElement/toDataURL\n\t */\n\tpublic toDataURL(format: RenderImageFormat, options?: RenderOptions): Promise<string>;\n\tpublic toDataURL(...args: [any]): Promise<string> {\n\t\treturn this.canvas.toDataURL(...args);\n\t}\n\n\t/**\n\t * Takes a file path and writes the canvas's current contents to disk. If the filename ends with an extension that\n\t * makes its format clear, the second argument is optional. If the filename is ambiguous, you can pass an options\n\t * object with a format string using names like \"png\" and \"jpeg\" or a full mime type like \"application/pdf\".\n\t * @param filename The way multi-page documents are handled depends on the filename argument. If the filename\n\t * contains the string \"{}\", it will be used as template for generating a numbered sequence of files—one per page.\n\t * If no curly braces are found in the filename, only a single file will be saved. That single file will be\n\t * multi-page in the case of PDF output but for other formats it will contain only the most recently added page.\n\t *\n\t * An integer can optionally be placed between the braces to indicate the number of padding characters to use for\n\t * numbering. For instance \"page-{}.svg\" will generate files of the form page-1.svg whereas \"frame-{4}.png\" will\n\t * generate files like frame-0001.png.\n\t * @param options The options for the image render.\n\t */\n\tpublic saveAs(filename: string, options?: SaveAsOptions): this;\n\tpublic saveAs(...args: readonly any[]): this {\n\t\t// @ts-expect-error: Complains about invalid overload (expects more than 0 overloads).\n\t\tthis.canvas.saveAsSync(...args);\n\t\treturn this;\n\t}\n\n\t/**\n\t * Takes a file path and writes the canvas's current contents to disk. If the filename ends with an extension that\n\t * makes its format clear, the second argument is optional. If the filename is ambiguous, you can pass an options\n\t * object with a format string using names like \"png\" and \"jpeg\" or a full mime type like \"application/pdf\".\n\t * @param filename The way multi-page documents are handled depends on the filename argument. If the filename\n\t * contains the string \"{}\", it will be used as template for generating a numbered sequence of files—one per page.\n\t * If no curly braces are found in the filename, only a single file will be saved. That single file will be\n\t * multi-page in the case of PDF output but for other formats it will contain only the most recently added page.\n\t *\n\t * An integer can optionally be placed between the braces to indicate the number of padding characters to use for\n\t * numbering. For instance \"page-{}.svg\" will generate files of the form page-1.svg whereas \"frame-{4}.png\" will\n\t * generate files like frame-0001.png.\n\t * @param options The options for the image render.\n\t */\n\tpublic saveAsAsync(filename: string, options?: SaveAsOptions): Promise<this>;\n\tpublic async saveAsAsync(...args: readonly any[]): Promise<this> {\n\t\t// @ts-expect-error: Complains about invalid overload (expects more than 0 overloads).\n\t\tawait this.canvas.saveAs(...args);\n\t\treturn this;\n\t}\n\n\tprotected parseFont(font: string) {\n\t\tconst result = fontRegExp.exec(font);\n\t\tif (result === null) return [font] as const;\n\n\t\treturn [font.slice(0, result.index), Number(result[1]), font.slice(result.index + result[1].length)] as const;\n\t}\n\n\tprotected resolveCircularCoordinates(\n\t\timageOrBuffer: ImageResolvable,\n\t\tx: number,\n\t\ty: number,\n\t\tradius: number,\n\t\tfit: NonNullable<PrintCircularOptions['fit']>\n\t): ResolvedCircularCoordinates {\n\t\tconst { width: w, height: h } = imageOrBuffer;\n\t\tif (fit === 'none') {\n\t\t\treturn {\n\t\t\t\tpositionX: x - w / 2,\n\t\t\t\tpositionY: y - h / 2,\n\t\t\t\tsizeX: w,\n\t\t\t\tsizeY: h\n\t\t\t};\n\t\t}\n\n\t\tconst ratio = w / h;\n\t\tconst diameter = radius * 2;\n\n\t\tif (fit === 'fill' || ratio === 1) {\n\t\t\treturn {\n\t\t\t\tpositionX: x - radius,\n\t\t\t\tpositionY: y - radius,\n\t\t\t\tsizeX: diameter,\n\t\t\t\tsizeY: diameter\n\t\t\t};\n\t\t}\n\n\t\tif (fit === 'contain') {\n\t\t\treturn ratio > 1\n\t\t\t\t? {\n\t\t\t\t\t\tpositionX: x - radius,\n\t\t\t\t\t\tpositionY: y - radius / ratio,\n\t\t\t\t\t\tsizeX: diameter,\n\t\t\t\t\t\tsizeY: diameter / ratio\n\t\t\t\t  }\n\t\t\t\t: {\n\t\t\t\t\t\tpositionX: x - radius * ratio,\n\t\t\t\t\t\tpositionY: y - radius,\n\t\t\t\t\t\tsizeX: diameter * ratio,\n\t\t\t\t\t\tsizeY: diameter\n\t\t\t\t  };\n\t\t}\n\n\t\tif (ratio > 1) {\n\t\t\tconst sizeX = diameter * ratio;\n\t\t\tconst sizeY = diameter;\n\t\t\treturn {\n\t\t\t\tpositionX: x - sizeX / 2,\n\t\t\t\tpositionY: y - sizeY / 2,\n\t\t\t\tsizeX,\n\t\t\t\tsizeY\n\t\t\t};\n\t\t}\n\n\t\tconst sizeX = diameter;\n\t\tconst sizeY = diameter / ratio;\n\t\treturn {\n\t\t\tpositionX: x - sizeX / 2,\n\t\t\tpositionY: y - sizeY / 2,\n\t\t\tsizeX,\n\t\t\tsizeY\n\t\t};\n\t}\n}\n\ninterface ResolvedCircularCoordinates {\n\tpositionX: number;\n\tpositionY: number;\n\tsizeX: number;\n\tsizeY: number;\n}\n\nexport { Path2D, FontLibrary, NativeImage as Image, loadImage };\n\nexport const resolveImage = deprecate(loadImage, 'resolveImage() is deprecated. Use loadImage() instead.');\n\nexport function loadFont(familyName: string, fontPaths?: string | readonly string[]): Font[];\nexport function loadFont(fontPaths: readonly string[]): Font[];\nexport function loadFont(families: Record<string, readonly string[] | string>): Record<string, Font[] | Font>;\nexport function loadFont(...args: [any]) {\n\treturn FontLibrary.use(...args) as any;\n}\n\nexport const registerFont = deprecate(loadFont, 'registerFont() is deprecated. Use loadFont() instead.');\n\n/**\n * @see https://developer.mozilla.org/en-US/docs/Web/CSS/font-variant-ligatures\n */\nexport type FontVariantLigatures =\n\t| 'common-ligatures'\n\t| 'no-common-ligatures'\n\t| 'discretionary-ligatures'\n\t| 'no-discretionary-ligatures'\n\t| 'historical-ligatures'\n\t| 'no-historical-ligatures'\n\t| 'contextual'\n\t| 'no-contextual';\n\n/**\n * @see https://developer.mozilla.org/en-US/docs/Web/CSS/font-variant-alternates\n */\nexport type FontVariantAlternates =\n\t| 'historical-forms'\n\t| `stylistic(${string})`\n\t| `styleset(${string})`\n\t| `character-variant(${string})`\n\t| `swash(${string})`\n\t| `ornaments(${string})`\n\t| `annotation()${string}'`;\n\n/**\n * @see https://developer.mozilla.org/en-US/docs/Web/CSS/font-variant-caps\n */\nexport type FontVariantCaps = 'small-caps' | 'all-small-caps' | 'petite-caps';\n\n/**\n * @see https://developer.mozilla.org/en-US/docs/Web/CSS/font-variant-numeric\n */\nexport type FontVariantNumeric =\n\t| 'lining-nums'\n\t| 'oldstyle-nums'\n\t| 'proportional-nums'\n\t| 'tabular-nums'\n\t| 'diagonal-fractions'\n\t| 'stacked-fractions'\n\t| 'ordinal'\n\t| 'slashed-zero';\n\n/**\n * @see https://developer.mozilla.org/en-US/docs/Web/CSS/font-variant-east-asian\n */\nexport type FontVariantEastAsian =\n\t| 'jis78'\n\t| 'jis83'\n\t| 'jis90'\n\t| 'jis04'\n\t| 'simplified'\n\t| 'traditional'\n\t| 'full-width'\n\t| 'proportional-width'\n\t| 'ruby';\n\nexport type FontVariantString = 'normal' | 'none' | string;\n\nexport type FontVariants = FontVariantLigatures | FontVariantAlternates | FontVariantCaps | FontVariantNumeric | FontVariantEastAsian;\n\ntype GetFontVariant<K extends FontVariants> = K extends FontVariantLigatures\n\t? FontVariantLigatures\n\t: K extends FontVariantAlternates\n\t? FontVariantAlternates\n\t: K extends FontVariantCaps\n\t? FontVariantCaps\n\t: K extends FontVariantNumeric\n\t? FontVariantNumeric\n\t: FontVariantEastAsian;\n\nexport function fontVariant<K1 extends FontVariantString>(k1: K1): K1;\nexport function fontVariant<K1 extends FontVariants, K2 extends Exclude<FontVariants, GetFontVariant<K1>>>(k1: K1, k2: K2): `${K1} ${K2}`;\nexport function fontVariant<\n\tK1 extends FontVariants,\n\tK2 extends Exclude<FontVariants, GetFontVariant<K1>>,\n\tK3 extends Exclude<FontVariants, GetFontVariant<K2>>\n>(k1: K1, k2: K2, k3: K3): `${K1} ${K2} ${K3}`;\nexport function fontVariant<\n\tK1 extends FontVariants,\n\tK2 extends Exclude<FontVariants, GetFontVariant<K1>>,\n\tK3 extends Exclude<FontVariants, GetFontVariant<K2>>,\n\tK4 extends Exclude<FontVariants, GetFontVariant<K3>>\n>(k1: K1, k2: K2, k3: K3, k4: K4): `${K1} ${K2} ${K3} ${K4}`;\nexport function fontVariant<\n\tK1 extends FontVariants,\n\tK2 extends Exclude<FontVariants, GetFontVariant<K1>>,\n\tK3 extends Exclude<FontVariants, GetFontVariant<K2>>,\n\tK4 extends Exclude<FontVariants, GetFontVariant<K3>>,\n\tK5 extends Exclude<FontVariants, GetFontVariant<K4>>\n>(k1: K1, k2: K2, k3: K3, k4: K4, k5: K5): `${K1} ${K2} ${K3} ${K4} ${K5}`;\nexport function fontVariant(...args: readonly FontVariantString[]): string {\n\treturn args.join(' ');\n}\n\n// Start Section: Filters\n/**\n * Invert an image\n * @param canvas The Canvas instance\n */\nexport const invert = (canvas: Canvas) =>\n\tcanvas.save().setGlobalCompositeOperation('difference').setColor('white').printRectangle(0, 0, canvas.width, canvas.height).restore();\n\n/**\n * Greyscale an image\n * @param canvas The Canvas instance\n */\nexport const greyscale = (canvas: Canvas) => {\n\tconst imageData = canvas.getImageData();\n\tconst { data } = imageData;\n\tfor (let i = 0; i < data.length; i += 4) {\n\t\tconst luminance = 0.2126 * data[i] + 0.7152 * data[i + 1] + 0.0722 * data[i + 2];\n\t\tdata[i] = luminance;\n\t\tdata[i + 1] = luminance;\n\t\tdata[i + 2] = luminance;\n\t}\n\n\treturn canvas.putImageData(imageData, 0, 0);\n};\nexport const grayscale = greyscale;\n\n/**\n * Invert then greyscale an image\n * @param canvas The Canvas instance\n */\nexport const invertGrayscale = (canvas: Canvas) => {\n\tconst imageData = canvas.getImageData();\n\tconst { data } = imageData;\n\tfor (let i = 0; i < data.length; i += 4) {\n\t\tconst luminance = 255 - (0.2126 * data[i] + 0.7152 * data[i + 1] + 0.0722 * data[i + 2]);\n\t\tdata[i] = luminance;\n\t\tdata[i + 1] = luminance;\n\t\tdata[i + 2] = luminance;\n\t}\n\n\treturn canvas.putImageData(imageData, 0, 0);\n};\nexport const invertGreyscale = invertGrayscale;\n\n/**\n * Give an image a sepia tone\n * @param canvas The Canvas instance\n */\nexport const sepia = (canvas: Canvas): Canvas => {\n\tconst imageData = canvas.getImageData();\n\tconst { data } = imageData;\n\tfor (let i = 0; i < data.length; i += 4) {\n\t\tconst r = data[i];\n\t\tconst g = data[i + 1];\n\t\tconst b = data[i + 2];\n\t\tdata[i] = r * 0.393 + g * 0.769 + b * 0.189;\n\t\tdata[i + 1] = r * 0.349 + g * 0.686 + b * 0.168;\n\t\tdata[i + 2] = r * 0.272 + g * 0.534 + b * 0.131;\n\t}\n\treturn canvas.putImageData(imageData, 0, 0);\n};\n\n/**\n * Turn an image into a silhouette\n * @param canvas The Canvas instance\n */\nexport const silhouette = (canvas: Canvas): Canvas => {\n\tconst imageData = canvas.getImageData();\n\tconst { data } = imageData;\n\tfor (let i = 0; i < data.length; i += 4) {\n\t\tdata[i] = 0;\n\t\tdata[i + 1] = 0;\n\t\tdata[i + 2] = 0;\n\t}\n\n\treturn canvas.putImageData(imageData, 0, 0);\n};\n\n/**\n * Apply a threshold to the image\n * @param canvas The Canvas instance\n * @param threshold The threshold to apply in a range of 0 to 255\n */\nexport const threshold = (canvas: Canvas, threshold: number): Canvas => {\n\tconst imageData = canvas.getImageData();\n\tconst { data } = imageData;\n\tfor (let i = 0; i < data.length; i += 4) {\n\t\tconst luminance = 0.2126 * data[i] + 0.7152 * data[i + 1] + 0.0722 * data[i + 2] >= threshold ? 255 : 0;\n\t\tdata[i] = luminance;\n\t\tdata[i + 1] = luminance;\n\t\tdata[i + 2] = luminance;\n\t}\n\n\treturn canvas.putImageData(imageData, 0, 0);\n};\n\n/**\n * Apply an inverted threshold to the image\n * @param canvas The Canvas instance\n * @param threshold The threshold to apply in a range of 0 to 255\n */\nexport const invertedThreshold = (canvas: Canvas, threshold: number): Canvas => {\n\tconst imageData = canvas.getImageData();\n\tconst { data } = imageData;\n\tfor (let i = 0; i < data.length; i += 4) {\n\t\tconst luminance = 0.2126 * data[i] + 0.7152 * data[i + 1] + 0.0722 * data[i + 2] >= threshold ? 0 : 255;\n\t\tdata[i] = luminance;\n\t\tdata[i + 1] = luminance;\n\t\tdata[i + 2] = luminance;\n\t}\n\n\treturn canvas.putImageData(imageData, 0, 0);\n};\n\n/**\n * Brighten an image\n * @param canvas The Canvas instance\n * @param brightness The brightness to apply in a range of 0 to 255\n */\nexport const brightness = (canvas: Canvas, brightness: number): Canvas => {\n\tconst imageData = canvas.getImageData();\n\tconst { data } = imageData;\n\tfor (let i = 0; i < data.length; i += 4) {\n\t\tdata[i] += brightness;\n\t\tdata[i + 1] += brightness;\n\t\tdata[i + 2] += brightness;\n\t}\n\n\treturn canvas.putImageData(imageData, 0, 0);\n};\n\n/**\n * Darken an image\n * @param canvas The Canvas instance\n * @param darkness The darkness to apply in a range of 0 to 255\n */\nexport const darkness = (canvas: Canvas, darkness: number): Canvas => {\n\tconst imageData = canvas.getImageData();\n\tconst { data } = imageData;\n\tfor (let i = 0; i < data.length; i += 4) {\n\t\tdata[i] -= darkness;\n\t\tdata[i + 1] -= darkness;\n\t\tdata[i + 2] -= darkness;\n\t}\n\n\treturn canvas.putImageData(imageData, 0, 0);\n};\nexport const myOldFriend = darkness;\n\n/**\n * Convolute a image. This filter needs a fix.\n * @param canvas The Canvas instance\n * @param weights The weights\n * @param opaque Whether or not pixels should try to be opaque\n * @see https://www.html5rocks.com/en/tutorials/canvas/imagefilters/\n */\nexport const convolute = (canvas: Canvas, weights: readonly number[], opaque = true): Canvas => {\n\tconst side = Math.round(Math.sqrt(weights.length));\n\tconst halfSide = Math.floor(side / 2);\n\n\tconst pixels = canvas.getImageData();\n\tconst src = pixels.data;\n\tconst sw = pixels.width;\n\tconst sh = pixels.height;\n\n\t// pad output by the convolution matrix\n\tconst w = sw;\n\tconst h = sh;\n\tconst output = canvas.getImageData();\n\tconst dst = output.data;\n\n\t// go through the destination image pixels\n\tconst alphaFac = opaque ? 1 : 0;\n\tfor (let y = 0; y < h; y++) {\n\t\tfor (let x = 0; x < w; x++) {\n\t\t\tconst sy = y;\n\t\t\tconst sx = x;\n\t\t\tconst dstOff = (y * w + x) * 4;\n\t\t\t// calculate the weighed sum of the source image pixels that\n\t\t\t// fall under the convolution matrix\n\t\t\tlet r = 0;\n\t\t\tlet g = 0;\n\t\t\tlet b = 0;\n\t\t\tlet a = 0;\n\t\t\tfor (let cy = 0; cy < side; cy++) {\n\t\t\t\tfor (let cx = 0; cx < side; cx++) {\n\t\t\t\t\tconst scy = sy + cy - halfSide;\n\t\t\t\t\tconst scx = sx + cx - halfSide;\n\t\t\t\t\tif (scy >= 0 && scy < sh && scx >= 0 && scx < sw) {\n\t\t\t\t\t\tconst srcOff = (scy * sw + scx) * 4;\n\t\t\t\t\t\tconst wt = weights[cy * side + cx];\n\t\t\t\t\t\tr += src[srcOff] * wt;\n\t\t\t\t\t\tg += src[srcOff + 1] * wt;\n\t\t\t\t\t\tb += src[srcOff + 2] * wt;\n\t\t\t\t\t\ta += src[srcOff + 3] * wt;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tdst[dstOff] = r;\n\t\t\tdst[dstOff + 1] = g;\n\t\t\tdst[dstOff + 2] = b;\n\t\t\tdst[dstOff + 3] = a + alphaFac * (255 - a);\n\t\t}\n\t}\n\n\treturn canvas.putImageData(output, 0, 0);\n};\n\n/**\n * The LaPlace matrix for edge\n * @internal\n */\nconst edgeLaPlaceMatrix = [0, -1, 0, -1, 4, -1, 0, -1, 0];\n\n/**\n * Display an image's edges\n * @param canvas The Canvas instance\n */\nexport const edge = (canvas: Canvas): Canvas => convolute(canvas, edgeLaPlaceMatrix, true);\n\n/**\n * The LaPlace matrix for sharpen\n * @internal\n */\nconst sharpenLaPlaceMatrix = [0, -1, 0, -1, 5, -1, 0, -1, 0];\n\n/**\n * Sharpen an image\n * @param canvas The Canvas instance\n * @param passes The amount of iterations to do\n */\nexport const sharpen = (canvas: Canvas, passes = 1): Canvas => {\n\tfor (let i = 0; i < passes; ++i) {\n\t\tconvolute(canvas, sharpenLaPlaceMatrix, true);\n\t}\n\n\treturn canvas;\n};\n\n/**\n * The LaPlace matrix for blur\n * @internal\n */\nconst blurLaPlaceMatrix = [1 / 9, 1 / 9, 1 / 9, 1 / 9, 1 / 9, 1 / 9, 1 / 9, 1 / 9, 1 / 9];\n\n/**\n * Blur an image\n * @param canvas The Canvas instance\n * @param passes The amount of iterations to do\n */\nexport const blur = (canvas: Canvas, passes = 1): Canvas => {\n\tfor (let i = 0; i < passes; ++i) {\n\t\tconvolute(canvas, blurLaPlaceMatrix, true);\n\t}\n\n\treturn canvas;\n};\n// End Section: Filters\n// Start Section: Util\nexport const fontRegExp = /([\\d.]+)(px|pt|pc|in|cm|mm|%|em|ex|ch|rem|q)/i;\nexport const getFontHeight = (() => {\n\tconst kCache = new Map<string, number>();\n\n\treturn (font: string): number => {\n\t\t// If it was already parsed, do not parse again\n\t\tconst previous = kCache.get(font);\n\t\tif (previous) return previous;\n\n\t\t// Test for required properties first, return null if the text is invalid\n\t\tconst sizeFamily = fontRegExp.exec(font);\n\t\tif (!sizeFamily) return 0;\n\n\t\tlet size = Number(sizeFamily[1]);\n\t\tconst unit = sizeFamily[2];\n\n\t\tswitch (unit) {\n\t\t\tcase 'pt':\n\t\t\t\tsize /= 0.75;\n\t\t\t\tbreak;\n\t\t\tcase 'pc':\n\t\t\t\tsize *= 16;\n\t\t\t\tbreak;\n\t\t\tcase 'in':\n\t\t\t\tsize *= 96;\n\t\t\t\tbreak;\n\t\t\tcase 'cm':\n\t\t\t\tsize *= 96.0 / 2.54;\n\t\t\t\tbreak;\n\t\t\tcase 'mm':\n\t\t\t\tsize *= 96.0 / 25.4;\n\t\t\t\tbreak;\n\t\t\tcase 'em':\n\t\t\tcase 'rem':\n\t\t\t\tsize *= 16 / 0.75;\n\t\t\t\tbreak;\n\t\t\tcase 'q':\n\t\t\t\tsize *= 96 / 25.4 / 4;\n\t\t\t\tbreak;\n\t\t}\n\n\t\tkCache.set(font, size);\n\t\treturn size;\n\t};\n})();\n\nexport const textWrap = (canvas: Canvas, text: string, wrapWidth: number): string => {\n\tconst result = [];\n\tconst buffer = [];\n\n\tconst spaceWidth = canvas.measureText(' ').width;\n\n\t// Run the loop for each line\n\tfor (const line of text.split(/\\r?\\n/)) {\n\t\tlet spaceLeft = wrapWidth;\n\n\t\t// Run the loop for each word\n\t\tfor (const word of line.split(' ')) {\n\t\t\tconst wordWidth = canvas.measureText(word).width;\n\t\t\t// eslint-disable-next-line @typescript-eslint/restrict-plus-operands\n\t\t\tconst wordWidthWithSpace = wordWidth + spaceWidth;\n\n\t\t\tif (wordWidthWithSpace > spaceLeft) {\n\t\t\t\tif (buffer.length) {\n\t\t\t\t\tresult.push(buffer.join(' '));\n\t\t\t\t\tbuffer.length = 0;\n\t\t\t\t}\n\t\t\t\tbuffer.push(word);\n\t\t\t\tspaceLeft = wrapWidth - wordWidth;\n\t\t\t} else {\n\t\t\t\tspaceLeft -= wordWidthWithSpace;\n\t\t\t\tbuffer.push(word);\n\t\t\t}\n\t\t}\n\n\t\tif (buffer.length) {\n\t\t\tresult.push(buffer.join(' '));\n\t\t\tbuffer.length = 0;\n\t\t}\n\t}\n\n\treturn result.join('\\n');\n};\n\n/**\n * The names of the filters that take a string argument.\n */\ntype LiteralFilters = 'url';\n\nexport type Percentage<T extends number = number> = `${T}%`;\n\n/**\n * The names of the filters that take a percentage argument.\n */\ntype PercentageFilters = 'brightness' | 'contrast' | 'grayscale' | 'invert' | 'opacity' | 'saturate' | 'sepia';\n\ntype RelativeLengthUnits = 'cap' | 'ch' | 'em' | 'ex' | 'ic' | 'lh' | 'rem' | 'rlh';\ntype RelativeUnits = RelativeLengthUnits | '%';\ntype ViewportPercentageUnits = 'vh' | 'vw' | 'vi' | 'vb' | 'vmin' | 'vmax';\ntype AbsoluteLengthUnits = 'px' | 'cm' | 'mm' | 'Q' | 'in' | 'pc' | 'pt';\ntype LengthUnits = RelativeUnits | ViewportPercentageUnits | AbsoluteLengthUnits;\nexport type Length<T extends number = number> = `${T}${LengthUnits}`;\n\n/**\n * The names of the filters that take a length argument.\n */\ntype LengthFilters = 'blur';\n\ntype AngleUnits = 'deg' | 'grad' | 'rad' | 'turn';\nexport type Angle<T extends number = number> = `${T}${AngleUnits}`;\n\n/**\n * The names of the filters that take an angle argument.\n */\ntype AngleFilters = 'hue-rotate';\n\nexport type Color = ColorKeyword | ColorHexadecimal | ColorRGB | ColorRGBA | ColorHSL | ColorHSLA;\n\ninterface Filter {\n\t<K extends LiteralFilters, V extends string>(name: K, url: V): `${K}(${V})`;\n\t<K extends PercentageFilters, V extends Percentage>(name: K, percentage: V): `${K}(${V})`;\n\t<K extends LengthFilters, V extends Length>(name: K, length: V): `${K}(${V})`;\n\t<K extends AngleFilters, V extends Angle>(name: K, angle: V): `${K}(${V})`;\n\t<Vx extends Length, Vy extends Length>(name: 'drop-shadow', x: Vx, y: Vy): `drop-shadow(${Vx} ${Vy})`;\n\t<Vx extends Length, Vy extends Length, Vb extends Length>(name: 'drop-shadow', x: Vx, y: Vy, blur: Vb): `drop-shadow(${Vx} ${Vy} ${Vb})`;\n\t<Vx extends Length, Vy extends Length, Vc extends Color>(name: 'drop-shadow', x: Vx, y: Vy, color: Vc): `drop-shadow(${Vx} ${Vy} ${Vc})`;\n\t<Vx extends Length, Vy extends Length, Vb extends Length, Vc extends Color>(\n\t\tname: 'drop-shadow',\n\t\tx: Vx,\n\t\ty: Vy,\n\t\tblur: Vb,\n\t\tcolor: Vc\n\t): `drop-shadow(${Vx} ${Vy} ${Vb} ${Vc})`;\n\t(value: 'none'): 'none';\n}\n\n// @ts-expect-error: Overload hell\nexport const filter: Filter = (name: string, ...args: readonly any[]) => `${name}(${args.join(' ')})` as const;\n\n/**\n * Represents a formatted hexadecimal value.\n */\nexport type ColorHexadecimal<T extends string = string> = `#${T}`;\n\n/**\n * Utility to format an hexadecimal string into a CSS hexadecimal string.\n * @param hex The hexadecimal code.\n * @example\n * hex('FFF'); // -> '#FFF'\n * hex('0F0F0F'); // -> '#0F0F0F'\n */\nexport const hex = <T extends string>(hex: T): ColorHexadecimal<T> => `#${hex}` as const;\n\n/**\n * Represents a formatted RGB value.\n */\nexport type ColorRGB<R extends number = number, G extends number = number, B extends number = number> = `rgb(${R}, ${G}, ${B})`;\n\n/**\n * Utility to format a RGB set of values into a string.\n * @param red The red value, must be a number between 0 and 255 inclusive.\n * @param green The green value, must be a number between 0 and 255 inclusive.\n * @param blue The blue value, must be a number between 0 and 255 inclusive.\n * @see https://en.wikipedia.org/wiki/RGB_color_model#Geometric_representation\n * @example\n * rgb(255, 150, 65); // -> 'rgb(255, 150, 65)'\n */\nexport const rgb = <R extends number, G extends number, B extends number>(red: R, green: G, blue: B): ColorRGB<R, G, B> =>\n\t`rgb(${red}, ${green}, ${blue})` as const;\n\n/**\n * Represents a formatted RGBA value.\n */\nexport type ColorRGBA<\n\tR extends number = number,\n\tG extends number = number,\n\tB extends number = number,\n\tA extends number = number\n> = `rgba(${R}, ${G}, ${B}, ${A})`;\n\n/**\n * Utility to format a RGBA set of values into a string.\n * @param red The red value, must be a number between 0 and 255 inclusive.\n * @param green The green value, must be a number between 0 and 255 inclusive.\n * @param blue The blue value, must be a number between 0 and 255 inclusive.\n * @param alpha The alpha value, must be a number between 0 and 1 inclusive.\n * @see https://en.wikipedia.org/wiki/RGB_color_model#Geometric_representation\n * @example\n * rgba(255, 150, 65, 0.3); // -> 'rgba(255, 150, 65, 0.3)'\n */\nexport const rgba = <R extends number, G extends number, B extends number, A extends number>(\n\tred: R,\n\tgreen: G,\n\tblue: B,\n\talpha: A\n): ColorRGBA<R, G, B, A> => `rgba(${red}, ${green}, ${blue}, ${alpha})` as const;\n\n/**\n * Represents a formatted HSL value.\n */\nexport type ColorHSL<H extends number = number, S extends number = number, L extends number = number> = `hsl(${H}, ${S}%, ${L}%)`;\n\n/**\n * Utility to format a HSL set of values into a string.\n * @param hue The hue, must be a number between 0 and 360 inclusive.\n * @param saturation The saturation, must be a number between 0 and 100 inclusive.\n * @param lightness The lightness, must be a number between 0 and 100 inclusive, 0 will make it black, 100 will make it white.\n * @see https://en.wikipedia.org/wiki/HSL_and_HSV\n * @example\n * hsl(120, 100, 40); // -> 'hsl(120, 100, 40)'\n */\nexport const hsl = <H extends number, S extends number, L extends number>(hue: H, saturation: S, lightness: L): ColorHSL<H, S, L> =>\n\t`hsl(${hue}, ${saturation}%, ${lightness}%)` as const;\n\n/**\n * Represents a formatted HSL value.\n */\nexport type ColorHSLA<\n\tH extends number = number,\n\tS extends number = number,\n\tL extends number = number,\n\tA extends number = number\n> = `hsla(${H}, ${S}%, ${L}%, ${A})`;\n\n/**\n * Utility to format a HSLA set of values into a string.\n * @param hue The hue, must be a number between 0 and 360 inclusive.\n * @param saturation The saturation, must be a number between 0 and 100 inclusive.\n * @param lightness The lightness, must be a number between 0 and 100 inclusive, 0 will make it black, 100 will make it white\n * @param alpha The alpha value, must be a number between 0 and 1 inclusive.\n * @see https://en.wikipedia.org/wiki/HSL_and_HSV\n * @example\n * hsla(120, 100, 40, 0.4); // -> 'hsla(120, 100, 40, 0.4)'\n */\nexport const hsla = <H extends number, S extends number, L extends number, A extends number>(\n\thue: H,\n\tsaturation: S,\n\tlightness: L,\n\talpha: A\n): ColorHSLA<H, S, L, A> => `hsla(${hue}, ${saturation}%, ${lightness}%, ${alpha})` as const;\n\n/**\n * Utility to type-safely use CSS colors.\n * @param color The CSS keyword color.\n * @example\n * color('silver'); // ✔\n * color('some-imaginary-number'); // ❌\n */\nexport const color = (color: ColorKeyword): ColorKeyword => color;\n\nexport type ColorKeyword = ColorKeywordLevel1 | ColorKeywordLevel2 | ColorKeywordLevel3 | ColorKeywordLevel4;\n\nexport type ColorKeywordLevel1 =\n\t| 'black'\n\t| 'silver'\n\t| 'gray'\n\t| 'white'\n\t| 'maroon'\n\t| 'red'\n\t| 'purple'\n\t| 'fuchsia'\n\t| 'green'\n\t| 'lime'\n\t| 'olive'\n\t| 'yellow'\n\t| 'navy'\n\t| 'blue'\n\t| 'teal'\n\t| 'aqua';\n\nexport type ColorKeywordLevel2 = 'orange';\n\nexport type ColorKeywordLevel3 =\n\t| 'aliceblue'\n\t| 'antiquewhite'\n\t| 'aquamarine'\n\t| 'azure'\n\t| 'beige'\n\t| 'bisque'\n\t| 'blanchedalmond'\n\t| 'blueviolet'\n\t| 'brown'\n\t| 'burlywood'\n\t| 'cadetblue'\n\t| 'chartreuse'\n\t| 'chocolate'\n\t| 'coral'\n\t| 'cornflowerblue'\n\t| 'cornsilk'\n\t| 'crimson'\n\t| 'cyan'\n\t| 'darkblue'\n\t| 'darkcyan'\n\t| 'darkgoldenrod'\n\t| 'darkgray'\n\t| 'darkgreen'\n\t| 'darkgrey'\n\t| 'darkkhaki'\n\t| 'darkmagenta'\n\t| 'darkolivegreen'\n\t| 'darkorange'\n\t| 'darkorchid'\n\t| 'darkred'\n\t| 'darksalmon'\n\t| 'darkseagreen'\n\t| 'darkslateblue'\n\t| 'darkslategray'\n\t| 'darkslategrey'\n\t| 'darkturquoise'\n\t| 'darkviolet'\n\t| 'deeppink'\n\t| 'deepskyblue'\n\t| 'dimgray'\n\t| 'dimgrey'\n\t| 'dodgerblue'\n\t| 'firebrick'\n\t| 'floralwhite'\n\t| 'forestgreen'\n\t| 'gainsboro'\n\t| 'ghostwhite'\n\t| 'gold'\n\t| 'goldenrod'\n\t| 'greenyellow'\n\t| 'grey'\n\t| 'honeydew'\n\t| 'hotpink'\n\t| 'indianred'\n\t| 'indigo'\n\t| 'ivory'\n\t| 'khaki'\n\t| 'lavender'\n\t| 'lavenderblush'\n\t| 'lawngreen'\n\t| 'lemonchiffon'\n\t| 'lightblue'\n\t| 'lightcoral'\n\t| 'lightcyan'\n\t| 'lightgoldenrodyellow'\n\t| 'lightgray'\n\t| 'lightgreen'\n\t| 'lightgrey'\n\t| 'lightpink'\n\t| 'lightsalmon'\n\t| 'lightseagreen'\n\t| 'lightskyblue'\n\t| 'lightslategray'\n\t| 'lightslategrey'\n\t| 'lightsteelblue'\n\t| 'lightyellow'\n\t| 'limegreen'\n\t| 'linen'\n\t| 'magenta'\n\t| 'mediumaquamarine'\n\t| 'mediumblue'\n\t| 'mediumorchid'\n\t| 'mediumpurple'\n\t| 'mediumseagreen'\n\t| 'mediumslateblue'\n\t| 'mediumspringgreen'\n\t| 'mediumturquoise'\n\t| 'mediumvioletred'\n\t| 'midnightblue'\n\t| 'mintcream'\n\t| 'mistyrose'\n\t| 'moccasin'\n\t| 'navajowhite'\n\t| 'oldlace'\n\t| 'olivedrab'\n\t| 'orangered'\n\t| 'orchid'\n\t| 'palegoldenrod'\n\t| 'palegreen'\n\t| 'paleturquoise'\n\t| 'palevioletred'\n\t| 'papayawhip'\n\t| 'peachpuff'\n\t| 'peru'\n\t| 'pink'\n\t| 'plum'\n\t| 'powderblue'\n\t| 'rosybrown'\n\t| 'royalblue'\n\t| 'saddlebrown'\n\t| 'salmon'\n\t| 'sandybrown'\n\t| 'seagreen'\n\t| 'seashell'\n\t| 'sienna'\n\t| 'skyblue'\n\t| 'slateblue'\n\t| 'slategray'\n\t| 'slategrey'\n\t| 'snow'\n\t| 'springgreen'\n\t| 'steelblue'\n\t| 'tan'\n\t| 'thistle'\n\t| 'tomato'\n\t| 'turquoise'\n\t| 'violet'\n\t| 'wheat'\n\t| 'whitesmoke'\n\t| 'yellowgreen';\n\nexport type ColorKeywordLevel4 = 'rebeccapurple';\n// End Section: Util\n"], "names": ["<PERSON><PERSON>", "constructor", "width", "height", "this", "canvas", "NativeCanvas", "context", "getContext", "addPage", "newPage", "value", "direction", "font", "globalAlpha", "imageSmoothingEnabled", "imageSmoothingQuality", "pages", "fontVariant", "textTracking", "textWrap", "transform", "getTransform", "textFontHeight", "getFontHeight", "lineDash", "getLineDash", "changeCanvasSize", "changeCanvasWidth", "changeCanvasHeight", "save", "restore", "rotate", "angle", "scale", "x", "y", "translate", "clip", "args", "setTransform", "resetTransform", "resetFilters", "setFilter", "getImageData", "putImageData", "fill", "printText", "text", "rest", "fillText", "printResponsiveText", "max<PERSON><PERSON><PERSON>", "tail", "lead", "parseFont", "measureText", "newHeight", "setTextFont", "printMultilineText", "lines", "split", "length", "linePositionY", "line", "Math", "floor", "printWrappedText", "wrapWidth", "wrappedText", "stroke", "printStrokeRectangle", "strokeRect", "printStrokeText", "strokeText", "setTextSize", "size", "result", "setStroke", "color", "strokeStyle", "setLineWidth", "lineWidth", "setStrokeWidth", "setLineDashOffset", "lineDashOffset", "setLineJoin", "lineJoin", "setLineCap", "lineCap", "setLineDash", "segments", "printImage", "image", "resolvable", "drawImage", "printCircularImage", "imageOrBuffer", "radius", "fit", "positionX", "positionY", "sizeX", "sizeY", "resolveCircularCoordinates", "createCircularClip", "PI", "printRoundedImage", "createRoundedClip", "printCircle", "createCircularPath", "printRectangle", "fillRect", "printRoundedRectangle", "createRoundedPath", "dx", "dy", "start", "antiClockwise", "beginPath", "arc", "createRectanglePath", "rect", "createRectangleClip", "radiusObject", "tl", "min", "tr", "br", "bl", "moveTo", "lineTo", "quadraticCurveTo", "closePath", "setColor", "fillStyle", "setTextAlign", "align", "textAlign", "setTextBaseline", "baseline", "textBaseline", "filter", "createPattern", "repetition", "printPattern", "createLinearGradient", "x0", "y0", "x1", "y1", "steps", "gradient", "step", "addColorStop", "position", "printLinearColorGradient", "printLinearStrokeGradient", "createRadialGradient", "r0", "r1", "printRadialColorGradient", "printRadialStrokeGradient", "createConicGradient", "startAngle", "printConicColorGradient", "printConicStrokeGradient", "createEllipsePath", "radiusX", "radiusY", "rotation", "endAngle", "anticlockwise", "ellipse", "createEllipseClip", "arcTo", "x2", "y2", "cpx", "cpy", "bezierCurveTo", "cp1x", "cp1y", "cp2x", "cp2y", "setShadowBlur", "<PERSON><PERSON><PERSON><PERSON>", "setShadowColor", "shadowColor", "setShadowOffsetX", "shadowOffsetX", "setShadowOffsetY", "shadowOffsetY", "setMiterLimit", "miterLimit", "setGlobalCompositeOperation", "type", "globalCompositeOperation", "setGlobalAlpha", "setImageSmoothingEnabled", "setImageSmoothingQuality", "setFontVariant", "setTextTracking", "setTextWrap", "resetShadows", "clearCircle", "clearRectangle", "clearRect", "isPointInPath", "isPointInStroke", "process", "fn", "call", "wrapText", "createImageData", "jpeg", "options", "toBufferSync", "jpegAsync", "<PERSON><PERSON><PERSON><PERSON>", "png", "pngAsync", "pdf", "pdfAsync", "svg", "svgAsync", "to<PERSON><PERSON><PERSON><PERSON><PERSON>", "toDataURL", "saveAs", "saveAsSync", "async", "fontRegExp", "exec", "slice", "index", "Number", "w", "h", "ratio", "diameter", "resolveImage", "deprecate", "loadImage", "loadFont", "FontLibrary", "use", "registerFont", "greyscale", "imageData", "data", "i", "luminance", "grayscale", "invertGrayscale", "invertGreyscale", "darkness", "myOldFriend", "convolute", "weights", "opaque", "side", "round", "sqrt", "halfSide", "pixels", "src", "sw", "sh", "output", "dst", "alphaFac", "sy", "sx", "dstOff", "r", "g", "b", "a", "cy", "cx", "scy", "scx", "srcOff", "wt", "edgeLaPlaceMatrix", "sharpenLaPlaceMatrix", "blurLaPlaceMatrix", "kCache", "Map", "previous", "get", "sizeFamily", "set", "buffer", "spaceWidth", "spaceLeft", "word", "wordWidth", "wordWidthWithSpace", "push", "join", "passes", "brightness", "name", "hex", "hue", "saturation", "lightness", "alpha", "threshold", "red", "green", "blue"], "mappings": "uEAwEaA,EAaZC,YAAmBC,EAA8BC,GAC3B,iBAAVD,GACVE,KAAKC,OAAS,IAAIC,EAAYN,OAACE,EAAOC,GACtCC,KAAKG,QAAUH,KAAKC,OAAOG,WAAW,QAEtCJ,KAAKC,OAASH,EACdE,KAAKG,QAAWJ,QAAAA,EAAUC,KAAKC,OAAOG,WAAW,MAElD,CASMC,QAAQP,EAAeC,GAC7B,OAAO,IAAIH,EAAOI,KAAKC,OAAQD,KAAKC,OAAOK,QAAQR,EAAOC,GAC1D,CAKUD,YACV,OAAOE,KAAKC,OAAOH,KACnB,CAEUA,UAAMS,GAChBP,KAAKC,OAAOH,MAAQS,CACpB,CAKUR,aACV,OAAOC,KAAKC,OAAOF,MACnB,CAEUA,WAAOQ,GACjBP,KAAKC,OAAOF,OAASQ,CACrB,CAMUC,gBACV,OAAOR,KAAKG,QAAQK,SACpB,CAOUC,WACV,OAAOT,KAAKG,QAAQM,IACpB,CAMUC,kBACV,OAAOV,KAAKG,QAAQO,WACpB,CAMUC,4BACV,OAAOX,KAAKG,QAAQQ,qBACpB,CAMUC,4BACV,OAAOZ,KAAKG,QAAQS,qBACpB,CAMUC,YACV,OAAOb,KAAKC,OAAOY,KACnB,CAOUC,kBACV,OAAOd,KAAKG,QAAQW,WACpB,CAOUC,mBACV,OAAOf,KAAKG,QAAQY,YACpB,CAOUC,eACV,OAAOhB,KAAKG,QAAQa,QACpB,CAMUC,gBACV,OAAOjB,KAAKG,QAAQe,cACpB,CAKUC,qBACV,OAAOC,EAAcpB,KAAKG,QAAQM,KAClC,CAgBUY,eACV,OAAOrB,KAAKG,QAAQmB,aACpB,CAOMC,iBAAiBzB,EAAeC,GACtC,OAAOC,KAAKwB,kBAAkB1B,GAAO2B,mBAAmB1B,EACxD,CAMMyB,kBAAkB1B,GAExB,OADAE,KAAKF,MAAQA,EACNE,IACP,CAMMyB,mBAAmB1B,GAEzB,OADAC,KAAKD,OAASA,EACPC,IACP,CAMM0B,OAEN,OADA1B,KAAKG,QAAQuB,OACN1B,IACP,CAOM2B,UAEN,OADA3B,KAAKG,QAAQwB,UACN3B,IACP,CAQM4B,OAAOC,GAEb,OADA7B,KAAKG,QAAQyB,OAAOC,GACb7B,IACP,CAQM8B,MAAMC,EAAWC,GAEvB,OADAhC,KAAKG,QAAQ2B,MAAMC,EAAGC,GACfhC,IACP,CAQMiC,UAAUF,EAAWC,GAE3B,OADAhC,KAAKG,QAAQ8B,UAAUF,EAAGC,GACnBhC,IACP,CAeMkC,QAAQC,GAEd,OADAnC,KAAKG,QAAQ+B,QAAQC,GACdnC,IACP,CAqBMoC,gBAAgBD,GAEtB,OADAnC,KAAKG,QAAQiC,gBAAgBD,GACtBnC,IACP,CAMMqC,iBAEN,OADArC,KAAKG,QAAQkC,iBACNrC,IACP,CAKMsC,eACN,OAAOtC,KAAKuC,UAAU,OACtB,CAkBMC,aAAaT,EAAYC,EAAYlC,EAAgBC,GAC3D,OAAOC,KAAKG,QAAQqC,aAAaT,QAAAA,EAAK,EAAGC,QAAAA,EAAK,EAAGlC,QAAAA,EAASE,KAAKF,MAAOC,QAAAA,EAAUC,KAAKD,OACrF,CA0BM0C,gBAAgBN,GAEtB,OADAnC,KAAKG,QAAQsC,gBAAgBN,GACtBnC,IACP,CAeM0C,QAAQP,GAEd,OADAnC,KAAKG,QAAQuC,QAAQP,GACdnC,IACP,CAYM2C,UAAUC,EAAcb,EAAWC,KAAca,GAEvD,OADA7C,KAAKG,QAAQ2C,SAASF,EAAMb,EAAGC,KAAMa,GAC9B7C,IACP,CAcM+C,oBAAoBH,EAAcb,EAAWC,EAAWgB,GAC9D,MAAOC,EAAMlD,EAAQmD,GAAQlD,KAAKmD,UAAUnD,KAAKG,QAAQM,MACzD,GAAsB,iBAAXV,EAAqB,OAAOC,KAAK2C,UAAUC,EAAMb,EAAGC,GAG/D,MAAMlC,MAAEA,GAAUE,KAAKoD,YAAYR,GACnC,GAAI9C,GAASkD,EAAU,OAAOhD,KAAK2C,UAAUC,EAAMb,EAAGC,GAGtD,MAAMqB,EAAaL,EAAWlD,EAASC,EACvC,OAAOC,KAAK0B,OAAO4B,YAAY,GAAGL,IAAOI,IAAYH,KAAQP,UAAUC,EAAMb,EAAGC,GAAGL,SACnF,CAaM4B,mBAAmBX,EAAcb,EAAWC,GAClD,MAAMwB,EAAQZ,EAAKa,MAAM,SAGzB,GAAID,EAAME,QAAU,EAAG,OAAO1D,KAAK2C,UAAUC,EAAMb,EAAGC,GAEtD,MAAMjC,EAASC,KAAKmB,eAEpB,IAAIwC,EAAgB3B,EACpB,IAAK,MAAM4B,KAAQJ,EAClBxD,KAAK2C,UAAUiB,EAAM7B,EAAG8B,KAAKC,MAAMH,IACnCA,GAAiB5D,EAGlB,OAAOC,IACP,CAcM+D,iBAAiBnB,EAAcb,EAAWC,EAAWgC,GAC3D,MAAMC,EAAcjD,EAAShB,KAAM4C,EAAMoB,GACzC,OAAOhE,KAAKuD,mBAAmBU,EAAalC,EAAGC,EAC/C,CAMMkC,SAEN,OADAlE,KAAKG,QAAQ+D,SACNlE,IACP,CAWMmE,qBAAqBpC,EAAWC,EAAWlC,EAAeC,GAEhE,OADAC,KAAKG,QAAQiE,WAAWrC,EAAGC,EAAGlC,EAAOC,GAC9BC,IACP,CAWMqE,gBAAgBzB,EAAcb,EAAWC,EAAWgB,GAE1D,OADAhD,KAAKG,QAAQmE,WAAW1B,EAAMb,EAAGC,EAAGgB,GAC7BhD,IACP,CA4BMoD,YAAYR,GAClB,OAAO5C,KAAKG,QAAQiD,YAAYR,EAChC,CAMM2B,YAAYC,GAClB,MAAMC,EAASzE,KAAKmD,UAAUnD,KAAKG,QAAQM,MAC3C,OAAyB,IAAlBgE,EAAOf,OAAe1D,KAAOA,KAAKsD,YAAY,GAAGmB,EAAO,KAAKD,IAAOC,EAAO,KAClF,CAOMC,UAAUC,GAEhB,OADA3E,KAAKG,QAAQyE,YAAcD,EACpB3E,IACP,CAOM6E,aAAa/E,GAEnB,OADAE,KAAKG,QAAQ2E,UAAYhF,EAClBE,IACP,CAEM+E,eAAejF,GACrB,OAAOE,KAAK6E,aAAa/E,EACzB,CAOMkF,kBAAkBzE,GAExB,OADAP,KAAKG,QAAQ8E,eAAiB1E,EACvBP,IACP,CASMkF,YAAY3E,GAElB,OADAP,KAAKG,QAAQgF,SAAW5E,EACjBP,IACP,CAQMoF,WAAW7E,GAEjB,OADAP,KAAKG,QAAQkF,QAAU9E,EAChBP,IACP,CAWMsF,YAAYC,GAElB,OADAvF,KAAKG,QAAQmF,YAAYC,GAClBvF,IACP,CAkCMwF,WAAWC,KAA2BtD,GA9oB9C,IAAuBuD,EAgpBrB,OADA1F,KAAKG,QAAQwF,WA/oBQD,EA+oBgBD,aA9oBT7F,EAAS8F,EAAWzF,OAASyF,KA8oBTvD,GACzCnC,IACP,CAYM4F,mBACNC,EACA9D,EACAC,EACA8D,GACAC,IAAEA,EAAM,QAAiC,IAEzC,MAAMC,UAAEA,EAASC,UAAEA,EAASC,MAAEA,EAAKC,MAAEA,GAAUnG,KAAKoG,2BAA2BP,EAAe9D,EAAGC,EAAG8D,EAAQC,GAC5G,OAAO/F,KAAK0B,OACV2E,mBAAmBtE,EAAGC,EAAG8D,EAAQ,EAAa,EAAVjC,KAAKyC,IAAQ,GACjDd,WAAWK,EAAeG,EAAWC,EAAWC,EAAOC,GACvDxE,SACF,CAoBM4E,kBACNV,EACA9D,EACAC,EACAlC,EACAC,EACA+F,GAEA,OAAO9F,KAAK0B,OAAO8E,kBAAkBzE,EAAGC,EAAGlC,EAAOC,EAAQ+F,GAAQN,WAAWK,EAAe9D,EAAGC,EAAGlC,EAAOC,GAAQ4B,SACjH,CAQM8E,YAAY1E,EAAWC,EAAW8D,GACxC,OAAO9F,KAAK0B,OAAOgF,mBAAmB3E,EAAGC,EAAG8D,GAAQpD,OAAOf,SAC3D,CAUMgF,eAAe5E,EAAWC,EAAWlC,EAAeC,GAE1D,OADAC,KAAKG,QAAQyG,SAAS7E,EAAGC,EAAGlC,EAAOC,GAC5BC,IACP,CAqCM6G,sBAAsB9E,EAAWC,EAAWlC,EAAeC,EAAgB+F,GACjF,OAAO9F,KAAK0B,OAAOoF,kBAAkB/E,EAAGC,EAAGlC,EAAOC,EAAQ+F,GAAQpD,OAAOf,SACzE,CAWM+E,mBAAmBK,EAAYC,EAAYlB,EAAgBmB,EAAQ,EAAGpF,EAAkB,EAAVgC,KAAKyC,GAAQY,GAAgB,GAGjH,OAFAlH,KAAKG,QAAQgH,YACbnH,KAAKG,QAAQiH,IAAIL,EAAIC,EAAIlB,EAAQmB,EAAOpF,EAAOqF,GACxClH,IACP,CAYMqG,mBAAmBU,EAAYC,EAAYlB,EAAgBmB,EAAgBpF,EAAgBqF,GACjG,OAAOlH,KAAK0G,mBAAmBK,EAAIC,EAAIlB,EAAQmB,EAAOpF,EAAOqF,GAAehF,MAC5E,CASMmF,oBAAoBtF,EAAWC,EAAWlC,EAAeC,GAE/D,OADAC,KAAKG,QAAQmH,KAAKvF,EAAGC,EAAGlC,EAAOC,GACxBC,IACP,CASMuH,oBAAoBxF,EAAWC,EAAWlC,EAAeC,GAC/D,OAAOC,KAAKqH,oBAAoBtF,EAAGC,EAAGlC,EAAOC,GAAQmC,MACrD,CAUM4E,kBAAkB/E,EAAWC,EAAWlC,EAAeC,EAAgB+F,GAC7E,GAAIhG,EAAQ,GAAKC,EAAS,EAAG,CAC5B,IAAIyH,EACkB,iBAAX1B,EAEV0B,EAAe,CAAEC,GADjB3B,EAASjC,KAAK6D,IAAI5B,EAAQhG,EAAQ,EAAGC,EAAS,GACjB4H,GAAI7B,EAAQ8B,GAAI9B,EAAQ+B,GAAI/B,IAEzD0B,EAAe1B,EACfA,EAASjC,KAAK6D,IAAI,EAAG5H,EAAQ,EAAGC,EAAS,IAE1C,MAAM0H,GAAEA,EAAK3B,EAAM6B,GAAEA,EAAK7B,EAAM8B,GAAEA,EAAK9B,EAAM+B,GAAEA,EAAK/B,GAAW0B,EAC/DxH,KAAKG,QAAQgH,YACbnH,KAAKG,QAAQ2H,OAAO/F,EAAI0F,EAAIzF,GAC5BhC,KAAKG,QAAQ4H,OAAOhG,EAAIjC,EAAQ6H,EAAI3F,GACpChC,KAAKG,QAAQ6H,iBAAiBjG,EAAIjC,EAAOkC,EAAGD,EAAIjC,EAAOkC,EAAI2F,GAC3D3H,KAAKG,QAAQ4H,OAAOhG,EAAIjC,EAAOkC,EAAIjC,EAAS6H,GAC5C5H,KAAKG,QAAQ6H,iBAAiBjG,EAAIjC,EAAOkC,EAAIjC,EAAQgC,EAAIjC,EAAQ8H,EAAI5F,EAAIjC,GACzEC,KAAKG,QAAQ4H,OAAOhG,EAAI8F,EAAI7F,EAAIjC,GAChCC,KAAKG,QAAQ6H,iBAAiBjG,EAAGC,EAAIjC,EAAQgC,EAAGC,EAAIjC,EAAS8H,GAC7D7H,KAAKG,QAAQ4H,OAAOhG,EAAGC,EAAIyF,GAC3BzH,KAAKG,QAAQ6H,iBAAiBjG,EAAGC,EAAGD,EAAI0F,EAAIzF,GAC5ChC,KAAKG,QAAQ8H,WACb,CACD,OAAOjI,IACP,CAwCMwG,kBAAkBzE,EAAWC,EAAWlC,EAAeC,EAAgB+F,GAC7E,OAAO9F,KAAK8G,kBAAkB/E,EAAGC,EAAGlC,EAAOC,EAAQ+F,GAAQ5D,MAC3D,CAOMgG,SAASvD,GAEf,OADA3E,KAAKG,QAAQgI,UAAYxD,EAClB3E,IACP,CAOMsD,YAAY7C,GAElB,OADAT,KAAKG,QAAQM,KAAOA,EACbT,IACP,CAOMoI,aAAaC,GAEnB,OADArI,KAAKG,QAAQmI,UAAYD,EAClBrI,IACP,CAOMuI,gBAAgBC,GAEtB,OADAxI,KAAKG,QAAQsI,aAAeD,EACrBxI,IACP,CAOMuC,UAAUmG,GAEhB,OADA1I,KAAKG,QAAQuI,OAASA,EACf1I,IACP,CAMMmH,YAEN,OADAnH,KAAKG,QAAQgH,YACNnH,IACP,CAOMiI,YAEN,OADAjI,KAAKG,QAAQ8H,YACNjI,IACP,CASM2I,cAAclD,EAA0BmD,GAC9C,OAAO5I,KAAKG,QAAQwI,eA98BGjD,EA88B2BD,aA78BzB7F,EAAe8F,EAAWzF,OAC7CyF,EA48BoDkD,GA98B5D,IAAyBlD,CA+8BvB,CASMmD,aAAapD,EAA0BmD,GAC7C,OAAO5I,KAAKkI,SAASlI,KAAK2I,cAAclD,EAAOmD,GAC/C,CAYME,qBAAqBC,EAAYC,EAAYC,EAAYC,EAAYC,EAAiC,IAC5G,MAAMC,EAAWpJ,KAAKG,QAAQ2I,qBAAqBC,EAAIC,EAAIC,EAAIC,GAC/D,IAAK,MAAMG,KAAQF,EAClBC,EAASE,aAAaD,EAAKE,SAAUF,EAAK1E,OAG3C,OAAOyE,CACP,CAqBMI,yBAAyBT,EAAYC,EAAYC,EAAYC,EAAYC,GAC/E,MAAMC,EAAWpJ,KAAK8I,qBAAqBC,EAAIC,EAAIC,EAAIC,EAAIC,GAC3D,OAAOnJ,KAAKkI,SAASkB,EACrB,CAqBMK,0BAA0BV,EAAYC,EAAYC,EAAYC,EAAYC,GAChF,MAAMC,EAAWpJ,KAAK8I,qBAAqBC,EAAIC,EAAIC,EAAIC,EAAIC,GAC3D,OAAOnJ,KAAK0E,UAAU0E,EACtB,CAaMM,qBACNX,EACAC,EACAW,EACAV,EACAC,EACAU,EACAT,EAAiC,IAEjC,MAAMC,EAAWpJ,KAAKG,QAAQuJ,qBAAqBX,EAAIC,EAAIW,EAAIV,EAAIC,EAAIU,GACvE,IAAK,MAAMP,KAAQF,EAClBC,EAASE,aAAaD,EAAKE,SAAUF,EAAK1E,OAG3C,OAAOyE,CACP,CAcMS,yBAAyBd,EAAYC,EAAYW,EAAYV,EAAYC,EAAYU,EAAYT,GACvG,MAAMC,EAAWpJ,KAAK0J,qBAAqBX,EAAIC,EAAIW,EAAIV,EAAIC,EAAIU,EAAIT,GACnE,OAAOnJ,KAAKkI,SAASkB,EACrB,CAcMU,0BAA0Bf,EAAYC,EAAYW,EAAYV,EAAYC,EAAYU,EAAYT,GACxG,MAAMC,EAAWpJ,KAAK0J,qBAAqBX,EAAIC,EAAIW,EAAIV,EAAIC,EAAIU,EAAIT,GACnE,OAAOnJ,KAAK0E,UAAU0E,EACtB,CAWMW,oBAAoBC,EAAoBjI,EAAWC,EAAWmH,EAAiC,IACrG,MAAMC,EAAWpJ,KAAKG,QAAQ4J,oBAAoBC,EAAYjI,EAAGC,GACjE,IAAK,MAAMqH,KAAQF,EAClBC,EAASE,aAAaD,EAAKE,SAAUF,EAAK1E,OAG3C,OAAOyE,CACP,CAYMa,wBAAwBD,EAAoBjI,EAAWC,EAAWmH,GACxE,MAAMC,EAAWpJ,KAAK+J,oBAAoBC,EAAYjI,EAAGC,EAAGmH,GAC5D,OAAOnJ,KAAKkI,SAASkB,EACrB,CAYMc,yBAAyBF,EAAoBjI,EAAWC,EAAWmH,GACzE,MAAMC,EAAWpJ,KAAK+J,oBAAoBC,EAAYjI,EAAGC,EAAGmH,GAC5D,OAAOnJ,KAAK0E,UAAU0E,EACtB,CAeMe,kBACNpI,EACAC,EACAoI,EACAC,EACAC,EACAN,EACAO,EACAC,GAGA,OADAxK,KAAKG,QAAQsK,QAAQ1I,EAAGC,EAAGoI,EAASC,EAASC,EAAUN,EAAYO,EAAUC,GACtExK,IACP,CAeM0K,kBACN3I,EACAC,EACAoI,EACAC,EACAC,EACAN,EACAO,EACAC,GAEA,OAAOxK,KAAKmK,kBAAkBpI,EAAGC,EAAGoI,EAASC,EAASC,EAAUN,EAAYO,EAAUC,GAAetI,MACrG,CAaMkF,IAAIrF,EAAWC,EAAW8D,EAAgBkE,EAAoBO,EAAkBC,GAEtF,OADAxK,KAAKG,QAAQiH,IAAIrF,EAAGC,EAAG8D,EAAQkE,EAAYO,EAAUC,GAC9CxK,IACP,CAWM2K,MAAM1B,EAAYC,EAAY0B,EAAYC,EAAY/E,GAE5D,OADA9F,KAAKG,QAAQwK,MAAM1B,EAAIC,EAAI0B,EAAIC,EAAI/E,GAC5B9F,IACP,CAYMgI,iBAAiB8C,EAAaC,EAAahJ,EAAWC,GAE5D,OADAhC,KAAKG,QAAQ6H,iBAAiB8C,EAAKC,EAAKhJ,EAAGC,GACpChC,IACP,CAcMgL,cAAcC,EAAcC,EAAcC,EAAcC,EAAcrJ,EAAWC,GAEvF,OADAhC,KAAKG,QAAQ6K,cAAcC,EAAMC,EAAMC,EAAMC,EAAMrJ,EAAGC,GAC/ChC,IACP,CAQM+H,OAAOhG,EAAWC,GAExB,OADAhC,KAAKG,QAAQ4H,OAAOhG,EAAGC,GAChBhC,IACP,CAQM8H,OAAO/F,EAAWC,GAExB,OADAhC,KAAKG,QAAQ2H,OAAO/F,EAAGC,GAChBhC,IACP,CAOMqL,cAAcvF,GAEpB,OADA9F,KAAKG,QAAQmL,WAAaxF,EACnB9F,IACP,CAOMuL,eAAe5G,GAErB,OADA3E,KAAKG,QAAQqL,YAAc7G,EACpB3E,IACP,CAOMyL,iBAAiBlL,GAEvB,OADAP,KAAKG,QAAQuL,cAAgBnL,EACtBP,IACP,CAOM2L,iBAAiBpL,GAEvB,OADAP,KAAKG,QAAQyL,cAAgBrL,EACtBP,IACP,CASM6L,cAActL,GAEpB,OADAP,KAAKG,QAAQ2L,WAAavL,EACnBP,IACP,CAQM+L,4BAA4BC,GAElC,OADAhM,KAAKG,QAAQ8L,yBAA2BD,EACjChM,IACP,CAOMkM,eAAe3L,GAErB,OADAP,KAAKG,QAAQO,YAAcH,EACpBP,IACP,CAOMmM,yBAAyB5L,GAE/B,OADAP,KAAKG,QAAQQ,sBAAwBJ,EAC9BP,IACP,CAOMoM,yBAAyB7L,GAE/B,OADAP,KAAKG,QAAQS,sBAAwBL,EAC9BP,IACP,CASMqM,eAAevL,GAErB,OADAd,KAAKG,QAAQW,YAAcA,EACpBd,IACP,CAWMsM,gBAAgBvL,GAEtB,OADAf,KAAKG,QAAQY,aAAeA,EACrBf,IACP,CAeMuM,YAAYhM,GAElB,OADAP,KAAKG,QAAQa,SAAWT,EACjBP,IACP,CAkBMwM,eACN,OAAOxM,KAAKqL,cAAc,GAAGI,iBAAiB,GAAGE,iBAAiB,GAAGJ,eAAe,cACpF,CAWMkB,YAAY1K,EAAWC,EAAW8D,EAAgBmB,EAAQ,EAAGpF,EAAkB,EAAVgC,KAAKyC,GAAQY,GAAgB,GACxG,OAAOlH,KAAKqG,mBAAmBtE,EAAGC,EAAG8D,EAAQmB,EAAOpF,EAAOqF,GAAewF,eAAe3K,EAAI+D,EAAQ9D,EAAI8D,EAAiB,EAATA,EAAqB,EAATA,EAC7H,CAUM4G,eAAe3F,EAAK,EAAGC,EAAK,EAAGlH,EAAQE,KAAKF,MAAOC,EAASC,KAAKD,QAEvE,OADAC,KAAKG,QAAQwM,UAAU5F,EAAIC,EAAIlH,EAAOC,GAC/BC,IACP,CAmBM4M,iBAAiBzK,GACvB,OAAOnC,KAAKG,QAAQyM,iBAAiBzK,EACrC,CAiBM0K,mBAAmB1K,GACzB,OAAOnC,KAAKG,QAAQ0M,mBAAmB1K,EACvC,CAOM2K,QAAqCC,KAA6D5K,GAExG,OADA4K,EAAGC,KAAKhN,KAAMA,QAASmC,GAChBnC,IACP,CAyBMiN,SAASrK,EAAcoB,GAC7B,OAAOhD,EAAShB,KAAM4C,EAAMoB,EAC5B,CAcMkJ,mBAAmB/K,GACzB,OAAOnC,KAAKG,QAAQ+M,mBAAmB/K,EACvC,CAQMgL,KAAKC,GACX,OAAOpN,KAAKC,OAAOoN,aAAa,OAAQD,EACxC,CAOME,UAAUF,GAChB,OAAOpN,KAAKC,OAAOsN,SAAS,OAAQH,EACpC,CAQMI,IAAIJ,GACV,OAAOpN,KAAKC,OAAOoN,aAAa,MAAOD,EACvC,CAQMK,SAASL,GACf,OAAOpN,KAAKC,OAAOsN,SAAS,MAAOH,EACnC,CAQMM,IAAIN,GACV,OAAOpN,KAAKC,OAAOoN,aAAa,MAAOD,EACvC,CAOMO,SAASP,GACf,OAAOpN,KAAKC,OAAOsN,SAAS,MAAOH,EACnC,CAQMQ,IAAIR,GACV,OAAOpN,KAAKC,OAAOoN,aAAa,MAAOD,EACvC,CAOMS,SAAST,GACf,OAAOpN,KAAKC,OAAOsN,SAAS,MAAOH,EACnC,CASMG,YAAYpL,GAClB,OAAOnC,KAAKC,OAAOoN,gBAAgBlL,EACnC,CASM2L,iBAAiB3L,GACvB,OAAOnC,KAAKC,OAAOsN,YAAYpL,EAC/B,CASM4L,aAAa5L,GACnB,OAAOnC,KAAKC,OAAO8N,aAAa5L,EAChC,CAiBM6L,UAAU7L,GAGhB,OADAnC,KAAKC,OAAOgO,cAAc9L,GACnBnC,IACP,CAiBMkO,qBAAqB/L,GAG3B,aADMnC,KAAKC,OAAO+N,UAAU7L,GACrBnC,IACP,CAESmD,UAAU1C,GACnB,MAAMgE,EAAS0J,EAAWC,KAAK3N,GAC/B,OAAe,OAAXgE,EAAwB,CAAChE,GAEtB,CAACA,EAAK4N,MAAM,EAAG5J,EAAO6J,OAAQC,OAAO9J,EAAO,IAAKhE,EAAK4N,MAAM5J,EAAO6J,MAAQ7J,EAAO,GAAGf,QAC5F,CAES0C,2BACTP,EACA9D,EACAC,EACA8D,EACAC,GAEA,MAAQjG,MAAO0O,EAAGzO,OAAQ0O,GAAM5I,EAChC,GAAY,SAARE,EACH,MAAO,CACNC,UAAWjE,EAAIyM,EAAI,EACnBvI,UAAWjE,EAAIyM,EAAI,EACnBvI,MAAOsI,EACPrI,MAAOsI,GAIT,MAAMC,EAAQF,EAAIC,EACZE,EAAoB,EAAT7I,EAEjB,GAAY,SAARC,GAA4B,IAAV2I,EACrB,MAAO,CACN1I,UAAWjE,EAAI+D,EACfG,UAAWjE,EAAI8D,EACfI,MAAOyI,EACPxI,MAAOwI,GAIT,GAAY,YAAR5I,EACH,OAAO2I,EAAQ,EACZ,CACA1I,UAAWjE,EAAI+D,EACfG,UAAWjE,EAAI8D,EAAS4I,EACxBxI,MAAOyI,EACPxI,MAAOwI,EAAWD,GAElB,CACA1I,UAAWjE,EAAI+D,EAAS4I,EACxBzI,UAAWjE,EAAI8D,EACfI,MAAOyI,EAAWD,EAClBvI,MAAOwI,GAIX,GAAID,EAAQ,EAAG,CACd,MAAMxI,EAAQyI,EAAWD,EAEzB,MAAO,CACN1I,UAAWjE,EAAImE,EAAQ,EACvBD,UAAWjE,EAHE2M,EAGU,EACvBzI,QACAC,MALawI,EAOd,CAED,MACMxI,EAAQwI,EAAWD,EACzB,MAAO,CACN1I,UAAWjE,EAHE4M,EAGU,EACvB1I,UAAWjE,EAAImE,EAAQ,EACvBD,MALayI,EAMbxI,QAED,EAYW,MAAAyI,EAAeC,EAAAA,UAAUC,EAASA,UAAE,0DAKjC,SAAAC,KAAY5M,GAC3B,OAAO6M,cAAYC,OAAO9M,EAC3B,CAEa,MAAA+M,EAAeL,EAAAA,UAAUE,EAAU,yDAsGnC,MAOAI,EAAalP,IACzB,MAAMmP,EAAYnP,EAAOuC,gBACnB6M,KAAEA,GAASD,EACjB,IAAK,IAAIE,EAAI,EAAGA,EAAID,EAAK3L,OAAQ4L,GAAK,EAAG,CACxC,MAAMC,EAAY,MAASF,EAAKC,GAAK,MAASD,EAAKC,EAAI,GAAK,MAASD,EAAKC,EAAI,GAC9ED,EAAKC,GAAKC,EACVF,EAAKC,EAAI,GAAKC,EACdF,EAAKC,EAAI,GAAKC,CACd,CAED,OAAOtP,EAAOwC,aAAa2M,EAAW,EAAG,EAAE,EAE/BI,EAAYL,EAMZM,EAAmBxP,IAC/B,MAAMmP,EAAYnP,EAAOuC,gBACnB6M,KAAEA,GAASD,EACjB,IAAK,IAAIE,EAAI,EAAGA,EAAID,EAAK3L,OAAQ4L,GAAK,EAAG,CACxC,MAAMC,EAAY,KAAO,MAASF,EAAKC,GAAK,MAASD,EAAKC,EAAI,GAAK,MAASD,EAAKC,EAAI,IACrFD,EAAKC,GAAKC,EACVF,EAAKC,EAAI,GAAKC,EACdF,EAAKC,EAAI,GAAKC,CACd,CAED,OAAOtP,EAAOwC,aAAa2M,EAAW,EAAG,EAAE,EAE/BM,EAAkBD,EA8FlBE,EAAW,CAAC1P,EAAgB0P,KACxC,MAAMP,EAAYnP,EAAOuC,gBACnB6M,KAAEA,GAASD,EACjB,IAAK,IAAIE,EAAI,EAAGA,EAAID,EAAK3L,OAAQ4L,GAAK,EACrCD,EAAKC,IAAMK,EACXN,EAAKC,EAAI,IAAMK,EACfN,EAAKC,EAAI,IAAMK,EAGhB,OAAO1P,EAAOwC,aAAa2M,EAAW,EAAG,EAAE,EAE/BQ,EAAcD,EASdE,EAAY,CAAC5P,EAAgB6P,EAA4BC,GAAS,KAC9E,MAAMC,EAAOnM,KAAKoM,MAAMpM,KAAKqM,KAAKJ,EAAQpM,SACpCyM,EAAWtM,KAAKC,MAAMkM,EAAO,GAE7BI,EAASnQ,EAAOuC,eAChB6N,EAAMD,EAAOf,KACbiB,EAAKF,EAAOtQ,MACZyQ,EAAKH,EAAOrQ,OAGZyO,EAAI8B,EACJ7B,EAAI8B,EACJC,EAASvQ,EAAOuC,eAChBiO,EAAMD,EAAOnB,KAGbqB,EAAWX,EAAS,EAAI,EAC9B,IAAK,IAAI/N,EAAI,EAAGA,EAAIyM,EAAGzM,IACtB,IAAK,IAAID,EAAI,EAAGA,EAAIyM,EAAGzM,IAAK,CAC3B,MAAM4O,EAAK3O,EACL4O,EAAK7O,EACL8O,EAAuB,GAAb7O,EAAIwM,EAAIzM,GAGxB,IAAI+O,EAAI,EACJC,EAAI,EACJC,EAAI,EACJC,EAAI,EACR,IAAK,IAAIC,EAAK,EAAGA,EAAKlB,EAAMkB,IAC3B,IAAK,IAAIC,EAAK,EAAGA,EAAKnB,EAAMmB,IAAM,CACjC,MAAMC,EAAMT,EAAKO,EAAKf,EAChBkB,EAAMT,EAAKO,EAAKhB,EACtB,GAAIiB,GAAO,GAAKA,EAAMb,GAAMc,GAAO,GAAKA,EAAMf,EAAI,CACjD,MAAMgB,EAA4B,GAAlBF,EAAMd,EAAKe,GACrBE,EAAKzB,EAAQoB,EAAKlB,EAAOmB,GAC/BL,GAAKT,EAAIiB,GAAUC,EACnBR,GAAKV,EAAIiB,EAAS,GAAKC,EACvBP,GAAKX,EAAIiB,EAAS,GAAKC,EACvBN,GAAKZ,EAAIiB,EAAS,GAAKC,CACvB,CACD,CAEFd,EAAII,GAAUC,EACdL,EAAII,EAAS,GAAKE,EAClBN,EAAII,EAAS,GAAKG,EAClBP,EAAII,EAAS,GAAKI,EAAIP,GAAY,IAAMO,EACxC,CAGF,OAAOhR,EAAOwC,aAAa+N,EAAQ,EAAG,EAAE,EAOnCgB,EAAoB,CAAC,GAAI,EAAG,GAAI,EAAG,GAAI,EAAG,GAAI,EAAG,GAYjDC,EAAuB,CAAC,GAAI,EAAG,GAAI,EAAG,GAAI,EAAG,GAAI,EAAG,GAmBpDC,EAAoB,CAAC,EAAI,EAAG,EAAI,EAAG,EAAI,EAAG,EAAI,EAAG,EAAI,EAAG,EAAI,EAAG,EAAI,EAAG,EAAI,EAAG,EAAI,GAgB1EvD,EAAa,gDACb/M,EAAgB,MAC5B,MAAMuQ,EAAS,IAAIC,IAEnB,OAAQnR,IAEP,MAAMoR,EAAWF,EAAOG,IAAIrR,GAC5B,GAAIoR,EAAU,OAAOA,EAGrB,MAAME,EAAa5D,EAAWC,KAAK3N,GACnC,IAAKsR,EAAY,OAAO,EAExB,IAAIvN,EAAO+J,OAAOwD,EAAW,IAG7B,OAFaA,EAAW,IAGvB,IAAK,KACJvN,GAAQ,IACR,MACD,IAAK,KACJA,GAAQ,GACR,MACD,IAAK,KACJA,GAAQ,GACR,MACD,IAAK,KACJA,GAAQ,GAAO,KACf,MACD,IAAK,KACJA,GAAQ,GAAO,KACf,MACD,IAAK,KACL,IAAK,MACJA,GAAQ,GAAK,IACb,MACD,IAAK,IACJA,GAAQ,GAAK,KAAO,EAKtB,OADAmN,EAAOK,IAAIvR,EAAM+D,GACVA,CAAI,CAEZ,EA3C4B,GA6ChBxD,EAAW,CAACf,EAAgB2C,EAAcoB,KACtD,MAAMS,EAAS,GACTwN,EAAS,GAETC,EAAajS,EAAOmD,YAAY,KAAKtD,MAG3C,IAAK,MAAM8D,KAAQhB,EAAKa,MAAM,SAAU,CACvC,IAAI0O,EAAYnO,EAGhB,IAAK,MAAMoO,KAAQxO,EAAKH,MAAM,KAAM,CACnC,MAAM4O,EAAYpS,EAAOmD,YAAYgP,GAAMtS,MAErCwS,EAAqBD,EAAYH,EAEnCI,EAAqBH,GACpBF,EAAOvO,SACVe,EAAO8N,KAAKN,EAAOO,KAAK,MACxBP,EAAOvO,OAAS,GAEjBuO,EAAOM,KAAKH,GACZD,EAAYnO,EAAYqO,IAExBF,GAAaG,EACbL,EAAOM,KAAKH,GAEb,CAEGH,EAAOvO,SACVe,EAAO8N,KAAKN,EAAOO,KAAK,MACxBP,EAAOvO,OAAS,EAEjB,CAED,OAAOe,EAAO+N,KAAK,KAAK,8YA1FL,CAACvS,EAAgBwS,EAAS,KAC7C,IAAK,IAAInD,EAAI,EAAGA,EAAImD,IAAUnD,EAC7BO,EAAU5P,EAAQyR,GAAmB,GAGtC,OAAOzR,CAAM,qBAxIY,CAACA,EAAgByS,KAC1C,MAAMtD,EAAYnP,EAAOuC,gBACnB6M,KAAEA,GAASD,EACjB,IAAK,IAAIE,EAAI,EAAGA,EAAID,EAAK3L,OAAQ4L,GAAK,EACrCD,EAAKC,IAAMoD,EACXrD,EAAKC,EAAI,IAAMoD,EACfrD,EAAKC,EAAI,IAAMoD,EAGhB,OAAOzS,EAAOwC,aAAa2M,EAAW,EAAG,EAAE,gBA2XtBzK,GAAsCA,sDAjSvC1E,GAA2B4P,EAAU5P,EAAQuR,GAAmB,kBAkLvD,CAACmB,KAAiBxQ,IAAyB,GAAGwQ,KAAQxQ,EAAKqQ,KAAK,iDAhZ9E,YAAerQ,GAC9B,OAAOA,EAAKqQ,KAAK,IAClB,8EA4ZsCI,GAAgC,IAAIA,gBA4DvD,CAAuDC,EAAQC,EAAeC,IAChG,OAAOF,MAAQC,OAAgBC,mBAsBZ,CACnBF,EACAC,EACAC,EACAC,IAC2B,QAAQH,MAAQC,OAAgBC,OAAeC,oBA7epD/S,GACtBA,EAAOyB,OAAOqK,4BAA4B,cAAc7D,SAAS,SAASvB,eAAe,EAAG,EAAG1G,EAAOH,MAAOG,EAAOF,QAAQ4B,wFA+F5F,CAAC1B,EAAgBgT,KACjD,MAAM7D,EAAYnP,EAAOuC,gBACnB6M,KAAEA,GAASD,EACjB,IAAK,IAAIE,EAAI,EAAGA,EAAID,EAAK3L,OAAQ4L,GAAK,EAAG,CACxC,MAAMC,EAAY,MAASF,EAAKC,GAAK,MAASD,EAAKC,EAAI,GAAK,MAASD,EAAKC,EAAI,IAAM2D,EAAY,EAAI,IACpG5D,EAAKC,GAAKC,EACVF,EAAKC,EAAI,GAAKC,EACdF,EAAKC,EAAI,GAAKC,CACd,CAED,OAAOtP,EAAOwC,aAAa2M,EAAW,EAAG,EAAE,qGA2TzB,CAAuD8D,EAAQC,EAAUC,IAC3F,OAAOF,MAAQC,MAAUC,kBAsBN,CACnBF,EACAC,EACAC,EACAJ,IAC2B,QAAQE,MAAQC,MAAUC,MAASJ,mBAtZzC/S,IACrB,MAAMmP,EAAYnP,EAAOuC,gBACnB6M,KAAEA,GAASD,EACjB,IAAK,IAAIE,EAAI,EAAGA,EAAID,EAAK3L,OAAQ4L,GAAK,EAAG,CACxC,MAAMwB,EAAIzB,EAAKC,GACTyB,EAAI1B,EAAKC,EAAI,GACb0B,EAAI3B,EAAKC,EAAI,GACnBD,EAAKC,GAAS,KAAJwB,EAAgB,KAAJC,EAAgB,KAAJC,EAClC3B,EAAKC,EAAI,GAAS,KAAJwB,EAAgB,KAAJC,EAAgB,KAAJC,EACtC3B,EAAKC,EAAI,GAAS,KAAJwB,EAAgB,KAAJC,EAAgB,KAAJC,CACtC,CACD,OAAO/Q,EAAOwC,aAAa2M,EAAW,EAAG,EAAE,kBA4KrB,CAACnP,EAAgBwS,EAAS,KAChD,IAAK,IAAInD,EAAI,EAAGA,EAAImD,IAAUnD,EAC7BO,EAAU5P,EAAQwR,GAAsB,GAGzC,OAAOxR,CAAM,qBA1KaA,IAC1B,MAAMmP,EAAYnP,EAAOuC,gBACnB6M,KAAEA,GAASD,EACjB,IAAK,IAAIE,EAAI,EAAGA,EAAID,EAAK3L,OAAQ4L,GAAK,EACrCD,EAAKC,GAAK,EACVD,EAAKC,EAAI,GAAK,EACdD,EAAKC,EAAI,GAAK,EAGf,OAAOrP,EAAOwC,aAAa2M,EAAW,EAAG,EAAE,uCAQnB,CAACnP,EAAgBgT,KACzC,MAAM7D,EAAYnP,EAAOuC,gBACnB6M,KAAEA,GAASD,EACjB,IAAK,IAAIE,EAAI,EAAGA,EAAID,EAAK3L,OAAQ4L,GAAK,EAAG,CACxC,MAAMC,EAAY,MAASF,EAAKC,GAAK,MAASD,EAAKC,EAAI,GAAK,MAASD,EAAKC,EAAI,IAAM2D,EAAY,IAAM,EACtG5D,EAAKC,GAAKC,EACVF,EAAKC,EAAI,GAAKC,EACdF,EAAKC,EAAI,GAAKC,CACd,CAED,OAAOtP,EAAOwC,aAAa2M,EAAW,EAAG,EAAE"}